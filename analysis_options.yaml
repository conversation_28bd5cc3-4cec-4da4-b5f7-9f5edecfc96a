include: package:very_good_analysis/analysis_options.yaml

linter:
  rules:
    # 自定义规则
    prefer_single_quotes: true
    require_trailing_commas: true
    sort_constructors_first: true
    sort_unnamed_constructors_first: true

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.config.dart"
    - "**/generated/**"

  errors:
    invalid_annotation_target: ignore
    missing_required_param: error
    missing_return: error
