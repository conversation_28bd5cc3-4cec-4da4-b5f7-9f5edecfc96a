# 开发环境配置
# 继承基础配置并覆盖特定设置

# 功能模块配置 - 开发环境
features:
  # 核心功能模块
  authentication: true
  authorization: true
  
  # UI/UX 模块
  internationalization: true
  theming: true
  
  # 高级功能模块
  analytics: true
  performance_monitoring: true
  push_notifications: false
  
  # 开发工具 - 开发环境启用
  dev_tools: true
  debug_features: true

# 开发环境特定配置
api:
  base_url: "https://dev-api.example.com"
  timeout: 30
  enable_logging: true

database:
  name: "app_dev.db"
  enable_logging: true

# 日志配置
logging:
  level: "debug"
  enable_console: true
  enable_file: true
