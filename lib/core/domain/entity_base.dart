import 'package:equatable/equatable.dart';

/// 业务实体基础类
/// 
/// 所有业务实体都应该继承此类
/// 实体是业务的核心对象，包含业务逻辑和规则
abstract class EntityBase extends Equatable {
  const EntityBase();
  
  /// 实体的唯一标识符
  String get id;
  
  /// 创建时间
  DateTime? get createdAt => null;
  
  /// 更新时间
  DateTime? get updatedAt => null;
  
  /// 验证实体是否有效
  bool isValid() => id.isNotEmpty;
  
  /// 复制方法（子类需要实现）
  EntityBase copyWith();
  
  @override
  List<Object?> get props => [id, createdAt, updatedAt];
  
  @override
  String toString() => '$runtimeType(id: $id)';
}

/// 聚合根基础类
/// 
/// 聚合根是聚合的入口点，负责维护聚合的一致性
abstract class AggregateRootBase extends EntityBase {
  const AggregateRootBase();
  
  /// 领域事件列表
  List<DomainEvent> get domainEvents => _domainEvents;
  final List<DomainEvent> _domainEvents = [];
  
  /// 添加领域事件
  void addDomainEvent(DomainEvent event) {
    _domainEvents.add(event);
  }
  
  /// 清除领域事件
  void clearDomainEvents() {
    _domainEvents.clear();
  }
  
  /// 获取未提交的事件
  List<DomainEvent> getUncommittedEvents() {
    return List.unmodifiable(_domainEvents);
  }
}

/// 值对象基础类
/// 
/// 值对象是不可变的，通过值来标识而不是通过ID
abstract class ValueObjectBase extends Equatable {
  const ValueObjectBase();
  
  /// 验证值对象是否有效
  bool isValid();
  
  /// 获取值对象的字符串表示
  String get value;
  
  @override
  String toString() => value;
}

/// 领域事件基础类
/// 
/// 领域事件表示业务中发生的重要事件
abstract class DomainEvent extends Equatable {
  const DomainEvent({
    required this.aggregateId,
    DateTime? occurredOn,
  }) : occurredOn = occurredOn ?? DateTime.now();
  
  /// 聚合根ID
  final String aggregateId;
  
  /// 事件发生时间
  final DateTime occurredOn;
  
  /// 事件类型
  String get eventType => runtimeType.toString();
  
  @override
  List<Object?> get props => [aggregateId, occurredOn, eventType];
}

/// 领域服务基础接口
/// 
/// 领域服务包含不属于任何实体或值对象的业务逻辑
abstract class DomainServiceBase {
  const DomainServiceBase();
}

/// 规约模式基础类
/// 
/// 用于封装业务规则和验证逻辑
abstract class SpecificationBase<T> {
  const SpecificationBase();
  
  /// 检查对象是否满足规约
  bool isSatisfiedBy(T candidate);
  
  /// 与操作
  SpecificationBase<T> and(SpecificationBase<T> other) {
    return AndSpecification(this, other);
  }
  
  /// 或操作
  SpecificationBase<T> or(SpecificationBase<T> other) {
    return OrSpecification(this, other);
  }
  
  /// 非操作
  SpecificationBase<T> not() {
    return NotSpecification(this);
  }
}

/// 与规约
class AndSpecification<T> extends SpecificationBase<T> {
  const AndSpecification(this.left, this.right);
  
  final SpecificationBase<T> left;
  final SpecificationBase<T> right;
  
  @override
  bool isSatisfiedBy(T candidate) {
    return left.isSatisfiedBy(candidate) && right.isSatisfiedBy(candidate);
  }
}

/// 或规约
class OrSpecification<T> extends SpecificationBase<T> {
  const OrSpecification(this.left, this.right);
  
  final SpecificationBase<T> left;
  final SpecificationBase<T> right;
  
  @override
  bool isSatisfiedBy(T candidate) {
    return left.isSatisfiedBy(candidate) || right.isSatisfiedBy(candidate);
  }
}

/// 非规约
class NotSpecification<T> extends SpecificationBase<T> {
  const NotSpecification(this.specification);
  
  final SpecificationBase<T> specification;
  
  @override
  bool isSatisfiedBy(T candidate) {
    return !specification.isSatisfiedBy(candidate);
  }
}

/// 实体工厂基础接口
/// 
/// 用于创建复杂的实体对象
abstract class EntityFactoryBase<T extends EntityBase> {
  /// 创建实体
  T create(Map<String, dynamic> data);
  
  /// 重建实体（从持久化数据）
  T reconstruct(Map<String, dynamic> data);
  
  /// 验证创建数据
  bool validateCreationData(Map<String, dynamic> data);
}
