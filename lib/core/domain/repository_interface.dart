import 'package:dartz/dartz.dart';
import '../errors/failures.dart';
import 'entity_base.dart';

/// Repository接口规范
/// 
/// 定义了Repository的标准接口，遵循依赖倒置原则
/// 领域层定义接口，数据层实现接口
abstract class RepositoryInterface<T extends EntityBase> {
  /// 根据ID获取实体
  Future<Either<Failure, T>> findById(String id);
  
  /// 获取所有实体
  Future<Either<Failure, List<T>>> findAll();
  
  /// 保存实体
  Future<Either<Failure, T>> save(T entity);
  
  /// 删除实体
  Future<Either<Failure, void>> delete(String id);
  
  /// 检查实体是否存在
  Future<Either<Failure, bool>> exists(String id);
}

/// 查询Repository接口
/// 
/// 提供复杂查询功能
abstract class QueryRepositoryInterface<T extends EntityBase> 
    extends RepositoryInterface<T> {
  
  /// 根据条件查询
  Future<Either<Failure, List<T>>> findWhere(
    Map<String, dynamic> conditions,
  );
  
  /// 分页查询
  Future<Either<Failure, PagedResult<T>>> findPaged({
    int page = 1,
    int size = 20,
    Map<String, dynamic>? filters,
    List<SortCriteria>? sorts,
  });
  
  /// 计数查询
  Future<Either<Failure, int>> count([Map<String, dynamic>? conditions]);
  
  /// 搜索查询
  Future<Either<Failure, List<T>>> search(String query);
}

/// 规约Repository接口
/// 
/// 支持规约模式的查询
abstract class SpecificationRepositoryInterface<T extends EntityBase> 
    extends RepositoryInterface<T> {
  
  /// 根据规约查询
  Future<Either<Failure, List<T>>> findBySpecification(
    SpecificationBase<T> specification,
  );
  
  /// 根据规约查询单个实体
  Future<Either<Failure, T?>> findOneBySpecification(
    SpecificationBase<T> specification,
  );
  
  /// 根据规约计数
  Future<Either<Failure, int>> countBySpecification(
    SpecificationBase<T> specification,
  );
}

/// 事件Repository接口
/// 
/// 支持领域事件的Repository
abstract class EventRepositoryInterface<T extends AggregateRootBase> 
    extends RepositoryInterface<T> {
  
  /// 保存聚合根及其事件
  Future<Either<Failure, T>> saveWithEvents(T aggregateRoot);
  
  /// 获取未发布的事件
  Future<Either<Failure, List<DomainEvent>>> getUnpublishedEvents();
  
  /// 标记事件为已发布
  Future<Either<Failure, void>> markEventsAsPublished(
    List<DomainEvent> events,
  );
}

/// 缓存Repository接口
/// 
/// 支持缓存的Repository
abstract class CacheRepositoryInterface<T extends EntityBase> 
    extends RepositoryInterface<T> {
  
  /// 从缓存获取
  Future<Either<Failure, T?>> getFromCache(String id);
  
  /// 保存到缓存
  Future<Either<Failure, void>> saveToCache(T entity);
  
  /// 从缓存删除
  Future<Either<Failure, void>> removeFromCache(String id);
  
  /// 清空缓存
  Future<Either<Failure, void>> clearCache();
  
  /// 刷新缓存
  Future<Either<Failure, T>> refreshCache(String id);
}

/// 只读Repository接口
/// 
/// 只提供查询功能的Repository
abstract class ReadOnlyRepositoryInterface<T extends EntityBase> {
  /// 根据ID获取实体
  Future<Either<Failure, T>> findById(String id);
  
  /// 获取所有实体
  Future<Either<Failure, List<T>>> findAll();
  
  /// 根据条件查询
  Future<Either<Failure, List<T>>> findWhere(
    Map<String, dynamic> conditions,
  );
  
  /// 检查实体是否存在
  Future<Either<Failure, bool>> exists(String id);
  
  /// 计数查询
  Future<Either<Failure, int>> count([Map<String, dynamic>? conditions]);
}

/// 分页结果
class PagedResult<T> {
  const PagedResult({
    required this.data,
    required this.totalCount,
    required this.page,
    required this.size,
  });
  
  final List<T> data;
  final int totalCount;
  final int page;
  final int size;
  
  int get totalPages => (totalCount / size).ceil();
  bool get hasNextPage => page < totalPages;
  bool get hasPreviousPage => page > 1;
}

/// 排序条件
class SortCriteria {
  const SortCriteria({
    required this.field,
    this.direction = SortDirection.asc,
  });
  
  final String field;
  final SortDirection direction;
}

/// 排序方向
enum SortDirection { asc, desc }

/// Repository工厂接口
/// 
/// 用于创建Repository实例
abstract class RepositoryFactory {
  /// 获取Repository实例
  T getRepository<T extends RepositoryInterface>();
  
  /// 注册Repository实现
  void registerRepository<T extends RepositoryInterface>(T repository);
  
  /// 检查Repository是否已注册
  bool hasRepository<T extends RepositoryInterface>();
}
