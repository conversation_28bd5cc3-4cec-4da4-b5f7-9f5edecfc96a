import 'package:dartz/dartz.dart';
import '../errors/failures.dart';

/// Repository基础接口
/// 
/// 定义了所有Repository的通用操作和错误处理模式
/// 使用Either类型进行函数式错误处理
abstract class RepositoryBase<T> {
  /// 获取单个实体
  Future<Either<Failure, T>> getById(String id);
  
  /// 获取所有实体
  Future<Either<Failure, List<T>>> getAll();
  
  /// 创建实体
  Future<Either<Failure, T>> create(T entity);
  
  /// 更新实体
  Future<Either<Failure, T>> update(T entity);
  
  /// 删除实体
  Future<Either<Failure, void>> delete(String id);
  
  /// 检查实体是否存在
  Future<Either<Failure, bool>> exists(String id);
}

/// 分页Repository基础接口
abstract class PaginatedRepositoryBase<T> extends RepositoryBase<T> {
  /// 分页获取实体列表
  Future<Either<Failure, PaginatedResult<T>>> getPaginated({
    int page = 1,
    int limit = 20,
    Map<String, dynamic>? filters,
    String? sortBy,
    SortOrder sortOrder = SortOrder.asc,
  });
}

/// 分页结果模型
class PaginatedResult<T> {
  const PaginatedResult({
    required this.data,
    required this.total,
    required this.page,
    required this.limit,
    required this.totalPages,
  });

  final List<T> data;
  final int total;
  final int page;
  final int limit;
  final int totalPages;

  bool get hasNextPage => page < totalPages;
  bool get hasPreviousPage => page > 1;
}

/// 排序顺序枚举
enum SortOrder { asc, desc }

/// 缓存Repository基础接口
abstract class CachedRepositoryBase<T> extends RepositoryBase<T> {
  /// 从缓存获取
  Future<Either<Failure, T?>> getFromCache(String id);
  
  /// 保存到缓存
  Future<Either<Failure, void>> saveToCache(String id, T entity);
  
  /// 清除缓存
  Future<Either<Failure, void>> clearCache([String? id]);
  
  /// 刷新缓存
  Future<Either<Failure, T>> refreshCache(String id);
}
