/// Mapper基础抽象类
/// 
/// 定义了实体(Entity)和模型(Model)之间的转换规则
/// 遵循Clean Architecture的数据转换原则
abstract class MapperBase<Entity, Model> {
  /// 将模型转换为实体
  Entity toEntity(Model model);
  
  /// 将实体转换为模型
  Model toModel(Entity entity);
  
  /// 批量将模型转换为实体
  List<Entity> toEntityList(List<Model> models) {
    return models.map(toEntity).toList();
  }
  
  /// 批量将实体转换为模型
  List<Model> toModelList(List<Entity> entities) {
    return entities.map(toModel).toList();
  }
}

/// 双向Mapper基础类
/// 
/// 提供了实体和模型之间的双向转换功能
abstract class BidirectionalMapperBase<Entity, Model> 
    extends MapperBase<Entity, Model> {
  
  /// 尝试将模型转换为实体，失败时返回null
  Entity? tryToEntity(Model? model) {
    if (model == null) return null;
    try {
      return toEntity(model);
    } catch (e) {
      return null;
    }
  }
  
  /// 尝试将实体转换为模型，失败时返回null
  Model? tryToModel(Entity? entity) {
    if (entity == null) return null;
    try {
      return toModel(entity);
    } catch (e) {
      return null;
    }
  }
  
  /// 安全的批量转换，跳过转换失败的项
  List<Entity> safeToEntityList(List<Model> models) {
    return models
        .map(tryToEntity)
        .where((entity) => entity != null)
        .cast<Entity>()
        .toList();
  }
  
  /// 安全的批量转换，跳过转换失败的项
  List<Model> safeToModelList(List<Entity> entities) {
    return entities
        .map(tryToModel)
        .where((model) => model != null)
        .cast<Model>()
        .toList();
  }
}

/// JSON Mapper基础类
/// 
/// 专门用于处理JSON数据的转换
abstract class JsonMapperBase<Entity> {
  /// 从JSON转换为实体
  Entity fromJson(Map<String, dynamic> json);
  
  /// 将实体转换为JSON
  Map<String, dynamic> toJson(Entity entity);
  
  /// 从JSON列表转换为实体列表
  List<Entity> fromJsonList(List<dynamic> jsonList) {
    return jsonList
        .cast<Map<String, dynamic>>()
        .map(fromJson)
        .toList();
  }
  
  /// 将实体列表转换为JSON列表
  List<Map<String, dynamic>> toJsonList(List<Entity> entities) {
    return entities.map(toJson).toList();
  }
  
  /// 安全的JSON转换，失败时返回null
  Entity? tryFromJson(Map<String, dynamic>? json) {
    if (json == null) return null;
    try {
      return fromJson(json);
    } catch (e) {
      return null;
    }
  }
  
  /// 安全的实体转换，失败时返回空Map
  Map<String, dynamic> tryToJson(Entity? entity) {
    if (entity == null) return {};
    try {
      return toJson(entity);
    } catch (e) {
      return {};
    }
  }
}

/// 复合Mapper基础类
/// 
/// 用于处理复杂的嵌套对象转换
abstract class CompositeMapperBase<Entity, Model> 
    extends MapperBase<Entity, Model> {
  
  /// 获取子Mapper列表
  List<MapperBase> get childMappers;
  
  /// 验证所有子Mapper是否可用
  bool validateChildMappers() {
    return childMappers.isNotEmpty;
  }
}

/// Mapper工厂接口
abstract class MapperFactory {
  /// 获取指定类型的Mapper
  MapperBase<Entity, Model> getMapper<Entity, Model>();
  
  /// 注册Mapper
  void registerMapper<Entity, Model>(MapperBase<Entity, Model> mapper);
  
  /// 检查Mapper是否已注册
  bool hasMapper<Entity, Model>();
}
