import 'package:equatable/equatable.dart';

/// 数据模型基础类
/// 
/// 所有数据模型都应该继承此类
/// 提供了基础的相等性比较和字符串表示
abstract class ModelBase extends Equatable {
  const ModelBase();
  
  /// 模型的唯一标识符
  String get id;
  
  /// 创建时间
  DateTime? get createdAt => null;
  
  /// 更新时间
  DateTime? get updatedAt => null;
  
  /// 转换为JSON
  Map<String, dynamic> toJson();
  
  /// 从JSON创建实例的工厂方法（子类需要实现）
  // static T fromJson<T extends ModelBase>(Map<String, dynamic> json);
  
  /// 复制方法（子类需要实现）
  ModelBase copyWith();
  
  /// 验证模型数据是否有效
  bool isValid() => id.isNotEmpty;
  
  @override
  List<Object?> get props => [id, createdAt, updatedAt];
  
  @override
  String toString() => '$runtimeType(id: $id)';
}

/// 可审计的模型基础类
/// 
/// 包含创建时间、更新时间等审计信息
abstract class AuditableModelBase extends ModelBase {
  const AuditableModelBase({
    required this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
  });
  
  @override
  final DateTime createdAt;
  
  @override
  final DateTime? updatedAt;
  
  /// 创建者ID
  final String? createdBy;
  
  /// 更新者ID
  final String? updatedBy;
  
  @override
  List<Object?> get props => [
    ...super.props,
    createdBy,
    updatedBy,
  ];
}

/// 软删除模型基础类
/// 
/// 支持软删除功能的模型
abstract class SoftDeletableModelBase extends AuditableModelBase {
  const SoftDeletableModelBase({
    required super.createdAt,
    super.updatedAt,
    super.createdBy,
    super.updatedBy,
    this.deletedAt,
    this.deletedBy,
  });
  
  /// 删除时间
  final DateTime? deletedAt;
  
  /// 删除者ID
  final String? deletedBy;
  
  /// 是否已删除
  bool get isDeleted => deletedAt != null;
  
  @override
  bool isValid() => super.isValid() && !isDeleted;
  
  @override
  List<Object?> get props => [
    ...super.props,
    deletedAt,
    deletedBy,
  ];
}

/// 版本化模型基础类
/// 
/// 支持乐观锁的版本控制
abstract class VersionedModelBase extends AuditableModelBase {
  const VersionedModelBase({
    required super.createdAt,
    super.updatedAt,
    super.createdBy,
    super.updatedBy,
    this.version = 1,
  });
  
  /// 版本号
  final int version;
  
  /// 获取下一个版本号
  int get nextVersion => version + 1;
  
  @override
  List<Object?> get props => [
    ...super.props,
    version,
  ];
}

/// 缓存模型基础类
/// 
/// 包含缓存相关的元数据
abstract class CacheableModelBase extends ModelBase {
  const CacheableModelBase({
    this.cacheKey,
    this.expiresAt,
    this.lastAccessedAt,
  });
  
  /// 缓存键
  final String? cacheKey;
  
  /// 过期时间
  final DateTime? expiresAt;
  
  /// 最后访问时间
  final DateTime? lastAccessedAt;
  
  /// 是否已过期
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }
  
  /// 获取有效的缓存键
  String getEffectiveCacheKey() {
    return cacheKey ?? '${runtimeType}_$id';
  }
  
  @override
  List<Object?> get props => [
    ...super.props,
    cacheKey,
    expiresAt,
    lastAccessedAt,
  ];
}
