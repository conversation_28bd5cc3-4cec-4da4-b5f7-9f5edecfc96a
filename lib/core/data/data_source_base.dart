import 'package:dartz/dartz.dart';
import '../errors/failures.dart';

/// 数据源基础抽象类
/// 
/// 定义了数据源的通用接口，支持本地和远程数据源
abstract class DataSourceBase<T> {
  /// 获取单个数据
  Future<T> getById(String id);
  
  /// 获取所有数据
  Future<List<T>> getAll();
  
  /// 创建数据
  Future<T> create(T data);
  
  /// 更新数据
  Future<T> update(T data);
  
  /// 删除数据
  Future<void> delete(String id);
  
  /// 检查数据是否存在
  Future<bool> exists(String id);
}

/// 远程数据源基础接口
abstract class RemoteDataSourceBase<T> extends DataSourceBase<T> {
  /// 同步数据到远程
  Future<void> sync();
  
  /// 批量操作
  Future<List<T>> batchCreate(List<T> data);
  Future<List<T>> batchUpdate(List<T> data);
  Future<void> batchDelete(List<String> ids);
}

/// 本地数据源基础接口
abstract class LocalDataSourceBase<T> extends DataSourceBase<T> {
  /// 清除所有本地数据
  Future<void> clear();
  
  /// 获取数据数量
  Future<int> count();
  
  /// 搜索数据
  Future<List<T>> search(String query);
  
  /// 按条件查询
  Future<List<T>> findWhere(Map<String, dynamic> conditions);
}

/// 缓存数据源基础接口
abstract class CacheDataSourceBase<T> extends LocalDataSourceBase<T> {
  /// 设置缓存过期时间
  Future<void> setExpiration(String id, Duration duration);
  
  /// 检查缓存是否过期
  Future<bool> isExpired(String id);
  
  /// 清除过期缓存
  Future<void> clearExpired();
  
  /// 获取缓存大小
  Future<int> getCacheSize();
  
  /// 设置最大缓存大小
  void setMaxCacheSize(int maxSize);
}

/// 分页数据源接口
mixin PaginatedDataSourceMixin<T> on DataSourceBase<T> {
  /// 分页获取数据
  Future<PaginatedData<T>> getPaginated({
    int page = 1,
    int limit = 20,
    Map<String, dynamic>? filters,
    String? sortBy,
    SortOrder sortOrder = SortOrder.asc,
  });
}

/// 分页数据模型
class PaginatedData<T> {
  const PaginatedData({
    required this.data,
    required this.total,
    required this.page,
    required this.limit,
  });

  final List<T> data;
  final int total;
  final int page;
  final int limit;

  int get totalPages => (total / limit).ceil();
  bool get hasNextPage => page < totalPages;
  bool get hasPreviousPage => page > 1;
}

/// 排序顺序枚举
enum SortOrder { asc, desc }

/// 数据源工厂接口
abstract class DataSourceFactory<T> {
  /// 创建远程数据源
  RemoteDataSourceBase<T> createRemoteDataSource();
  
  /// 创建本地数据源
  LocalDataSourceBase<T> createLocalDataSource();
  
  /// 创建缓存数据源
  CacheDataSourceBase<T> createCacheDataSource();
}
