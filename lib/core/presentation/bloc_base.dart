import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../errors/failures.dart';

/// BLoC基础类
/// 
/// 所有BLoC都应该继承此类
/// 提供了统一的状态管理模式和错误处理
abstract class BlocBase<Event extends BlocEventBase, State extends BlocStateBase>
    extends Bloc<Event, State> {
  
  BlocBase(super.initialState) {
    // 注册通用事件处理器
    on<Event>((event, emit) async {
      await handleEvent(event, emit);
    });
  }
  
  /// 处理事件的抽象方法
  Future<void> handleEvent(Event event, Emitter<State> emit);
  
  /// 发射加载状态
  void emitLoading(Emitter<State> emit) {
    if (state is! LoadingState) {
      emit(createLoadingState() as State);
    }
  }
  
  /// 发射成功状态
  void emitSuccess<T>(Emitter<State> emit, T data) {
    emit(createSuccessState(data) as State);
  }
  
  /// 发射失败状态
  void emitFailure(Emitter<State> emit, Failure failure) {
    emit(createFailureState(failure) as State);
  }
  
  /// 创建加载状态（子类需要实现）
  BlocStateBase createLoadingState();
  
  /// 创建成功状态（子类需要实现）
  BlocStateBase createSuccessState<T>(T data);
  
  /// 创建失败状态（子类需要实现）
  BlocStateBase createFailureState(Failure failure);
  
  /// 处理错误的通用方法
  void handleError(Object error, StackTrace stackTrace) {
    // 记录错误日志
    // Logger可以在这里注入
    print('BLoC Error: $error');
    print('Stack Trace: $stackTrace');
  }
}

/// BLoC事件基础类
abstract class BlocEventBase extends Equatable {
  const BlocEventBase();
  
  @override
  List<Object?> get props => [];
}

/// BLoC状态基础类
abstract class BlocStateBase extends Equatable {
  const BlocStateBase();
  
  @override
  List<Object?> get props => [];
}

/// 初始状态基础类
abstract class InitialState extends BlocStateBase {
  const InitialState();
}

/// 加载状态基础类
abstract class LoadingState extends BlocStateBase {
  const LoadingState();
}

/// 成功状态基础类
abstract class SuccessState<T> extends BlocStateBase {
  const SuccessState(this.data);
  
  final T data;
  
  @override
  List<Object?> get props => [data];
}

/// 失败状态基础类
abstract class FailureState extends BlocStateBase {
  const FailureState({
    required this.failure,
    this.canRetry = true,
  });
  
  final Failure failure;
  final bool canRetry;
  
  String get message => failure.message;
  
  @override
  List<Object?> get props => [failure, canRetry];
}

/// 空状态基础类
abstract class EmptyState extends BlocStateBase {
  const EmptyState();
}

/// Cubit基础类
/// 
/// 简化版的状态管理，适用于简单场景
abstract class CubitBase<State extends BlocStateBase> extends Cubit<State> {
  CubitBase(super.initialState);
  
  /// 发射加载状态
  void emitLoading() {
    if (state is! LoadingState) {
      emit(createLoadingState() as State);
    }
  }
  
  /// 发射成功状态
  void emitSuccess<T>(T data) {
    emit(createSuccessState(data) as State);
  }
  
  /// 发射失败状态
  void emitFailure(Failure failure) {
    emit(createFailureState(failure) as State);
  }
  
  /// 创建加载状态（子类需要实现）
  BlocStateBase createLoadingState();
  
  /// 创建成功状态（子类需要实现）
  BlocStateBase createSuccessState<T>(T data);
  
  /// 创建失败状态（子类需要实现）
  BlocStateBase createFailureState(Failure failure);
  
  /// 处理错误的通用方法
  void handleError(Object error, StackTrace stackTrace) {
    print('Cubit Error: $error');
    print('Stack Trace: $stackTrace');
  }
}

/// 分页BLoC基础类
/// 
/// 专门处理分页数据的BLoC
abstract class PaginatedBlocBase<Event extends BlocEventBase, 
    State extends PaginatedStateBase<T>, T>
    extends BlocBase<Event, State> {
  
  PaginatedBlocBase(super.initialState);
  
  /// 加载更多数据
  void loadMore() {
    if (state is PaginatedLoadedState<T>) {
      final currentState = state as PaginatedLoadedState<T>;
      if (currentState.hasNextPage && !currentState.isLoadingMore) {
        add(createLoadMoreEvent() as Event);
      }
    }
  }
  
  /// 刷新数据
  void refresh() {
    add(createRefreshEvent() as Event);
  }
  
  /// 创建加载更多事件（子类需要实现）
  BlocEventBase createLoadMoreEvent();
  
  /// 创建刷新事件（子类需要实现）
  BlocEventBase createRefreshEvent();
}

/// 分页状态基础类
abstract class PaginatedStateBase<T> extends BlocStateBase {
  const PaginatedStateBase();
}

/// 分页加载完成状态
class PaginatedLoadedState<T> extends PaginatedStateBase<T> {
  const PaginatedLoadedState({
    required this.items,
    required this.hasNextPage,
    required this.currentPage,
    this.isLoadingMore = false,
  });
  
  final List<T> items;
  final bool hasNextPage;
  final int currentPage;
  final bool isLoadingMore;
  
  PaginatedLoadedState<T> copyWith({
    List<T>? items,
    bool? hasNextPage,
    int? currentPage,
    bool? isLoadingMore,
  }) {
    return PaginatedLoadedState<T>(
      items: items ?? this.items,
      hasNextPage: hasNextPage ?? this.hasNextPage,
      currentPage: currentPage ?? this.currentPage,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
    );
  }
  
  @override
  List<Object?> get props => [items, hasNextPage, currentPage, isLoadingMore];
}
