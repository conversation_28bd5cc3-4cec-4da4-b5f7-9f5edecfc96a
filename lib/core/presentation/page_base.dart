import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../errors/failures.dart';
import 'bloc_base.dart';

/// 页面基础类
/// 
/// 所有页面都应该继承此类
/// 提供了统一的页面结构和错误处理
abstract class PageBase extends StatelessWidget {
  const PageBase({super.key});
  
  /// 页面标题
  String get title => '';
  
  /// 是否显示应用栏
  bool get showAppBar => true;
  
  /// 是否显示返回按钮
  bool get showBackButton => true;
  
  /// 应用栏操作按钮
  List<Widget>? get appBarActions => null;
  
  /// 浮动操作按钮
  Widget? get floatingActionButton => null;
  
  /// 底部导航栏
  Widget? get bottomNavigationBar => null;
  
  /// 抽屉菜单
  Widget? get drawer => null;
  
  /// 页面背景色
  Color? get backgroundColor => null;
  
  /// 是否安全区域
  bool get useSafeArea => true;
  
  /// 构建页面内容
  Widget buildContent(BuildContext context);
  
  /// 构建加载状态
  Widget buildLoading(BuildContext context) {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }
  
  /// 构建错误状态
  Widget buildError(BuildContext context, Failure failure) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            failure.message,
            style: Theme.of(context).textTheme.titleMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => onRetry(context),
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }
  
  /// 构建空状态
  Widget buildEmpty(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '暂无数据',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 重试回调
  void onRetry(BuildContext context) {
    // 子类可以重写此方法
  }
  
  @override
  Widget build(BuildContext context) {
    Widget body = buildContent(context);
    
    if (useSafeArea) {
      body = SafeArea(child: body);
    }
    
    return Scaffold(
      appBar: showAppBar ? buildAppBar(context) : null,
      body: body,
      floatingActionButton: floatingActionButton,
      bottomNavigationBar: bottomNavigationBar,
      drawer: drawer,
      backgroundColor: backgroundColor,
    );
  }
  
  /// 构建应用栏
  PreferredSizeWidget? buildAppBar(BuildContext context) {
    if (!showAppBar) return null;
    
    return AppBar(
      title: title.isNotEmpty ? Text(title) : null,
      automaticallyImplyLeading: showBackButton,
      actions: appBarActions,
    );
  }
}

/// 状态页面基础类
/// 
/// 带有状态管理的页面
abstract class StatefulPageBase extends StatefulWidget {
  const StatefulPageBase({super.key});
  
  @override
  StatefulPageBaseState createState() => createState();
  
  /// 创建状态（子类需要实现）
  @override
  StatefulPageBaseState createState();
}

/// 状态页面状态基础类
abstract class StatefulPageBaseState<T extends StatefulPageBase> 
    extends State<T> {
  
  /// 页面标题
  String get title => '';
  
  /// 是否显示应用栏
  bool get showAppBar => true;
  
  /// 是否显示返回按钮
  bool get showBackButton => true;
  
  /// 应用栏操作按钮
  List<Widget>? get appBarActions => null;
  
  /// 浮动操作按钮
  Widget? get floatingActionButton => null;
  
  /// 底部导航栏
  Widget? get bottomNavigationBar => null;
  
  /// 抽屉菜单
  Widget? get drawer => null;
  
  /// 页面背景色
  Color? get backgroundColor => null;
  
  /// 是否安全区域
  bool get useSafeArea => true;
  
  /// 构建页面内容
  Widget buildContent(BuildContext context);
  
  /// 构建加载状态
  Widget buildLoading(BuildContext context) {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }
  
  /// 构建错误状态
  Widget buildError(BuildContext context, Failure failure) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            failure.message,
            style: Theme.of(context).textTheme.titleMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: onRetry,
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }
  
  /// 构建空状态
  Widget buildEmpty(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '暂无数据',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 重试回调
  void onRetry() {
    // 子类可以重写此方法
  }
  
  @override
  Widget build(BuildContext context) {
    Widget body = buildContent(context);
    
    if (useSafeArea) {
      body = SafeArea(child: body);
    }
    
    return Scaffold(
      appBar: showAppBar ? buildAppBar(context) : null,
      body: body,
      floatingActionButton: floatingActionButton,
      bottomNavigationBar: bottomNavigationBar,
      drawer: drawer,
      backgroundColor: backgroundColor,
    );
  }
  
  /// 构建应用栏
  PreferredSizeWidget? buildAppBar(BuildContext context) {
    if (!showAppBar) return null;
    
    return AppBar(
      title: title.isNotEmpty ? Text(title) : null,
      automaticallyImplyLeading: showBackButton,
      actions: appBarActions,
    );
  }
}
