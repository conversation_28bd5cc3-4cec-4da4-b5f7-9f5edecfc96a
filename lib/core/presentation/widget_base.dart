import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../errors/failures.dart';
import 'bloc_base.dart';

/// Widget基础类
/// 
/// 所有自定义Widget都应该继承此类
/// 提供了统一的Widget结构和状态处理
abstract class WidgetBase extends StatelessWidget {
  const WidgetBase({super.key});
  
  /// 构建Widget内容
  Widget buildContent(BuildContext context);
  
  /// 构建加载状态
  Widget buildLoading(BuildContext context) {
    return const Center(
      child: SizedBox(
        width: 24,
        height: 24,
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }
  
  /// 构建错误状态
  Widget buildError(BuildContext context, Failure failure) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.error_outline,
            color: Theme.of(context).colorScheme.error,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            failure.message,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.error,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  /// 构建空状态
  Widget buildEmpty(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.inbox_outlined,
            color: Colors.grey[400],
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            '暂无数据',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return buildContent(context);
  }
}

/// 状态Widget基础类
/// 
/// 带有状态管理的Widget
abstract class StatefulWidgetBase extends StatefulWidget {
  const StatefulWidgetBase({super.key});
  
  @override
  StatefulWidgetBaseState createState();
}

/// 状态Widget状态基础类
abstract class StatefulWidgetBaseState<T extends StatefulWidgetBase> 
    extends State<T> {
  
  /// 构建Widget内容
  Widget buildContent(BuildContext context);
  
  /// 构建加载状态
  Widget buildLoading(BuildContext context) {
    return const Center(
      child: SizedBox(
        width: 24,
        height: 24,
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }
  
  /// 构建错误状态
  Widget buildError(BuildContext context, Failure failure) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.error_outline,
            color: Theme.of(context).colorScheme.error,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            failure.message,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.error,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  /// 构建空状态
  Widget buildEmpty(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.inbox_outlined,
            color: Colors.grey[400],
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            '暂无数据',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return buildContent(context);
  }
}

/// BLoC监听Widget基础类
/// 
/// 专门用于监听BLoC状态变化的Widget
abstract class BlocListenerWidgetBase<B extends BlocBase<dynamic, S>, S extends BlocStateBase> 
    extends StatelessWidget {
  const BlocListenerWidgetBase({super.key});
  
  /// 构建Widget内容
  Widget buildContent(BuildContext context);
  
  /// 监听状态变化
  void onStateChanged(BuildContext context, S state) {
    // 子类可以重写此方法
  }
  
  /// 监听条件
  bool listenWhen(S previous, S current) => true;
  
  @override
  Widget build(BuildContext context) {
    return BlocListener<B, S>(
      listener: onStateChanged,
      listenWhen: listenWhen,
      child: buildContent(context),
    );
  }
}

/// BLoC构建器Widget基础类
/// 
/// 专门用于根据BLoC状态构建UI的Widget
abstract class BlocBuilderWidgetBase<B extends BlocBase<dynamic, S>, S extends BlocStateBase> 
    extends StatelessWidget {
  const BlocBuilderWidgetBase({super.key});
  
  /// 根据状态构建Widget
  Widget buildWithState(BuildContext context, S state);
  
  /// 构建条件
  bool buildWhen(S previous, S current) => true;
  
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<B, S>(
      builder: buildWithState,
      buildWhen: buildWhen,
    );
  }
}

/// BLoC消费者Widget基础类
/// 
/// 同时监听和构建的Widget
abstract class BlocConsumerWidgetBase<B extends BlocBase<dynamic, S>, S extends BlocStateBase> 
    extends StatelessWidget {
  const BlocConsumerWidgetBase({super.key});
  
  /// 根据状态构建Widget
  Widget buildWithState(BuildContext context, S state);
  
  /// 监听状态变化
  void onStateChanged(BuildContext context, S state) {
    // 子类可以重写此方法
  }
  
  /// 监听条件
  bool listenWhen(S previous, S current) => true;
  
  /// 构建条件
  bool buildWhen(S previous, S current) => true;
  
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<B, S>(
      listener: onStateChanged,
      builder: buildWithState,
      listenWhen: listenWhen,
      buildWhen: buildWhen,
    );
  }
}

/// 响应式Widget基础类
/// 
/// 根据屏幕尺寸自适应的Widget
abstract class ResponsiveWidgetBase extends StatelessWidget {
  const ResponsiveWidgetBase({super.key});
  
  /// 手机端布局
  Widget buildMobile(BuildContext context);
  
  /// 平板端布局
  Widget buildTablet(BuildContext context) => buildMobile(context);
  
  /// 桌面端布局
  Widget buildDesktop(BuildContext context) => buildTablet(context);
  
  /// 断点配置
  double get mobileBreakpoint => 600;
  double get tabletBreakpoint => 1024;
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= tabletBreakpoint) {
          return buildDesktop(context);
        } else if (constraints.maxWidth >= mobileBreakpoint) {
          return buildTablet(context);
        } else {
          return buildMobile(context);
        }
      },
    );
  }
}
