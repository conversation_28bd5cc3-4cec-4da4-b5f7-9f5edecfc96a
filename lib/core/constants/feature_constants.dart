/// 功能常量定义
/// 
/// 定义了应用中所有可配置的功能模块常量
/// 用于功能配置管理和条件依赖注入
class Features {
  Features._(); // 私有构造函数，防止实例化

  // 核心功能模块
  static const String authentication = 'authentication';
  static const String authorization = 'authorization';

  // UI/UX 模块
  static const String internationalization = 'internationalization';
  static const String theming = 'theming';

  // 高级功能模块
  static const String analytics = 'analytics';
  static const String performanceMonitoring = 'performance_monitoring';
  static const String pushNotifications = 'push_notifications';

  // 开发工具
  static const String devTools = 'dev_tools';
  static const String debugFeatures = 'debug_features';

  // 网络和数据
  static const String offlineSupport = 'offline_support';
  static const String dataSync = 'data_sync';
  static const String caching = 'caching';

  // 安全功能
  static const String biometricAuth = 'biometric_auth';
  static const String encryption = 'encryption';
  static const String certificatePinning = 'certificate_pinning';

  // 媒体功能
  static const String imageProcessing = 'image_processing';
  static const String videoPlayback = 'video_playback';
  static const String audioRecording = 'audio_recording';

  // 设备功能
  static const String camera = 'camera';
  static const String location = 'location';
  static const String bluetooth = 'bluetooth';
  static const String nfc = 'nfc';

  // 社交功能
  static const String socialLogin = 'social_login';
  static const String sharing = 'sharing';
  static const String deepLinking = 'deep_linking';

  // 支付功能
  static const String inAppPurchase = 'in_app_purchase';
  static const String paymentGateway = 'payment_gateway';

  // 通知功能
  static const String localNotifications = 'local_notifications';
  static const String backgroundTasks = 'background_tasks';

  // 数据库功能
  static const String sqliteDatabase = 'sqlite_database';
  static const String cloudDatabase = 'cloud_database';

  // 文件功能
  static const String fileUpload = 'file_upload';
  static const String fileDownload = 'file_download';
  static const String documentViewer = 'document_viewer';

  // 地图功能
  static const String maps = 'maps';
  static const String geofencing = 'geofencing';

  // 通信功能
  static const String webSocket = 'web_socket';
  static const String webRTC = 'web_rtc';

  // AI/ML功能
  static const String machineLearning = 'machine_learning';
  static const String textRecognition = 'text_recognition';
  static const String faceDetection = 'face_detection';

  // 可访问性功能
  static const String accessibility = 'accessibility';
  static const String screenReader = 'screen_reader';

  // 测试功能
  static const String testingTools = 'testing_tools';
  static const String mockData = 'mock_data';

  /// 所有功能列表
  static const List<String> allFeatures = [
    // 核心功能
    authentication,
    authorization,
    
    // UI/UX
    internationalization,
    theming,
    
    // 高级功能
    analytics,
    performanceMonitoring,
    pushNotifications,
    
    // 开发工具
    devTools,
    debugFeatures,
    
    // 网络和数据
    offlineSupport,
    dataSync,
    caching,
    
    // 安全功能
    biometricAuth,
    encryption,
    certificatePinning,
    
    // 媒体功能
    imageProcessing,
    videoPlayback,
    audioRecording,
    
    // 设备功能
    camera,
    location,
    bluetooth,
    nfc,
    
    // 社交功能
    socialLogin,
    sharing,
    deepLinking,
    
    // 支付功能
    inAppPurchase,
    paymentGateway,
    
    // 通知功能
    localNotifications,
    backgroundTasks,
    
    // 数据库功能
    sqliteDatabase,
    cloudDatabase,
    
    // 文件功能
    fileUpload,
    fileDownload,
    documentViewer,
    
    // 地图功能
    maps,
    geofencing,
    
    // 通信功能
    webSocket,
    webRTC,
    
    // AI/ML功能
    machineLearning,
    textRecognition,
    faceDetection,
    
    // 可访问性功能
    accessibility,
    screenReader,
    
    // 测试功能
    testingTools,
    mockData,
  ];

  /// 核心功能（不可禁用）
  static const List<String> coreFeatures = [
    authentication,
    authorization,
  ];

  /// 开发环境专用功能
  static const List<String> developmentFeatures = [
    devTools,
    debugFeatures,
    testingTools,
    mockData,
  ];

  /// 生产环境推荐功能
  static const List<String> productionFeatures = [
    analytics,
    performanceMonitoring,
    pushNotifications,
    offlineSupport,
    caching,
    encryption,
  ];

  /// 检查是否为核心功能
  static bool isCoreFeature(String feature) {
    return coreFeatures.contains(feature);
  }

  /// 检查是否为开发功能
  static bool isDevelopmentFeature(String feature) {
    return developmentFeatures.contains(feature);
  }

  /// 检查是否为生产推荐功能
  static bool isProductionFeature(String feature) {
    return productionFeatures.contains(feature);
  }

  /// 获取功能分组
  static Map<String, List<String>> getFeatureGroups() {
    return {
      'core': coreFeatures,
      'ui_ux': [internationalization, theming],
      'advanced': [analytics, performanceMonitoring, pushNotifications],
      'development': developmentFeatures,
      'security': [biometricAuth, encryption, certificatePinning],
      'media': [imageProcessing, videoPlayback, audioRecording],
      'device': [camera, location, bluetooth, nfc],
      'social': [socialLogin, sharing, deepLinking],
      'payment': [inAppPurchase, paymentGateway],
      'notification': [localNotifications, backgroundTasks],
      'database': [sqliteDatabase, cloudDatabase],
      'file': [fileUpload, fileDownload, documentViewer],
      'location': [maps, geofencing],
      'communication': [webSocket, webRTC],
      'ai_ml': [machineLearning, textRecognition, faceDetection],
      'accessibility': [accessibility, screenReader],
      'testing': [testingTools, mockData],
    };
  }
}
