import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:yaml/yaml.dart';

/// 环境配置管理器
/// 
/// 负责管理不同环境的配置信息
/// 支持配置合并、环境切换和热重载
class EnvironmentConfig {
  static EnvironmentConfig? _instance;
  static EnvironmentConfig get instance {
    if (_instance == null) {
      throw StateError('EnvironmentConfig has not been initialized. Call EnvironmentConfig.initialize() first.');
    }
    return _instance!;
  }

  EnvironmentConfig._(this._environment, this._config);

  final String _environment;
  final Map<String, dynamic> _config;
  final StreamController<EnvironmentConfigChangeEvent> _changeController = 
      StreamController<EnvironmentConfigChangeEvent>.broadcast();

  /// 当前环境
  String get environment => _environment;

  /// 配置变更事件流
  Stream<EnvironmentConfigChangeEvent> get onConfigChanged => _changeController.stream;

  /// 初始化环境配置
  static Future<void> initialize(String environment) async {
    try {
      // 加载基础配置
      final baseConfig = await _loadConfigFile('assets/config/app_config.yaml');
      
      // 加载环境特定配置
      final envConfig = await _loadConfigFile('assets/config/app_config.$environment.yaml');
      
      // 合并配置
      final mergedConfig = _mergeConfigs(baseConfig, envConfig);
      
      _instance = EnvironmentConfig._(environment, mergedConfig);
      
      if (kDebugMode) {
        _instance!._logConfiguration();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to load environment config: $e');
      }
      // 使用默认配置
      _instance = EnvironmentConfig._(environment, {});
    }
  }

  /// 加载配置文件
  static Future<Map<String, dynamic>> _loadConfigFile(String path) async {
    try {
      final yamlString = await rootBundle.loadString(path);
      final yamlMap = loadYaml(yamlString);
      return Map<String, dynamic>.from(yamlMap);
    } catch (e) {
      if (kDebugMode) {
        print('Failed to load config file $path: $e');
      }
      return {};
    }
  }

  /// 合并配置
  static Map<String, dynamic> _mergeConfigs(
    Map<String, dynamic> baseConfig,
    Map<String, dynamic> envConfig,
  ) {
    final merged = Map<String, dynamic>.from(baseConfig);
    _deepMerge(merged, envConfig);
    return merged;
  }

  /// 深度合并配置
  static void _deepMerge(Map<String, dynamic> target, Map<String, dynamic> source) {
    source.forEach((key, value) {
      if (value is Map<String, dynamic> && target[key] is Map<String, dynamic>) {
        _deepMerge(target[key] as Map<String, dynamic>, value);
      } else {
        target[key] = value;
      }
    });
  }

  /// 获取配置值
  T? getValue<T>(String key, [T? defaultValue]) {
    final keys = key.split('.');
    dynamic current = _config;
    
    for (final k in keys) {
      if (current is Map<String, dynamic> && current.containsKey(k)) {
        current = current[k];
      } else {
        return defaultValue;
      }
    }
    
    return current is T ? current : defaultValue;
  }

  /// 获取字符串配置
  String getString(String key, [String defaultValue = '']) {
    return getValue<String>(key, defaultValue) ?? defaultValue;
  }

  /// 获取整数配置
  int getInt(String key, [int defaultValue = 0]) {
    final value = getValue<dynamic>(key);
    if (value is int) return value;
    if (value is String) return int.tryParse(value) ?? defaultValue;
    return defaultValue;
  }

  /// 获取双精度配置
  double getDouble(String key, [double defaultValue = 0.0]) {
    final value = getValue<dynamic>(key);
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? defaultValue;
    return defaultValue;
  }

  /// 获取布尔配置
  bool getBool(String key, [bool defaultValue = false]) {
    final value = getValue<dynamic>(key);
    if (value is bool) return value;
    if (value is String) {
      return value.toLowerCase() == 'true' || value == '1';
    }
    if (value is int) return value != 0;
    return defaultValue;
  }

  /// 获取列表配置
  List<T> getList<T>(String key, [List<T> defaultValue = const []]) {
    final value = getValue<dynamic>(key);
    if (value is List) {
      return value.cast<T>();
    }
    return defaultValue;
  }

  /// 获取映射配置
  Map<String, T> getMap<T>(String key, [Map<String, T> defaultValue = const {}]) {
    final value = getValue<dynamic>(key);
    if (value is Map<String, dynamic>) {
      return value.cast<String, T>();
    }
    return defaultValue;
  }

  /// 检查配置是否存在
  bool hasKey(String key) {
    final keys = key.split('.');
    dynamic current = _config;
    
    for (final k in keys) {
      if (current is Map<String, dynamic> && current.containsKey(k)) {
        current = current[k];
      } else {
        return false;
      }
    }
    
    return true;
  }

  /// 获取所有配置
  Map<String, dynamic> getAllConfig() {
    return Map<String, dynamic>.from(_config);
  }

  /// 获取环境特定配置
  Map<String, dynamic> getEnvironmentConfig() {
    return getMap<dynamic>('environment.$_environment', {});
  }

  /// 是否为开发环境
  bool get isDevelopment => _environment == 'dev' || _environment == 'development';

  /// 是否为测试环境
  bool get isTest => _environment == 'test' || _environment == 'testing';

  /// 是否为生产环境
  bool get isProduction => _environment == 'prod' || _environment == 'production';

  /// 是否为调试模式
  bool get isDebugMode => kDebugMode && (isDevelopment || isTest);

  /// 获取API基础URL
  String get apiBaseUrl => getString('api.base_url', 'https://api.example.com');

  /// 获取API超时时间
  int get apiTimeout => getInt('api.timeout', 30);

  /// 是否启用API日志
  bool get enableApiLogging => getBool('api.enable_logging', isDevelopment);

  /// 获取数据库名称
  String get databaseName => getString('database.name', 'app.db');

  /// 是否启用数据库日志
  bool get enableDatabaseLogging => getBool('database.enable_logging', isDevelopment);

  /// 获取日志级别
  String get logLevel => getString('logging.level', isDevelopment ? 'debug' : 'info');

  /// 是否启用控制台日志
  bool get enableConsoleLogging => getBool('logging.enable_console', isDevelopment);

  /// 是否启用文件日志
  bool get enableFileLogging => getBool('logging.enable_file', true);

  /// 动态更新配置
  Future<void> updateConfig(String key, dynamic value) async {
    final keys = key.split('.');
    Map<String, dynamic> current = _config;
    
    for (int i = 0; i < keys.length - 1; i++) {
      final k = keys[i];
      if (!current.containsKey(k) || current[k] is! Map<String, dynamic>) {
        current[k] = <String, dynamic>{};
      }
      current = current[k] as Map<String, dynamic>;
    }
    
    final oldValue = current[keys.last];
    current[keys.last] = value;
    
    // 发送配置变更事件
    _changeController.add(EnvironmentConfigChangeEvent(
      key: key,
      oldValue: oldValue,
      newValue: value,
    ));
  }

  /// 重新加载配置
  Future<void> reload() async {
    try {
      // 重新加载配置文件
      final baseConfig = await _loadConfigFile('assets/config/app_config.yaml');
      final envConfig = await _loadConfigFile('assets/config/app_config.$_environment.yaml');
      final mergedConfig = _mergeConfigs(baseConfig, envConfig);
      
      // 更新配置
      _config.clear();
      _config.addAll(mergedConfig);
      
      // 发送重载事件
      _changeController.add(EnvironmentConfigChangeEvent(
        key: '__reload__',
        oldValue: null,
        newValue: _config,
      ));
      
      if (kDebugMode) {
        print('Environment config reloaded successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to reload environment config: $e');
      }
    }
  }

  /// 记录配置信息
  void _logConfiguration() {
    print('=== Environment Configuration ===');
    print('Environment: $_environment');
    print('API Base URL: $apiBaseUrl');
    print('Database Name: $databaseName');
    print('Log Level: $logLevel');
    print('Debug Mode: $isDebugMode');
    print('================================');
  }

  /// 清理资源
  void dispose() {
    _changeController.close();
  }
}

/// 环境配置变更事件
class EnvironmentConfigChangeEvent {
  const EnvironmentConfigChangeEvent({
    required this.key,
    required this.oldValue,
    required this.newValue,
  });

  final String key;
  final dynamic oldValue;
  final dynamic newValue;
}

/// 环境类型枚举
enum Environment {
  development('dev'),
  test('test'),
  production('prod');

  const Environment(this.value);
  final String value;

  static Environment fromString(String value) {
    switch (value.toLowerCase()) {
      case 'dev':
      case 'development':
        return Environment.development;
      case 'test':
      case 'testing':
        return Environment.test;
      case 'prod':
      case 'production':
        return Environment.production;
      default:
        return Environment.development;
    }
  }
}
