# flutter_template

本项目为一个企业级 **Flutter 模板工程**，基于 `doc/` 目录下的多份架构设计文档（v2.0 / v3.0 / 补充规范）实现，目标是构建一个可复用、可扩展、可维护的统一基础架构，覆盖 99% 的通用业务场景，支持 Android / iOS 双平台。

---

## ✅ 项目目标

- 🚀 快速启动一个标准化、结构清晰的 Flutter 项目
- 📦 集成企业级必备能力（如网络、状态管理、依赖注入、环境切换、UI组件库）
- 🧱 遵循 Clean Architecture + Monorepo 模型，解耦业务与表现
- 🧰 支持命令行一键生成自定义工程：`flutter create -t my_template --project_name my_app`

---

## 🧭 架构核心

本模板严格遵循 **整洁架构 (Clean Architecture)**，三层分明：

Presentation Layer (UI 展现)
└── Domain Layer (核心业务逻辑)
└── Data Layer (远程/本地数据源)

支持如下特性：

| 类别 | 技术栈 | 描述 |
|------|--------|------|
| 状态管理 | `bloc` | 企业级推荐，结构清晰，便于测试 |
| 路由管理 | `go_router` | 官方推荐，支持深链接与嵌套路由 |
| 网络访问 | `dio` | 支持拦截器、超时、Token刷新等 |
| 数据存储 | `drift`, `shared_preferences` | 响应式本地数据库 + 配置存储 |
| 依赖注入 | `get_it` + `injectable` | 自动注册，环境支持 |
| UI系统 | `ui_kit` 模块 | 自定义组件、主题系统、响应式设计 |
| 原生桥接 | `pigeon` / `dart:ffi` | 用于接入第三方SDK与硬件能力 |
| 多环境支持 | `Flavor` + DI | 支持国内与国际服务配置切换 |
| 模块结构 | `melos` + Monorepo | 模块化项目管理，便于团队协作 |

---

## 📂 项目结构说明

```bash
project_root/
├── apps/                  # 主应用壳（Android/iOS/Web）
│   └── main_app/
├── packages/
│   ├── features/          # 每个业务模块
│   ├── core/              # 网络、分析、数据库等核心能力
│   └── shared/            # 通用组件、UI设计系统、工具函数
├── doc/                   # 架构设计文档与说明
├── melos.yaml             # Monorepo 管理配置
└── README.md


⸻

🚧 使用方式（计划支持）

未来将支持以下命令式一键创建方式（通过定制模板）：

flutter create -t flutter_enterprise_template --project_name your_app_name

或通过 Makefile / CLI 工具一键执行：

make create_project --project_name your_app_name

将自动生成符合架构规范的 Flutter 工程，内含：
	•	登录页/主页等演示模块
	•	注入已配置的服务与主题
	•	集成完整的状态管理与路由系统

⸻

📖 文档与参考资料

本模板架构基于以下文档实现（详见 doc/ 目录）：
	•	Flutter项目架构设计研究_.docx
	•	企业级Flutter应用统一架构方案 v2.0
	•	企业级Flutter应用统一架构方案 v3.0
	•	flutter-enterprise-architecture-guide.md
	•	补充.md

⸻

📱 支持平台

✅ Android
✅ iOS
⬜ Flutter Web（后续支持）
⬜ macOS / Windows / Linux（待适配）

⸻

📌 注意事项
	•	本模板默认采用 bloc 模式，如需切换 riverpod 可重构状态管理模块
	•	所有模块已支持单元测试 / 集成测试结构
	•	强烈建议使用 VSCode + Melos 插件进行开发

⸻

🔮 后续规划
	•	自动生成项目的 CLI 工具
	•	支持 Flutter Web / Desktop 渐进式接入
	•	支持 DSL 构建项目结构 / 业务流程
	•	提供官方 Demo 展示项目最佳实践

⸻

🤝 贡献指南

欢迎任何贡献！请查看 CONTRIBUTING.md 获取开发规范与提交方式。

⸻

🧭 License

MIT License. Free to use and modify.