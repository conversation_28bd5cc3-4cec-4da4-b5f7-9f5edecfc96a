{"valid_import": true, "imports": [{"uri": "package:get_it/get_it.dart", "transitive": false}, {"uri": "package:injectable/injectable.dart", "transitive": false}, {"uri": "package:dio/dio.dart", "transitive": false}, {"uri": "package:flutter_secure_storage/flutter_secure_storage.dart", "transitive": false}, {"uri": "package:hive_flutter/hive_flutter.dart", "transitive": false}, {"uri": "package:logger/logger.dart", "transitive": false}, {"uri": "package:yaml/yaml.dart", "transitive": false}, {"uri": "package:flutter/services.dart", "transitive": false}, {"uri": "package:flutter_enterprise_app/core/config/feature_config.dart", "transitive": false}, {"uri": "package:flutter_enterprise_app/core/di/injection.config.dart", "transitive": false}], "elements": []}