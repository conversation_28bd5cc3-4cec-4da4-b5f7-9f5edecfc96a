{"valid_import": true, "imports": [{"uri": "package:get_it/get_it.dart", "transitive": false}, {"uri": "package:flutter_enterprise_app/core/config/feature_config.dart", "transitive": false}, {"uri": "package:flutter_enterprise_app/core/constants/feature_constants.dart", "transitive": false}, {"uri": "package:flutter_enterprise_app/core/di/conditional_di.dart", "transitive": false}, {"uri": "package:flutter_enterprise_app/core/di/noop_services.dart", "transitive": false}], "elements": []}