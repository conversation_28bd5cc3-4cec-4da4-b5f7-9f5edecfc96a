{"valid_import": true, "imports": [{"uri": "package:flutter_test/flutter_test.dart", "transitive": false}, {"uri": "package:get_it/get_it.dart", "transitive": false}, {"uri": "package:mocktail/mocktail.dart", "transitive": false}, {"uri": "package:flutter_enterprise_app/core/config/feature_config.dart", "transitive": false}, {"uri": "package:flutter_enterprise_app/core/config/environment_config.dart", "transitive": false}, {"uri": "package:flutter_enterprise_app/core/di/injection.dart", "transitive": false}], "elements": []}