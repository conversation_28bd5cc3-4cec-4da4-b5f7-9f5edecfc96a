import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'helpers/test_helper.dart';
import 'mocks/mock_services.dart';

/// 测试基础类
/// 
/// 所有测试类都应该继承此类
/// 提供统一的测试环境初始化和清理
abstract class TestBase {
  /// 设置测试环境
  Future<void> setUp() async {
    await TestHelper.initializeTest();
    await setUpMocks();
    await setUpCustom();
  }

  /// 清理测试环境
  Future<void> tearDown() async {
    await tearDownCustom();
    await TestHelper.cleanupTest();
  }

  /// 设置Mock服务
  Future<void> setUpMocks() async {
    // 子类可以重写此方法来设置特定的Mock服务
  }

  /// 自定义设置
  Future<void> setUpCustom() async {
    // 子类可以重写此方法来进行自定义设置
  }

  /// 自定义清理
  Future<void> tearDownCustom() async {
    // 子类可以重写此方法来进行自定义清理
  }

  /// 创建测试组
  void testGroup(String description, void Function() body) {
    group(description, () {
      setUp(() async {
        await this.setUp();
      });

      tearDown(() async {
        await this.tearDown();
      });

      body();
    });
  }

  /// 验证异常
  void expectException<T extends Exception>(
    void Function() callback, {
    String? message,
  }) {
    expect(
      callback,
      throwsA(isA<T>()),
      reason: message,
    );
  }

  /// 验证异步异常
  Future<void> expectAsyncException<T extends Exception>(
    Future<void> Function() callback, {
    String? message,
  }) async {
    expect(
      callback,
      throwsA(isA<T>()),
      reason: message,
    );
  }

  /// 验证Mock调用
  void verifyMockCall<T extends Mock>(
    T mock,
    dynamic invocation, {
    int times = 1,
  }) {
    verify(() => invocation).called(times);
  }

  /// 验证Mock从未被调用
  void verifyNeverCalled<T extends Mock>(
    T mock,
    dynamic invocation,
  ) {
    verifyNever(() => invocation);
  }

  /// 重置Mock
  void resetMock<T extends Mock>(T mock) {
    reset(mock);
  }

  /// 重置所有Mock
  void resetAllMocks(List<Mock> mocks) {
    for (final mock in mocks) {
      reset(mock);
    }
  }
}

/// 单元测试基础类
/// 
/// 专门用于单元测试的基础类
abstract class UnitTestBase extends TestBase {
  @override
  Future<void> setUpMocks() async {
    // 单元测试通常需要Mock所有外部依赖
    await super.setUpMocks();
  }
}

/// 集成测试基础类
/// 
/// 专门用于集成测试的基础类
abstract class IntegrationTestBase extends TestBase {
  @override
  Future<void> setUpMocks() async {
    // 集成测试可能只Mock部分外部依赖
    await super.setUpMocks();
  }
}

/// Widget测试基础类
/// 
/// 专门用于Widget测试的基础类
abstract class WidgetTestBase extends TestBase {
  @override
  Future<void> setUpMocks() async {
    // Widget测试需要Mock UI相关的依赖
    await super.setUpMocks();
  }

  /// 创建Widget测试
  void widgetTest(
    String description,
    Future<void> Function(WidgetTester) callback, {
    bool skip = false,
    String? skipReason,
  }) {
    testWidgets(description, (tester) async {
      await setUp();
      try {
        await callback(tester);
      } finally {
        await tearDown();
      }
    }, skip: skip, skipReason: skipReason);
  }
}

/// BLoC测试基础类
/// 
/// 专门用于BLoC测试的基础类
abstract class BlocTestBase extends TestBase {
  @override
  Future<void> setUpMocks() async {
    // BLoC测试需要Mock Repository和UseCase
    await super.setUpMocks();
  }
}

/// Repository测试基础类
/// 
/// 专门用于Repository测试的基础类
abstract class RepositoryTestBase extends TestBase {
  @override
  Future<void> setUpMocks() async {
    // Repository测试需要Mock数据源
    await super.setUpMocks();
  }
}

/// UseCase测试基础类
/// 
/// 专门用于UseCase测试的基础类
abstract class UseCaseTestBase extends TestBase {
  @override
  Future<void> setUpMocks() async {
    // UseCase测试需要Mock Repository
    await super.setUpMocks();
  }
}

/// 服务测试基础类
/// 
/// 专门用于服务测试的基础类
abstract class ServiceTestBase extends TestBase {
  @override
  Future<void> setUpMocks() async {
    // 服务测试需要Mock外部API和存储
    await super.setUpMocks();
  }
}

/// 测试工具类
/// 
/// 提供测试中常用的工具方法
class TestUtils {
  TestUtils._();

  /// 等待异步操作
  static Future<void> waitForAsync([Duration? duration]) async {
    await Future.delayed(duration ?? const Duration(milliseconds: 100));
  }

  /// 创建测试数据
  static Map<String, dynamic> createTestData({
    String? id,
    Map<String, dynamic>? data,
  }) {
    final result = {
      'id': id ?? 'test-${DateTime.now().millisecondsSinceEpoch}',
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    };

    if (data != null) {
      result.addAll(data);
    }

    return result;
  }

  /// 验证日期时间
  static void expectDateTime(
    dynamic actual,
    DateTime expected, {
    Duration tolerance = const Duration(seconds: 1),
  }) {
    expect(actual, isA<DateTime>());
    final actualDateTime = actual as DateTime;
    final difference = actualDateTime.difference(expected).abs();
    expect(
      difference,
      lessThanOrEqualTo(tolerance),
      reason: 'DateTime difference is too large: ${difference.inMilliseconds}ms',
    );
  }

  /// 验证列表包含元素
  static void expectListContains<T>(
    List<T> actual,
    T expected, {
    String? reason,
  }) {
    expect(
      actual,
      contains(expected),
      reason: reason,
    );
  }

  /// 验证列表不包含元素
  static void expectListNotContains<T>(
    List<T> actual,
    T expected, {
    String? reason,
  }) {
    expect(
      actual,
      isNot(contains(expected)),
      reason: reason,
    );
  }

  /// 验证Map包含键值对
  static void expectMapContains(
    Map<String, dynamic> actual,
    String key,
    dynamic value, {
    String? reason,
  }) {
    expect(actual, containsPair(key, value), reason: reason);
  }

  /// 验证字符串匹配正则表达式
  static void expectStringMatches(
    String actual,
    RegExp pattern, {
    String? reason,
  }) {
    expect(actual, matches(pattern), reason: reason);
  }
}
