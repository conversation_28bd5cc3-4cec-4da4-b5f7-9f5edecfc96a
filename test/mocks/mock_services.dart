import 'package:mocktail/mocktail.dart';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:logger/logger.dart';
import 'package:flutter_enterprise_app/core/di/service_registry.dart';

/// Mock服务集合
/// 
/// 提供所有服务接口的Mock实现
/// 用于单元测试和集成测试

// ============================================================================
// 第三方服务Mock
// ============================================================================

/// Mock Dio客户端
class MockDio extends Mock implements Dio {}

/// Mock FlutterSecureStorage
class MockFlutterSecureStorage extends Mock implements FlutterSecureStorage {}

/// Mock HiveInterface
class MockHiveInterface extends Mock implements HiveInterface {}

/// Mock Logger
class MockLogger extends Mock implements Logger {}

/// Mock Box (Hive)
class MockBox<T> extends Mock implements Box<T> {}

// ============================================================================
// 业务服务Mock
// ============================================================================

/// Mock认证服务
class MockAuthService extends Mock implements IAuthService {}

/// Mock Token服务
class MockTokenService extends Mock implements ITokenService {}

/// Mock授权服务
class MockAuthorizationService extends Mock implements IAuthorizationService {}

/// Mock权限服务
class MockPermissionService extends Mock implements IPermissionService {}

/// Mock本地化服务
class MockLocalizationService extends Mock implements ILocalizationService {}

/// Mock分析服务
class MockAnalyticsService extends Mock implements IAnalyticsService {}

/// Mock性能服务
class MockPerformanceService extends Mock implements IPerformanceService {}

/// Mock推送通知服务
class MockPushNotificationService extends Mock implements IPushNotificationService {}

// ============================================================================
// Mock工厂类
// ============================================================================

/// Mock服务工厂
/// 
/// 提供创建Mock服务实例的便捷方法
class MockServiceFactory {
  MockServiceFactory._();

  /// 创建Mock认证服务
  static MockAuthService createMockAuthService() {
    final mock = MockAuthService();
    
    // 设置默认行为
    when(() => mock.login(any(), any())).thenAnswer((_) async => true);
    when(() => mock.logout()).thenAnswer((_) async {});
    when(() => mock.isLoggedIn()).thenAnswer((_) async => false);
    
    return mock;
  }

  /// 创建Mock Token服务
  static MockTokenService createMockTokenService() {
    final mock = MockTokenService();
    
    // 设置默认行为
    when(() => mock.getAccessToken()).thenAnswer((_) async => 'mock-token');
    when(() => mock.saveAccessToken(any())).thenAnswer((_) async {});
    when(() => mock.clearTokens()).thenAnswer((_) async {});
    
    return mock;
  }

  /// 创建Mock授权服务
  static MockAuthorizationService createMockAuthorizationService() {
    final mock = MockAuthorizationService();
    
    // 设置默认行为
    when(() => mock.hasPermission(any())).thenAnswer((_) async => true);
    when(() => mock.getUserPermissions()).thenAnswer((_) async => ['read', 'write']);
    
    return mock;
  }

  /// 创建Mock权限服务
  static MockPermissionService createMockPermissionService() {
    final mock = MockPermissionService();
    
    // 设置默认行为
    when(() => mock.requestPermission(any())).thenAnswer((_) async => true);
    when(() => mock.checkPermission(any())).thenAnswer((_) async => true);
    
    return mock;
  }

  /// 创建Mock本地化服务
  static MockLocalizationService createMockLocalizationService() {
    final mock = MockLocalizationService();
    
    // 设置默认行为
    when(() => mock.translate(any())).thenReturn('translated-text');
    when(() => mock.changeLanguage(any())).thenAnswer((_) async {});
    
    return mock;
  }

  /// 创建Mock分析服务
  static MockAnalyticsService createMockAnalyticsService() {
    final mock = MockAnalyticsService();
    
    // 设置默认行为
    when(() => mock.trackEvent(any(), any())).thenReturn(null);
    when(() => mock.setUserId(any())).thenReturn(null);
    
    return mock;
  }

  /// 创建Mock性能服务
  static MockPerformanceService createMockPerformanceService() {
    final mock = MockPerformanceService();
    
    // 设置默认行为
    when(() => mock.startTrace(any())).thenReturn(null);
    when(() => mock.stopTrace(any())).thenReturn(null);
    when(() => mock.recordMetric(any(), any())).thenReturn(null);
    
    return mock;
  }

  /// 创建Mock推送通知服务
  static MockPushNotificationService createMockPushNotificationService() {
    final mock = MockPushNotificationService();
    
    // 设置默认行为
    when(() => mock.initialize()).thenAnswer((_) async {});
    when(() => mock.getToken()).thenAnswer((_) async => 'mock-push-token');
    when(() => mock.onMessage(any())).thenReturn(null);
    
    return mock;
  }

  /// 创建Mock Dio
  static MockDio createMockDio() {
    final mock = MockDio();
    
    // 设置默认配置
    mock.options = BaseOptions(
      baseUrl: 'https://mock-api.example.com',
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
    );
    
    return mock;
  }

  /// 创建Mock FlutterSecureStorage
  static MockFlutterSecureStorage createMockSecureStorage() {
    final mock = MockFlutterSecureStorage();
    
    // 设置默认行为
    when(() => mock.read(key: any(named: 'key'))).thenAnswer((_) async => null);
    when(() => mock.write(key: any(named: 'key'), value: any(named: 'value')))
        .thenAnswer((_) async {});
    when(() => mock.delete(key: any(named: 'key'))).thenAnswer((_) async {});
    when(() => mock.deleteAll()).thenAnswer((_) async {});
    
    return mock;
  }

  /// 创建Mock HiveInterface
  static MockHiveInterface createMockHive() {
    final mock = MockHiveInterface();
    
    // 设置默认行为
    when(() => mock.openBox<dynamic>(any())).thenAnswer((_) async => MockBox<dynamic>());
    when(() => mock.isBoxOpen(any())).thenReturn(false);
    when(() => mock.close()).thenAnswer((_) async {});
    
    return mock;
  }

  /// 创建Mock Logger
  static MockLogger createMockLogger() {
    final mock = MockLogger();
    
    // 设置默认行为
    when(() => mock.d(any())).thenReturn(null);
    when(() => mock.i(any())).thenReturn(null);
    when(() => mock.w(any())).thenReturn(null);
    when(() => mock.e(any(), error: any(named: 'error'), stackTrace: any(named: 'stackTrace')))
        .thenReturn(null);
    
    return mock;
  }

  /// 根据类型创建Mock服务
  static T createMockService<T>() {
    switch (T) {
      case IAuthService:
        return createMockAuthService() as T;
      case ITokenService:
        return createMockTokenService() as T;
      case IAuthorizationService:
        return createMockAuthorizationService() as T;
      case IPermissionService:
        return createMockPermissionService() as T;
      case ILocalizationService:
        return createMockLocalizationService() as T;
      case IAnalyticsService:
        return createMockAnalyticsService() as T;
      case IPerformanceService:
        return createMockPerformanceService() as T;
      case IPushNotificationService:
        return createMockPushNotificationService() as T;
      case Dio:
        return createMockDio() as T;
      case FlutterSecureStorage:
        return createMockSecureStorage() as T;
      case HiveInterface:
        return createMockHive() as T;
      case Logger:
        return createMockLogger() as T;
      default:
        throw UnsupportedError('Mock service not available for type $T');
    }
  }
}

/// Mock数据生成器
/// 
/// 提供生成测试数据的便捷方法
class MockDataGenerator {
  MockDataGenerator._();

  /// 生成Mock用户数据
  static Map<String, dynamic> generateUserData({
    String? id,
    String? username,
    String? email,
  }) {
    return {
      'id': id ?? 'user-${DateTime.now().millisecondsSinceEpoch}',
      'username': username ?? 'testuser',
      'email': email ?? '<EMAIL>',
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    };
  }

  /// 生成Mock API响应
  static Map<String, dynamic> generateApiResponse({
    dynamic data,
    bool success = true,
    String? message,
    int statusCode = 200,
  }) {
    return {
      'success': success,
      'data': data,
      'message': message ?? (success ? 'Success' : 'Error'),
      'statusCode': statusCode,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// 生成Mock分页数据
  static Map<String, dynamic> generatePaginatedData<T>({
    required List<T> items,
    int page = 1,
    int limit = 20,
    int? total,
  }) {
    final totalCount = total ?? items.length;
    final totalPages = (totalCount / limit).ceil();

    return {
      'data': items,
      'pagination': {
        'page': page,
        'limit': limit,
        'total': totalCount,
        'totalPages': totalPages,
        'hasNextPage': page < totalPages,
        'hasPreviousPage': page > 1,
      },
    };
  }

  /// 生成Mock错误响应
  static Map<String, dynamic> generateErrorResponse({
    String? message,
    String? code,
    int statusCode = 400,
    Map<String, dynamic>? details,
  }) {
    return {
      'success': false,
      'error': {
        'message': message ?? 'An error occurred',
        'code': code ?? 'UNKNOWN_ERROR',
        'statusCode': statusCode,
        'details': details,
      },
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}
