import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_enterprise_app/core/config/feature_config.dart';
import 'package:flutter_enterprise_app/core/config/environment_config.dart';
import 'package:flutter_enterprise_app/core/constants/feature_constants.dart';

/// 基础架构测试
/// 
/// 验证核心组件的基本功能
void main() {
  group('Basic Architecture Tests', () {
    tearDown(() async {
      // 清理测试环境
      try {
        FeatureConfig.instance.dispose();
      } catch (e) {
        // 忽略清理错误
      }
      
      try {
        EnvironmentConfig.instance.dispose();
      } catch (e) {
        // 忽略清理错误
      }
    });

    test('FeatureConfig should initialize correctly', () async {
      // Arrange
      final config = {
        'features': {
          Features.authentication: true,
          Features.authorization: false,
        },
      };

      // Act
      await FeatureConfig.initialize(config);

      // Assert
      expect(FeatureConfig.instance.isFeatureEnabled(Features.authentication), isTrue);
      expect(FeatureConfig.instance.isFeatureEnabled(Features.authorization), isFalse);
    });

    test('EnvironmentConfig should initialize correctly', () async {
      // Act
      await EnvironmentConfig.initialize('test');

      // Assert
      expect(EnvironmentConfig.instance.environment, equals('test'));
      expect(EnvironmentConfig.instance.isTest, isTrue);
    });

    test('Features constants should be defined', () {
      // Assert
      expect(Features.authentication, isNotEmpty);
      expect(Features.authorization, isNotEmpty);
      expect(Features.allFeatures, isNotEmpty);
      expect(Features.allFeatures, contains(Features.authentication));
      expect(Features.allFeatures, contains(Features.authorization));
    });

    test('Feature groups should be properly organized', () {
      // Act
      final groups = Features.getFeatureGroups();

      // Assert
      expect(groups, isA<Map<String, List<String>>>());
      expect(groups.containsKey('core'), isTrue);
      expect(groups['core'], contains(Features.authentication));
      expect(groups['core'], contains(Features.authorization));
    });

    test('Core features should be identified correctly', () {
      // Assert
      expect(Features.isCoreFeature(Features.authentication), isTrue);
      expect(Features.isCoreFeature(Features.authorization), isTrue);
      expect(Features.isCoreFeature(Features.analytics), isFalse);
    });

    test('Development features should be identified correctly', () {
      // Assert
      expect(Features.isDevelopmentFeature(Features.devTools), isTrue);
      expect(Features.isDevelopmentFeature(Features.debugFeatures), isTrue);
      expect(Features.isDevelopmentFeature(Features.authentication), isFalse);
    });

    test('Production features should be identified correctly', () {
      // Assert
      expect(Features.isProductionFeature(Features.analytics), isTrue);
      expect(Features.isProductionFeature(Features.performanceMonitoring), isTrue);
      expect(Features.isProductionFeature(Features.devTools), isFalse);
    });

    test('Environment enum should convert strings correctly', () {
      // Assert
      expect(Environment.fromString('dev'), equals(Environment.development));
      expect(Environment.fromString('test'), equals(Environment.test));
      expect(Environment.fromString('prod'), equals(Environment.production));
      expect(Environment.fromString('unknown'), equals(Environment.development));
    });

    test('FeatureConfig should handle dependencies', () async {
      // Arrange
      final config = {
        'features': {
          Features.authentication: true,
          Features.authorization: true,
        },
        'dependencies': {
          Features.authorization: {
            'requires': [Features.authentication],
          },
        },
      };

      // Act
      await FeatureConfig.initialize(config);

      // Assert
      expect(FeatureConfig.instance.isFeatureEnabled(Features.authentication), isTrue);
      expect(FeatureConfig.instance.isFeatureEnabled(Features.authorization), isTrue);
      
      final dependencies = FeatureConfig.instance.getFeatureDependencies(Features.authorization);
      expect(dependencies, contains(Features.authentication));
    });

    test('FeatureConfig should prevent enabling feature without dependencies', () async {
      // Arrange
      final config = {
        'features': {
          Features.authentication: false,
          Features.authorization: false,
        },
        'dependencies': {
          Features.authorization: {
            'requires': [Features.authentication],
          },
        },
      };
      await FeatureConfig.initialize(config);

      // Act
      final result = await FeatureConfig.instance.enableFeature(Features.authorization);

      // Assert
      expect(result, isFalse);
      expect(FeatureConfig.instance.isFeatureEnabled(Features.authorization), isFalse);
    });

    test('EnvironmentConfig should provide default values', () async {
      // Arrange
      await EnvironmentConfig.initialize('test');

      // Act & Assert
      expect(EnvironmentConfig.instance.getString('nonexistent', 'default'), equals('default'));
      expect(EnvironmentConfig.instance.getInt('nonexistent', 42), equals(42));
      expect(EnvironmentConfig.instance.getBool('nonexistent', true), isTrue);
      expect(EnvironmentConfig.instance.getDouble('nonexistent', 3.14), equals(3.14));
    });
  });
}
