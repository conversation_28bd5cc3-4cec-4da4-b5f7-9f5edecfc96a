import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_enterprise_app/core/di/conditional_di.dart';
import 'package:flutter_enterprise_app/core/config/feature_config.dart';
import 'package:flutter_enterprise_app/core/constants/feature_constants.dart';
import '../../test_base.dart';
import '../../mocks/mock_services.dart';

/// ConditionalDI测试
class ConditionalDITest extends UnitTestBase {
  late ConditionalDI conditionalDI;
  late GetIt getIt;

  @override
  Future<void> setUpCustom() async {
    conditionalDI = ConditionalDI.instance;
    getIt = GetIt.instance;
  }

  void runTests() {
    testGroup('ConditionalDI', () {
      test('should register conditional service with feature enabled', () async {
        // Arrange
        await FeatureConfig.initialize({
          'features': {Features.authentication: true},
        });

        // Act
        conditionalDI.registerConditionalLazySingleton<IAuthService>(
          featureName: Features.authentication,
          activeFactory: () => MockServiceFactory.createMockAuthService(),
          noOpFactory: () => MockServiceFactory.createMockAuthService(),
        );

        // Assert
        expect(getIt.isRegistered<IAuthService>(), isTrue);
        final service = getIt.get<IAuthService>();
        expect(service, isA<MockAuthService>());
      });

      test('should register NoOp service with feature disabled', () async {
        // Arrange
        await FeatureConfig.initialize({
          'features': {Features.authentication: false},
        });

        // Act
        conditionalDI.registerConditionalLazySingleton<IAuthService>(
          featureName: Features.authentication,
          activeFactory: () => MockServiceFactory.createMockAuthService(),
          noOpFactory: () => MockServiceFactory.createMockAuthService(),
        );

        // Assert
        expect(getIt.isRegistered<IAuthService>(), isTrue);
        final service = getIt.get<IAuthService>();
        expect(service, isA<MockAuthService>());
      });

      test('should register conditional singleton service', () async {
        // Arrange
        await FeatureConfig.initialize({
          'features': {Features.analytics: true},
        });

        // Act
        conditionalDI.registerConditionalSingleton<IAnalyticsService>(
          featureName: Features.analytics,
          activeFactory: () => MockServiceFactory.createMockAnalyticsService(),
          noOpFactory: () => MockServiceFactory.createMockAnalyticsService(),
        );

        // Assert
        expect(getIt.isRegistered<IAnalyticsService>(), isTrue);
        final service1 = getIt.get<IAnalyticsService>();
        final service2 = getIt.get<IAnalyticsService>();
        expect(identical(service1, service2), isTrue); // Same instance for singleton
      });

      test('should register conditional factory service', () async {
        // Arrange
        await FeatureConfig.initialize({
          'features': {Features.performanceMonitoring: true},
        });

        // Act
        conditionalDI.registerConditionalFactory<IPerformanceService>(
          featureName: Features.performanceMonitoring,
          activeFactory: () => MockServiceFactory.createMockPerformanceService(),
          noOpFactory: () => MockServiceFactory.createMockPerformanceService(),
        );

        // Assert
        expect(getIt.isRegistered<IPerformanceService>(), isTrue);
        final service1 = getIt.get<IPerformanceService>();
        final service2 = getIt.get<IPerformanceService>();
        expect(identical(service1, service2), isFalse); // Different instances for factory
      });

      test('should register service with instance name', () async {
        // Arrange
        await FeatureConfig.initialize({
          'features': {Features.authentication: true},
        });

        // Act
        conditionalDI.registerConditionalLazySingleton<IAuthService>(
          featureName: Features.authentication,
          activeFactory: () => MockServiceFactory.createMockAuthService(),
          noOpFactory: () => MockServiceFactory.createMockAuthService(),
          instanceName: 'primary',
        );

        // Assert
        expect(getIt.isRegistered<IAuthService>(instanceName: 'primary'), isTrue);
        final service = getIt.get<IAuthService>(instanceName: 'primary');
        expect(service, isA<MockAuthService>());
      });

      test('should refresh feature services when feature config changes', () async {
        // Arrange
        await FeatureConfig.initialize({
          'features': {Features.authentication: false},
        });

        conditionalDI.registerConditionalLazySingleton<IAuthService>(
          featureName: Features.authentication,
          activeFactory: () => MockServiceFactory.createMockAuthService(),
          noOpFactory: () => MockServiceFactory.createMockAuthService(),
        );

        // Act - Enable feature
        await FeatureConfig.instance.enableFeature(Features.authentication);
        await conditionalDI.refreshFeatureServices(Features.authentication);

        // Assert
        expect(getIt.isRegistered<IAuthService>(), isTrue);
        final service = getIt.get<IAuthService>();
        expect(service, isA<MockAuthService>());
      });

      test('should refresh all conditional services', () async {
        // Arrange
        await FeatureConfig.initialize({
          'features': {
            Features.authentication: false,
            Features.analytics: false,
          },
        });

        conditionalDI.registerConditionalLazySingleton<IAuthService>(
          featureName: Features.authentication,
          activeFactory: () => MockServiceFactory.createMockAuthService(),
          noOpFactory: () => MockServiceFactory.createMockAuthService(),
        );

        conditionalDI.registerConditionalLazySingleton<IAnalyticsService>(
          featureName: Features.analytics,
          activeFactory: () => MockServiceFactory.createMockAnalyticsService(),
          noOpFactory: () => MockServiceFactory.createMockAnalyticsService(),
        );

        // Act - Enable features
        await FeatureConfig.instance.enableFeature(Features.authentication);
        await FeatureConfig.instance.enableFeature(Features.analytics);
        await conditionalDI.refreshAllConditionalServices();

        // Assert
        expect(getIt.isRegistered<IAuthService>(), isTrue);
        expect(getIt.isRegistered<IAnalyticsService>(), isTrue);
      });

      test('should get feature service status', () async {
        // Arrange
        await FeatureConfig.initialize({
          'features': {Features.authentication: true},
        });

        conditionalDI.registerConditionalLazySingleton<IAuthService>(
          featureName: Features.authentication,
          activeFactory: () => MockServiceFactory.createMockAuthService(),
          noOpFactory: () => MockServiceFactory.createMockAuthService(),
        );

        // Act
        final status = conditionalDI.getFeatureServiceStatus(Features.authentication);

        // Assert
        expect(status, isA<Map<String, bool>>());
        expect(status.isNotEmpty, isTrue);
      });

      test('should get all service status', () async {
        // Arrange
        await FeatureConfig.initialize({
          'features': {
            Features.authentication: true,
            Features.analytics: false,
          },
        });

        conditionalDI.registerConditionalLazySingleton<IAuthService>(
          featureName: Features.authentication,
          activeFactory: () => MockServiceFactory.createMockAuthService(),
          noOpFactory: () => MockServiceFactory.createMockAuthService(),
        );

        conditionalDI.registerConditionalLazySingleton<IAnalyticsService>(
          featureName: Features.analytics,
          activeFactory: () => MockServiceFactory.createMockAnalyticsService(),
          noOpFactory: () => MockServiceFactory.createMockAnalyticsService(),
        );

        // Act
        final allStatus = conditionalDI.getAllServiceStatus();

        // Assert
        expect(allStatus, isA<Map<String, Map<String, bool>>>());
        expect(allStatus.containsKey(Features.authentication), isTrue);
        expect(allStatus.containsKey(Features.analytics), isTrue);
      });

      test('should handle service lifetime correctly', () async {
        // Arrange
        await FeatureConfig.initialize({
          'features': {Features.authentication: true},
        });

        // Act - Register different service lifetimes
        conditionalDI.registerConditionalService<IAuthService>(
          featureName: Features.authentication,
          serviceType: IAuthService,
          activeFactory: () => MockServiceFactory.createMockAuthService(),
          noOpFactory: () => MockServiceFactory.createMockAuthService(),
          lifetime: ServiceLifetime.singleton,
        );

        // Assert
        expect(getIt.isRegistered<IAuthService>(), isTrue);
      });

      test('should use ConditionalDIExtension', () async {
        // Arrange
        await FeatureConfig.initialize({
          'features': {Features.authentication: true},
        });

        // Act
        getIt.registerConditional<IAuthService>(
          featureName: Features.authentication,
          activeFactory: () => MockServiceFactory.createMockAuthService(),
          noOpFactory: () => MockServiceFactory.createMockAuthService(),
        );

        // Assert
        expect(getIt.isRegistered<IAuthService>(), isTrue);
        final service = getIt.get<IAuthService>();
        expect(service, isA<MockAuthService>());
      });

      test('should handle disposal correctly', () async {
        // Arrange
        await FeatureConfig.initialize({
          'features': {Features.authentication: true},
        });

        conditionalDI.registerConditionalLazySingleton<IAuthService>(
          featureName: Features.authentication,
          activeFactory: () => MockServiceFactory.createMockAuthService(),
          noOpFactory: () => MockServiceFactory.createMockAuthService(),
        );

        // Act
        await conditionalDI.dispose();

        // Assert - After disposal, services should be unregistered
        // Note: This test verifies the disposal process completes without error
        expect(true, isTrue); // Placeholder assertion
      });
    });
  }
}

void main() {
  ConditionalDITest().runTests();
}
