import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_enterprise_app/core/config/environment_config.dart';
import '../../test_base.dart';

/// EnvironmentConfig测试
class EnvironmentConfigTest extends UnitTestBase {
  void runTests() {
    testGroup('EnvironmentConfig', () {
      test('should initialize with test environment', () async {
        // Act
        await EnvironmentConfig.initialize('test');

        // Assert
        expect(EnvironmentConfig.instance.environment, equals('test'));
        expect(EnvironmentConfig.instance.isTest, isTrue);
        expect(EnvironmentConfig.instance.isDevelopment, isFalse);
        expect(EnvironmentConfig.instance.isProduction, isFalse);
      });

      test('should identify development environment correctly', () async {
        // Act
        await EnvironmentConfig.initialize('dev');

        // Assert
        expect(EnvironmentConfig.instance.environment, equals('dev'));
        expect(EnvironmentConfig.instance.isDevelopment, isTrue);
        expect(EnvironmentConfig.instance.isTest, isFalse);
        expect(EnvironmentConfig.instance.isProduction, isFalse);
      });

      test('should identify production environment correctly', () async {
        // Act
        await EnvironmentConfig.initialize('prod');

        // Assert
        expect(EnvironmentConfig.instance.environment, equals('prod'));
        expect(EnvironmentConfig.instance.isProduction, isTrue);
        expect(EnvironmentConfig.instance.isDevelopment, isFalse);
        expect(EnvironmentConfig.instance.isTest, isFalse);
      });

      test('should get string value with default', () async {
        // Arrange
        await EnvironmentConfig.initialize('test');

        // Act
        final value = EnvironmentConfig.instance.getString('nonexistent.key', 'default');

        // Assert
        expect(value, equals('default'));
      });

      test('should get int value with default', () async {
        // Arrange
        await EnvironmentConfig.initialize('test');

        // Act
        final value = EnvironmentConfig.instance.getInt('nonexistent.key', 42);

        // Assert
        expect(value, equals(42));
      });

      test('should get double value with default', () async {
        // Arrange
        await EnvironmentConfig.initialize('test');

        // Act
        final value = EnvironmentConfig.instance.getDouble('nonexistent.key', 3.14);

        // Assert
        expect(value, equals(3.14));
      });

      test('should get bool value with default', () async {
        // Arrange
        await EnvironmentConfig.initialize('test');

        // Act
        final value = EnvironmentConfig.instance.getBool('nonexistent.key', true);

        // Assert
        expect(value, isTrue);
      });

      test('should get list value with default', () async {
        // Arrange
        await EnvironmentConfig.initialize('test');

        // Act
        final value = EnvironmentConfig.instance.getList<String>('nonexistent.key', ['default']);

        // Assert
        expect(value, equals(['default']));
      });

      test('should get map value with default', () async {
        // Arrange
        await EnvironmentConfig.initialize('test');

        // Act
        final value = EnvironmentConfig.instance.getMap<String>('nonexistent.key', {'key': 'value'});

        // Assert
        expect(value, equals({'key': 'value'}));
      });

      test('should check if key exists', () async {
        // Arrange
        await EnvironmentConfig.initialize('test');

        // Act & Assert
        expect(EnvironmentConfig.instance.hasKey('nonexistent.key'), isFalse);
      });

      test('should get all config', () async {
        // Arrange
        await EnvironmentConfig.initialize('test');

        // Act
        final config = EnvironmentConfig.instance.getAllConfig();

        // Assert
        expect(config, isA<Map<String, dynamic>>());
      });

      test('should get environment specific config', () async {
        // Arrange
        await EnvironmentConfig.initialize('test');

        // Act
        final envConfig = EnvironmentConfig.instance.getEnvironmentConfig();

        // Assert
        expect(envConfig, isA<Map<String, dynamic>>());
      });

      test('should have default API configuration', () async {
        // Arrange
        await EnvironmentConfig.initialize('test');

        // Act & Assert
        expect(EnvironmentConfig.instance.apiBaseUrl, isNotEmpty);
        expect(EnvironmentConfig.instance.apiTimeout, greaterThan(0));
      });

      test('should have default database configuration', () async {
        // Arrange
        await EnvironmentConfig.initialize('test');

        // Act & Assert
        expect(EnvironmentConfig.instance.databaseName, isNotEmpty);
      });

      test('should have default logging configuration', () async {
        // Arrange
        await EnvironmentConfig.initialize('test');

        // Act & Assert
        expect(EnvironmentConfig.instance.logLevel, isNotEmpty);
      });

      test('should enable debug mode in development', () async {
        // Arrange
        await EnvironmentConfig.initialize('dev');

        // Act & Assert
        // Note: isDebugMode depends on kDebugMode which is false in tests
        // This test verifies the logic structure
        expect(EnvironmentConfig.instance.isDevelopment, isTrue);
      });

      test('should update config dynamically', () async {
        // Arrange
        await EnvironmentConfig.initialize('test');
        final events = <EnvironmentConfigChangeEvent>[];
        EnvironmentConfig.instance.onConfigChanged.listen(events.add);

        // Act
        await EnvironmentConfig.instance.updateConfig('test.key', 'test.value');

        // Assert
        expect(EnvironmentConfig.instance.getString('test.key'), equals('test.value'));
        expect(events, hasLength(1));
        expect(events.first.key, equals('test.key'));
        expect(events.first.newValue, equals('test.value'));
      });

      test('should update nested config', () async {
        // Arrange
        await EnvironmentConfig.initialize('test');

        // Act
        await EnvironmentConfig.instance.updateConfig('nested.deep.key', 'nested.value');

        // Assert
        expect(EnvironmentConfig.instance.getString('nested.deep.key'), equals('nested.value'));
      });

      test('should emit change events on config update', () async {
        // Arrange
        await EnvironmentConfig.initialize('test');
        final events = <EnvironmentConfigChangeEvent>[];
        EnvironmentConfig.instance.onConfigChanged.listen(events.add);

        // Act
        await EnvironmentConfig.instance.updateConfig('event.test', 'event.value');

        // Assert
        expect(events, hasLength(1));
        final event = events.first;
        expect(event.key, equals('event.test'));
        expect(event.oldValue, isNull);
        expect(event.newValue, equals('event.value'));
      });

      test('should handle config reload', () async {
        // Arrange
        await EnvironmentConfig.initialize('test');
        final events = <EnvironmentConfigChangeEvent>[];
        EnvironmentConfig.instance.onConfigChanged.listen(events.add);

        // Act
        await EnvironmentConfig.instance.reload();

        // Assert
        expect(events, hasLength(1));
        expect(events.first.key, equals('__reload__'));
      });

      test('should parse string to bool correctly', () async {
        // Arrange
        await EnvironmentConfig.initialize('test');
        await EnvironmentConfig.instance.updateConfig('bool.true', 'true');
        await EnvironmentConfig.instance.updateConfig('bool.false', 'false');
        await EnvironmentConfig.instance.updateConfig('bool.one', '1');
        await EnvironmentConfig.instance.updateConfig('bool.zero', '0');

        // Act & Assert
        expect(EnvironmentConfig.instance.getBool('bool.true'), isTrue);
        expect(EnvironmentConfig.instance.getBool('bool.false'), isFalse);
        expect(EnvironmentConfig.instance.getBool('bool.one'), isTrue);
        expect(EnvironmentConfig.instance.getBool('bool.zero'), isFalse);
      });

      test('should parse string to int correctly', () async {
        // Arrange
        await EnvironmentConfig.initialize('test');
        await EnvironmentConfig.instance.updateConfig('int.valid', '42');
        await EnvironmentConfig.instance.updateConfig('int.invalid', 'not-a-number');

        // Act & Assert
        expect(EnvironmentConfig.instance.getInt('int.valid'), equals(42));
        expect(EnvironmentConfig.instance.getInt('int.invalid', 0), equals(0));
      });

      test('should parse string to double correctly', () async {
        // Arrange
        await EnvironmentConfig.initialize('test');
        await EnvironmentConfig.instance.updateConfig('double.valid', '3.14');
        await EnvironmentConfig.instance.updateConfig('double.invalid', 'not-a-number');

        // Act & Assert
        expect(EnvironmentConfig.instance.getDouble('double.valid'), equals(3.14));
        expect(EnvironmentConfig.instance.getDouble('double.invalid', 0.0), equals(0.0));
      });

      test('should handle Environment enum conversion', () {
        // Act & Assert
        expect(Environment.fromString('dev'), equals(Environment.development));
        expect(Environment.fromString('development'), equals(Environment.development));
        expect(Environment.fromString('test'), equals(Environment.test));
        expect(Environment.fromString('testing'), equals(Environment.test));
        expect(Environment.fromString('prod'), equals(Environment.production));
        expect(Environment.fromString('production'), equals(Environment.production));
        expect(Environment.fromString('unknown'), equals(Environment.development));
      });
    });
  }
}

void main() {
  EnvironmentConfigTest().runTests();
}
