import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_enterprise_app/core/config/feature_config.dart';
import 'package:flutter_enterprise_app/core/constants/feature_constants.dart';
import '../../test_base.dart';

/// FeatureConfig测试
class FeatureConfigTest extends UnitTestBase {
  void runTests() {
    testGroup('FeatureConfig', () {
      test('should initialize with default config', () async {
        // Arrange
        final config = {
          'features': {
            Features.authentication: true,
            Features.authorization: false,
          },
        };

        // Act
        await FeatureConfig.initialize(config);

        // Assert
        expect(FeatureConfig.instance.isFeatureEnabled(Features.authentication), isTrue);
        expect(FeatureConfig.instance.isFeatureEnabled(Features.authorization), isFalse);
      });

      test('should handle empty config', () async {
        // Act
        await FeatureConfig.initialize({});

        // Assert
        expect(FeatureConfig.instance.isFeatureEnabled(Features.authentication), isFalse);
        expect(FeatureConfig.instance.isFeatureEnabled(Features.authorization), isFalse);
      });

      test('should validate dependencies correctly', () async {
        // Arrange
        final config = {
          'features': {
            Features.authentication: true,
            Features.authorization: true,
          },
          'dependencies': {
            Features.authorization: {
              'requires': [Features.authentication],
            },
          },
        };

        // Act
        await FeatureConfig.initialize(config);

        // Assert
        expect(FeatureConfig.instance.isFeatureEnabled(Features.authentication), isTrue);
        expect(FeatureConfig.instance.isFeatureEnabled(Features.authorization), isTrue);
      });

      test('should disable dependent feature when dependency is disabled', () async {
        // Arrange
        final config = {
          'features': {
            Features.authentication: false,
            Features.authorization: true,
          },
          'dependencies': {
            Features.authorization: {
              'requires': [Features.authentication],
            },
          },
        };

        // Act
        await FeatureConfig.initialize(config);

        // Assert
        expect(FeatureConfig.instance.isFeatureEnabled(Features.authentication), isFalse);
        expect(FeatureConfig.instance.isFeatureEnabled(Features.authorization), isFalse);
      });

      test('should enable feature dynamically', () async {
        // Arrange
        final config = {
          'features': {
            Features.authentication: true,
            Features.authorization: false,
          },
        };
        await FeatureConfig.initialize(config);

        // Act
        final result = await FeatureConfig.instance.enableFeature(Features.authorization);

        // Assert
        expect(result, isTrue);
        expect(FeatureConfig.instance.isFeatureEnabled(Features.authorization), isTrue);
      });

      test('should fail to enable feature when dependency is not met', () async {
        // Arrange
        final config = {
          'features': {
            Features.authentication: false,
            Features.authorization: false,
          },
          'dependencies': {
            Features.authorization: {
              'requires': [Features.authentication],
            },
          },
        };
        await FeatureConfig.initialize(config);

        // Act
        final result = await FeatureConfig.instance.enableFeature(Features.authorization);

        // Assert
        expect(result, isFalse);
        expect(FeatureConfig.instance.isFeatureEnabled(Features.authorization), isFalse);
      });

      test('should disable feature dynamically', () async {
        // Arrange
        final config = {
          'features': {
            Features.authentication: true,
            Features.authorization: true,
          },
        };
        await FeatureConfig.initialize(config);

        // Act
        final result = await FeatureConfig.instance.disableFeature(Features.authorization);

        // Assert
        expect(result, isTrue);
        expect(FeatureConfig.instance.isFeatureEnabled(Features.authorization), isFalse);
      });

      test('should fail to disable feature when other features depend on it', () async {
        // Arrange
        final config = {
          'features': {
            Features.authentication: true,
            Features.authorization: true,
          },
          'dependencies': {
            Features.authorization: {
              'requires': [Features.authentication],
            },
          },
        };
        await FeatureConfig.initialize(config);

        // Act
        final result = await FeatureConfig.instance.disableFeature(Features.authentication);

        // Assert
        expect(result, isFalse);
        expect(FeatureConfig.instance.isFeatureEnabled(Features.authentication), isTrue);
      });

      test('should get enabled features list', () async {
        // Arrange
        final config = {
          'features': {
            Features.authentication: true,
            Features.authorization: false,
            Features.analytics: true,
          },
        };
        await FeatureConfig.initialize(config);

        // Act
        final enabledFeatures = FeatureConfig.instance.getEnabledFeatures();

        // Assert
        expect(enabledFeatures, contains(Features.authentication));
        expect(enabledFeatures, contains(Features.analytics));
        expect(enabledFeatures, isNot(contains(Features.authorization)));
      });

      test('should get disabled features list', () async {
        // Arrange
        final config = {
          'features': {
            Features.authentication: true,
            Features.authorization: false,
          },
        };
        await FeatureConfig.initialize(config);

        // Act
        final disabledFeatures = FeatureConfig.instance.getDisabledFeatures();

        // Assert
        expect(disabledFeatures, contains(Features.authorization));
        expect(disabledFeatures, isNot(contains(Features.authentication)));
      });

      test('should get feature dependencies', () async {
        // Arrange
        final config = {
          'features': {
            Features.authentication: true,
            Features.authorization: true,
          },
          'dependencies': {
            Features.authorization: {
              'requires': [Features.authentication],
            },
          },
        };
        await FeatureConfig.initialize(config);

        // Act
        final dependencies = FeatureConfig.instance.getFeatureDependencies(Features.authorization);

        // Assert
        expect(dependencies, contains(Features.authentication));
      });

      test('should emit change events when feature is enabled', () async {
        // Arrange
        final config = {
          'features': {
            Features.authentication: true,
            Features.authorization: false,
          },
        };
        await FeatureConfig.initialize(config);

        final events = <FeatureConfigChangeEvent>[];
        FeatureConfig.instance.onConfigChanged.listen(events.add);

        // Act
        await FeatureConfig.instance.enableFeature(Features.authorization);

        // Assert
        expect(events, hasLength(1));
        expect(events.first.featureName, equals(Features.authorization));
        expect(events.first.isEnabled, isTrue);
        expect(events.first.changeType, equals(FeatureChangeType.enabled));
      });

      test('should emit change events when feature is disabled', () async {
        // Arrange
        final config = {
          'features': {
            Features.authentication: true,
            Features.authorization: true,
          },
        };
        await FeatureConfig.initialize(config);

        final events = <FeatureConfigChangeEvent>[];
        FeatureConfig.instance.onConfigChanged.listen(events.add);

        // Act
        await FeatureConfig.instance.disableFeature(Features.authorization);

        // Assert
        expect(events, hasLength(1));
        expect(events.first.featureName, equals(Features.authorization));
        expect(events.first.isEnabled, isFalse);
        expect(events.first.changeType, equals(FeatureChangeType.disabled));
      });

      test('should throw exception for circular dependencies', () async {
        // Arrange
        final config = {
          'features': {
            'feature_a': true,
            'feature_b': true,
          },
          'dependencies': {
            'feature_a': {
              'requires': ['feature_b'],
            },
            'feature_b': {
              'requires': ['feature_a'],
            },
          },
        };

        // Act & Assert
        expect(
          () => FeatureConfig.initialize(config),
          throwsA(isA<FeatureConfigurationException>()),
        );
      });

      test('should throw exception for unknown dependencies', () async {
        // Arrange
        final config = {
          'features': {
            Features.authorization: true,
          },
          'dependencies': {
            Features.authorization: {
              'requires': ['unknown_feature'],
            },
          },
        };

        // Act & Assert
        expect(
          () => FeatureConfig.initialize(config),
          throwsA(isA<FeatureConfigurationException>()),
        );
      });

      test('should cache feature status for performance', () async {
        // Arrange
        final config = {
          'features': {
            Features.authentication: true,
          },
        };
        await FeatureConfig.initialize(config);

        // Act - 多次调用同一个功能检查
        final result1 = FeatureConfig.instance.isFeatureEnabled(Features.authentication);
        final result2 = FeatureConfig.instance.isFeatureEnabled(Features.authentication);
        final result3 = FeatureConfig.instance.isFeatureEnabled(Features.authentication);

        // Assert
        expect(result1, isTrue);
        expect(result2, isTrue);
        expect(result3, isTrue);
      });
    });
  }
}

void main() {
  FeatureConfigTest().runTests();
}
