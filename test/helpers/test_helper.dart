import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';
import 'package:flutter_enterprise_app/core/config/feature_config.dart';
import 'package:flutter_enterprise_app/core/config/environment_config.dart';
import 'package:flutter_enterprise_app/core/di/injection.dart';

/// 测试辅助工具类
/// 
/// 提供测试环境的初始化、清理和通用工具方法
class TestHelper {
  TestHelper._();

  static final GetIt _getIt = GetIt.instance;

  /// 初始化测试环境
  static Future<void> initializeTest({
    Map<String, dynamic>? featureConfig,
    String environment = 'test',
  }) async {
    // 重置GetIt容器
    await _getIt.reset();

    // 初始化功能配置
    await FeatureConfig.initialize(featureConfig ?? _getDefaultTestFeatureConfig());

    // 初始化环境配置
    await EnvironmentConfig.initialize(environment);

    // 注册测试专用的Mock服务
    await _registerTestServices();
  }

  /// 清理测试环境
  static Future<void> cleanupTest() async {
    // 清理GetIt容器
    await _getIt.reset();

    // 清理功能配置
    try {
      FeatureConfig.instance.dispose();
    } catch (e) {
      // 忽略清理错误
    }

    // 清理环境配置
    try {
      EnvironmentConfig.instance.dispose();
    } catch (e) {
      // 忽略清理错误
    }
  }

  /// 获取默认测试功能配置
  static Map<String, dynamic> _getDefaultTestFeatureConfig() {
    return {
      'features': {
        'authentication': true,
        'authorization': true,
        'internationalization': true,
        'analytics': false,
        'performance_monitoring': false,
        'push_notifications': false,
        'dev_tools': true,
        'debug_features': true,
        'testing_tools': true,
        'mock_data': true,
      },
      'dependencies': {
        'authorization': {
          'requires': ['authentication'],
        },
        'analytics': {
          'requires': ['authentication'],
        },
        'push_notifications': {
          'requires': ['authentication'],
        },
      },
    };
  }

  /// 注册测试服务
  static Future<void> _registerTestServices() async {
    // 这里可以注册测试专用的服务
    // 例如：Mock数据服务、测试工具等
  }

  /// 创建测试用的功能配置
  static Future<void> setupFeatureConfig({
    required Map<String, bool> features,
    Map<String, List<String>>? dependencies,
  }) async {
    final config = {
      'features': features,
      'dependencies': dependencies ?? {},
    };
    await FeatureConfig.initialize(config);
  }

  /// 验证服务是否已注册
  static bool isServiceRegistered<T extends Object>({String? instanceName}) {
    return _getIt.isRegistered<T>(instanceName: instanceName);
  }

  /// 获取服务实例
  static T getService<T extends Object>({String? instanceName}) {
    return _getIt.get<T>(instanceName: instanceName);
  }

  /// 注册Mock服务
  static void registerMockService<T extends Object>(
    T mockService, {
    String? instanceName,
  }) {
    _getIt.registerSingleton<T>(
      mockService,
      instanceName: instanceName,
    );
  }

  /// 注册Mock工厂服务
  static void registerMockFactory<T extends Object>(
    T Function() factory, {
    String? instanceName,
  }) {
    _getIt.registerFactory<T>(
      factory,
      instanceName: instanceName,
    );
  }

  /// 创建测试组
  static void testGroup(
    String description,
    void Function() body, {
    bool skip = false,
    String? skipReason,
  }) {
    group(description, () {
      setUp(() async {
        await initializeTest();
      });

      tearDown(() async {
        await cleanupTest();
      });

      body();
    }, skip: skip, skipReason: skipReason);
  }

  /// 创建异步测试组
  static void asyncTestGroup(
    String description,
    Future<void> Function() body, {
    bool skip = false,
    String? skipReason,
  }) {
    group(description, () {
      setUp(() async {
        await initializeTest();
      });

      tearDown(() async {
        await cleanupTest();
      });

      test('async test', () async {
        await body();
      });
    }, skip: skip, skipReason: skipReason);
  }

  /// 验证异常
  static void expectException<T extends Exception>(
    void Function() callback, {
    String? message,
  }) {
    expect(
      callback,
      throwsA(isA<T>()),
      reason: message,
    );
  }

  /// 验证异步异常
  static Future<void> expectAsyncException<T extends Exception>(
    Future<void> Function() callback, {
    String? message,
  }) async {
    expect(
      callback,
      throwsA(isA<T>()),
      reason: message,
    );
  }

  /// 等待异步操作完成
  static Future<void> waitForAsync([Duration? timeout]) async {
    await Future.delayed(timeout ?? const Duration(milliseconds: 100));
  }

  /// 创建测试数据
  static Map<String, dynamic> createTestData({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? additionalData,
  }) {
    final data = {
      'id': id ?? 'test-id-${DateTime.now().millisecondsSinceEpoch}',
      'createdAt': (createdAt ?? DateTime.now()).toIso8601String(),
      'updatedAt': (updatedAt ?? DateTime.now()).toIso8601String(),
    };

    if (additionalData != null) {
      data.addAll(additionalData);
    }

    return data;
  }

  /// 创建分页测试数据
  static Map<String, dynamic> createPaginatedTestData<T>({
    required List<T> data,
    int page = 1,
    int limit = 20,
    int? total,
  }) {
    final totalCount = total ?? data.length;
    final totalPages = (totalCount / limit).ceil();

    return {
      'data': data,
      'total': totalCount,
      'page': page,
      'limit': limit,
      'totalPages': totalPages,
      'hasNextPage': page < totalPages,
      'hasPreviousPage': page > 1,
    };
  }

  /// 验证分页数据
  static void expectPaginatedData(
    dynamic actual,
    List<dynamic> expectedData, {
    int expectedPage = 1,
    int expectedLimit = 20,
    int? expectedTotal,
  }) {
    expect(actual, isA<Map<String, dynamic>>());
    final map = actual as Map<String, dynamic>;

    expect(map['data'], equals(expectedData));
    expect(map['page'], equals(expectedPage));
    expect(map['limit'], equals(expectedLimit));
    
    if (expectedTotal != null) {
      expect(map['total'], equals(expectedTotal));
    }
  }
}

/// 测试基础类
/// 
/// 所有测试类都应该继承此类
abstract class TestBase {
  /// 设置测试
  Future<void> setUp() async {
    await TestHelper.initializeTest();
  }

  /// 清理测试
  Future<void> tearDown() async {
    await TestHelper.cleanupTest();
  }

  /// 创建测试组
  void testGroup(String description, void Function() body) {
    TestHelper.testGroup(description, body);
  }

  /// 验证异常
  void expectException<T extends Exception>(
    void Function() callback, {
    String? message,
  }) {
    TestHelper.expectException<T>(callback, message: message);
  }

  /// 验证异步异常
  Future<void> expectAsyncException<T extends Exception>(
    Future<void> Function() callback, {
    String? message,
  }) async {
    await TestHelper.expectAsyncException<T>(callback, message: message);
  }
}
