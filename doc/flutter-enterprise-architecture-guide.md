# 企业级Flutter应用统一架构方案 (v3.0)
## 面向99%跨平台开发场景的完整解决方案

### 文档概述

本方案旨在构建一个能够覆盖99%跨平台开发需求的企业级Flutter架构，让开发团队能够专注于业务逻辑和业务UI开发，而无需关心底层技术细节。架构设计遵循**高内聚、低耦合、UI与业务逻辑彻底分离**的原则，并具备优秀的可扩展性和可维护性。

---

## 第一部分：核心架构设计

### 1. 架构哲学与设计原则

#### 1.1 整洁架构 (Clean Architecture)

我们采用整洁架构作为指导思想，将应用严格划分为三个核心层次：

```
┌─────────────────────────────────────────────┐
│          Presentation Layer (表现层)          │
│  Widgets, Pages, BLoCs, ViewModels          │
├─────────────────────────────────────────────┤
│           Domain Layer (领域层)              │
│   Use Cases, Entities, Repository Interfaces│
├─────────────────────────────────────────────┤
│            Data Layer (数据层)               │
│  Repository Impl, Data Sources, Models      │
└─────────────────────────────────────────────┘
```

**依赖关系:** 表现层 → 领域层 ← 数据层

#### 1.2 架构原则

1. **依赖倒置原则 (DIP)**: 高层模块不依赖低层模块，两者都依赖抽象
2. **单一职责原则 (SRP)**: 每个类只有一个改变的理由
3. **开闭原则 (OCP)**: 对扩展开放，对修改关闭
4. **接口隔离原则 (ISP)**: 使用多个专门的接口，而不是一个总接口
5. **里氏替换原则 (LSP)**: 派生类必须能够替换其基类

### 2. 项目结构：Monorepo + Melos

#### 2.1 目录结构

```bash
/your_project
├── melos.yaml                    # Melos 配置文件
├── .github/                      # CI/CD 配置
│   └── workflows/
│       ├── flutter.yml           # 主要的 CI/CD 流程
│       └── release.yml           # 发布流程
├── apps/
│   ├── main_app/                 # 主应用 (壳工程)
│   ├── admin_app/                # 管理端应用
│   └── merchant_app/             # 商户端应用
└── packages/
    ├── features/                 # 功能模块 (按业务划分)
    │   ├── feature_auth/         # 认证功能
    │   ├── feature_home/         # 首页功能
    │   ├── feature_payment/      # 支付功能
    │   └── feature_profile/      # 个人中心
    ├── core/                     # 核心模块 (业务无关)
    │   ├── core_network/         # 网络层
    │   ├── core_database/        # 数据库层
    │   ├── core_analytics/       # 分析统计
    │   ├── core_navigation/      # 导航路由
    │   ├── core_platform/        # 平台抽象
    │   ├── core_offline/         # 离线同步
    │   ├── core_realtime/        # 实时通信
    │   ├── core_permissions/     # 权限管理
    │   ├── core_encryption/      # 加密模块
    │   ├── core_device/          # 设备能力
    │   ├── core_lifecycle/       # 生命周期
    │   └── core_monitoring/      # 监控日志
    └── shared/                   # 共享模块
        ├── ui_kit/               # 设计系统 (UI组件库)
        ├── shared_utils/         # 通用工具类
        ├── shared_models/        # 共享数据模型
        ├── business_components/  # 业务组件库
        ├── form_management/      # 表单管理
        └── error_handling/       # 错误处理
```

#### 2.2 Melos 配置

```yaml
# melos.yaml
name: your_project

packages:
  - apps/**
  - packages/**

command:
  bootstrap:
    usePubspecOverrides: true

scripts:
  # 获取所有包的依赖
  get:
    run: melos exec -- "flutter pub get"
    description: Run `flutter pub get` in all packages.
  
  # 运行代码生成
  generate:
    run: melos exec --order-dependents --fail-fast -- "dart run build_runner build --delete-conflicting-outputs"
    description: Run code generation for all packages.
  
  # 运行所有测试
  test:
    run: melos exec -- "flutter test --coverage"
    description: Run all tests with coverage.
  
  # 单元测试
  test:unit:
    run: melos exec -- "flutter test test/unit"
    description: Run unit tests only.
  
  # Widget测试
  test:widget:
    run: melos exec -- "flutter test test/widget"
    description: Run widget tests only.
  
  # 集成测试
  test:integration:
    run: melos exec -- "flutter test integration_test"
    description: Run integration tests.
  
  # 检查代码格式
  format:check:
    run: melos exec -- "dart format --output=none --set-exit-if-changed ."
    description: Check formatting in all packages.
  
  # 格式化代码
  format:fix:
    run: melos exec -- "dart format ."
    description: Fix formatting in all packages.
  
  # 运行静态分析
  analyze:
    run: melos exec -- "flutter analyze"
    description: Run static analysis for all packages.
  
  # 清理所有构建产物
  clean:
    run: melos exec -- "flutter clean"
    description: Clean all packages.
```

### 3. 状态管理：BLoC

#### 3.1 为什么选择 BLoC

- **强制分离**: 通过事件驱动模式强制UI与业务逻辑分离
- **可测试性**: 业务逻辑完全独立，易于单元测试
- **可预测性**: 单向数据流使状态变化可预测
- **团队协作**: 清晰的模式降低代码审查难度

#### 3.2 BLoC 实现模式

```dart
// 事件定义
abstract class AuthEvent {}

class LoginRequested extends AuthEvent {
  final String email;
  final String password;
  
  LoginRequested({required this.email, required this.password});
}

// 状态定义
abstract class AuthState {}

class AuthInitial extends AuthState {}
class AuthLoading extends AuthState {}
class AuthSuccess extends AuthState {
  final User user;
  AuthSuccess(this.user);
}
class AuthFailure extends AuthState {
  final String message;
  AuthFailure(this.message);
}

// BLoC实现
@injectable
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final LoginUseCase _loginUseCase;
  
  AuthBloc(this._loginUseCase) : super(AuthInitial()) {
    on<LoginRequested>(_onLoginRequested);
  }
  
  Future<void> _onLoginRequested(
    LoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
    
    final result = await _loginUseCase.execute(
      email: event.email,
      password: event.password,
    );
    
    result.fold(
      (failure) => emit(AuthFailure(failure.message)),
      (user) => emit(AuthSuccess(user)),
    );
  }
}
```

### 4. 依赖注入：get_it + injectable

#### 4.1 配置依赖注入

```dart
// lib/injection.dart
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'injection.config.dart';

final getIt = GetIt.instance;

@InjectableInit(
  initializerName: r'$initGetIt',
  preferRelativeImports: true,
  asExtension: false,
)
void configureDependencies({String? environment}) => $initGetIt(
  getIt,
  environment: environment,
);
```

#### 4.2 依赖注册示例

```dart
// 数据层
@LazySingleton(as: AuthRepository)
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource _remoteDataSource;
  final AuthLocalDataSource _localDataSource;
  
  AuthRepositoryImpl(
    this._remoteDataSource,
    this._localDataSource,
  );
}

// 领域层
@injectable
class LoginUseCase {
  final AuthRepository _repository;
  
  LoginUseCase(this._repository);
  
  Future<Result<User, Failure>> execute({
    required String email,
    required String password,
  }) async {
    // 业务逻辑
  }
}

// 第三方依赖
@module
abstract class RegisterModule {
  @lazySingleton
  Dio get dio => Dio();
  
  @lazySingleton
  FlutterSecureStorage get secureStorage => const FlutterSecureStorage();
}
```

---

## 第二部分：核心基础设施模块

### 5. 错误处理和容错机制

#### 5.1 统一错误处理架构

```dart
// packages/shared/error_handling/lib/src/failure.dart
abstract class Failure {
  final String message;
  final String? code;
  final dynamic originalError;
  final StackTrace? stackTrace;
  
  const Failure({
    required this.message,
    this.code,
    this.originalError,
    this.stackTrace,
  });
  
  // 用于日志记录
  Map<String, dynamic> toJson() => {
    'message': message,
    'code': code,
    'type': runtimeType.toString(),
    'timestamp': DateTime.now().toIso8601String(),
  };
}

// 具体错误类型
class NetworkFailure extends Failure {
  const NetworkFailure({
    required super.message,
    super.code,
    super.originalError,
    super.stackTrace,
  });
}

class ServerFailure extends Failure {
  final int? statusCode;
  
  const ServerFailure({
    required super.message,
    this.statusCode,
    super.code,
    super.originalError,
    super.stackTrace,
  });
}

class CacheFailure extends Failure {
  const CacheFailure({
    required super.message,
    super.code,
    super.originalError,
    super.stackTrace,
  });
}

class ValidationFailure extends Failure {
  final Map<String, String> errors;
  
  const ValidationFailure({
    required super.message,
    required this.errors,
    super.code,
  });
}
```

#### 5.2 Result 类型（函数式错误处理）

```dart
// packages/shared/error_handling/lib/src/result.dart
sealed class Result<S, F extends Failure> {
  const Result();
  
  // 模式匹配
  T fold<T>(
    T Function(F failure) onFailure,
    T Function(S success) onSuccess,
  );
  
  // 便捷方法
  bool get isSuccess => this is Success<S, F>;
  bool get isFailure => this is Failure<S, F>;
  
  S? get successOrNull => isSuccess ? (this as Success<S, F>).value : null;
  F? get failureOrNull => isFailure ? (this as Failure<S, F>).failure : null;
  
  // 函数式操作
  Result<T, F> map<T>(T Function(S) transform) {
    return fold(
      (failure) => Failure(failure),
      (success) => Success(transform(success)),
    );
  }
  
  Result<T, F> flatMap<T>(Result<T, F> Function(S) transform) {
    return fold(
      (failure) => Failure(failure),
      (success) => transform(success),
    );
  }
}

class Success<S, F extends Failure> extends Result<S, F> {
  final S value;
  const Success(this.value);
  
  @override
  T fold<T>(
    T Function(F failure) onFailure,
    T Function(S success) onSuccess,
  ) => onSuccess(value);
}

class Failure<S, F extends Failure> extends Result<S, F> {
  final F failure;
  const Failure(this.failure);
  
  @override
  T fold<T>(
    T Function(F failure) onFailure,
    T Function(S success) onSuccess,
  ) => onFailure(failure);
}
```

#### 5.3 全局错误处理

```dart
// lib/main.dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 捕获 Flutter 错误
  FlutterError.onError = (details) {
    getIt<CrashReporter>().reportFlutterError(details);
  };
  
  // 捕获未处理的异步错误
  PlatformDispatcher.instance.onError = (error, stack) {
    getIt<CrashReporter>().reportError(error, stack);
    return true;
  };
  
  runApp(const MyApp());
}
```

### 6. 网络层架构

#### 6.1 Dio 配置与拦截器

```dart
// packages/core/core_network/lib/src/dio_client.dart
@module
abstract class NetworkModule {
  @lazySingleton
  Dio dio(
    @Named('baseUrl') String baseUrl,
    LoggingInterceptor loggingInterceptor,
    AuthInterceptor authInterceptor,
    TokenRefreshInterceptor tokenRefreshInterceptor,
    ErrorInterceptor errorInterceptor,
  ) {
    final dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    );
    
    dio.interceptors.addAll([
      loggingInterceptor,
      authInterceptor,
      tokenRefreshInterceptor,
      errorInterceptor,
    ]);
    
    return dio;
  }
}

// 认证拦截器
@lazySingleton
class AuthInterceptor extends Interceptor {
  final SecureStorageService _storage;
  
  AuthInterceptor(this._storage);
  
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final token = await _storage.getAccessToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }
}

// Token 刷新拦截器
@lazySingleton
class TokenRefreshInterceptor extends Interceptor {
  final AuthRepository _authRepository;
  final SecureStorageService _storage;
  final Dio _dio;
  
  TokenRefreshInterceptor(
    this._authRepository,
    this._storage,
    @Named('refreshDio') this._dio,
  );
  
  @override
  void onError(DioError err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      try {
        await _refreshToken();
        // 重试原始请求
        final response = await _dio.fetch(err.requestOptions);
        handler.resolve(response);
      } catch (e) {
        // Token 刷新失败，退出登录
        getIt<AuthBloc>().add(LogoutRequested());
        handler.reject(err);
      }
    } else {
      handler.next(err);
    }
  }
  
  Future<void> _refreshToken() async {
    final refreshToken = await _storage.getRefreshToken();
    if (refreshToken == null) throw Exception('No refresh token');
    
    final result = await _authRepository.refreshToken(refreshToken);
    
    await result.fold(
      (failure) => throw failure,
      (tokens) async {
        await _storage.saveTokens(tokens);
      },
    );
  }
}
```

#### 6.2 网络安全增强

```dart
// packages/core/core_network/lib/src/security/certificate_pinning.dart
class SecureHttpClient {
  static Dio createSecureClient() {
    final dio = Dio();
    
    if (kReleaseMode) {
      (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate = (client) {
        client.badCertificateCallback = (cert, host, port) {
          // 验证证书指纹
          final certDER = cert.der;
          final sha256 = sha.sha256;
          final digest = sha256.convert(certDER);
          final certFingerprint = digest.toString();
          
          // 预定义的证书指纹列表
          const allowedFingerprints = [
            'aa:bb:cc:dd:ee:ff...', // 生产环境证书指纹
          ];
          
          return allowedFingerprints.contains(certFingerprint);
        };
        return client;
      };
    }
    
    return dio;
  }
}
```

### 7. 数据持久化：Drift

#### 7.1 数据库配置

```dart
// packages/core/core_database/lib/src/app_database.dart
@DriftDatabase(
  tables: [Users, Tasks, Settings],
  daos: [UserDao, TaskDao, SettingsDao],
)
class AppDatabase extends _$AppDatabase {
  AppDatabase(QueryExecutor e) : super(e);
  
  @override
  int get schemaVersion => 1;
  
  @override
  MigrationStrategy get migration => MigrationStrategy(
    onCreate: (Migrator m) async {
      await m.createAll();
    },
    onUpgrade: (Migrator m, int from, int to) async {
      if (from < 2) {
        // 版本1到版本2的迁移
        await m.addColumn(users, users.profilePicture);
      }
    },
    beforeOpen: (details) async {
      if (details.wasCreated) {
        // 初始化默认数据
        await into(settings).insert(
          SettingsCompanion.insert(
            key: 'theme',
            value: 'system',
          ),
        );
      }
    },
  );
}

// DAO 示例
@DriftAccessor(tables: [Users])
class UserDao extends DatabaseAccessor<AppDatabase> with _$UserDaoMixin {
  UserDao(AppDatabase db) : super(db);
  
  // 响应式查询
  Stream<User?> watchCurrentUser(String userId) {
    return (select(users)..where((u) => u.id.equals(userId)))
      .watchSingleOrNull();
  }
  
  // 批量操作
  Future<void> insertUsers(List<User> userList) {
    return batch((batch) {
      batch.insertAll(users, userList);
    });
  }
}
```

### 8. 平台抽象层

```dart
// packages/core/core_platform/lib/src/platform_service.dart
abstract class PlatformService {
  // 平台特定UI组件
  Widget getPlatformButton({
    required VoidCallback onPressed,
    required Widget child,
    ButtonStyle? style,
  });
  
  Widget getPlatformTextField({
    required TextEditingController controller,
    String? placeholder,
    TextInputType? keyboardType,
  });
  
  Widget getPlatformSwitch({
    required bool value,
    required ValueChanged<bool> onChanged,
  });
  
  // 平台特定行为
  Future<bool> requestPermission(Permission permission);
  Future<FilePickerResult?> pickFiles(FilePickerOptions options);
  Future<void> shareContent(ShareContent content);
  Future<ImageSource?> showImageSourceDialog();
  
  // 平台特定存储路径
  Future<Directory> getDocumentsDirectory();
  Future<Directory> getCacheDirectory();
  Future<Directory> getTemporaryDirectory();
  
  // 平台特定功能
  Future<void> openAppSettings();
  Future<void> setStatusBarStyle(StatusBarStyle style);
  Future<void> setOrientation(DeviceOrientation orientation);
  
  // 生物识别
  Future<bool> canCheckBiometrics();
  Future<BiometricType> getAvailableBiometrics();
  Future<bool> authenticateWithBiometrics(String reason);
}

// 具体实现
@Injectable(as: PlatformService)
class IOSPlatformService implements PlatformService {
  @override
  Widget getPlatformButton({
    required VoidCallback onPressed,
    required Widget child,
    ButtonStyle? style,
  }) {
    return CupertinoButton(
      onPressed: onPressed,
      child: child,
    );
  }
  // ... 其他实现
}

@Injectable(as: PlatformService)
class AndroidPlatformService implements PlatformService {
  @override
  Widget getPlatformButton({
    required VoidCallback onPressed,
    required Widget child,
    ButtonStyle? style,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: style,
      child: child,
    );
  }
  // ... 其他实现
}

// 自动注入正确的实现
@module
abstract class PlatformModule {
  @lazySingleton
  PlatformService get platformService {
    if (Platform.isIOS) {
      return IOSPlatformService();
    } else if (Platform.isAndroid) {
      return AndroidPlatformService();
    } else if (kIsWeb) {
      return WebPlatformService();
    } else {
      return DesktopPlatformService();
    }
  }
}
```

### 9. 离线优先架构

```dart
// packages/core/core_offline/lib/src/offline_sync_manager.dart
@lazySingleton
class OfflineSyncManager {
  final LocalDatabase _localDb;
  final RemoteApi _remoteApi;
  final ConnectivityService _connectivity;
  final Logger _logger;
  
  // 同步队列
  final _syncQueue = Queue<SyncOperation>();
  StreamController<SyncStatus> _syncStatusController = StreamController.broadcast();
  
  // 冲突解决策略
  ConflictResolutionStrategy strategy = ConflictResolutionStrategy.lastWrite;
  
  Stream<SyncStatus> get syncStatus => _syncStatusController.stream;
  
  // 执行操作（支持离线）
  Future<Result<T, Failure>> executeOperation<T>({
    required String operationId,
    required Future<T> Function() remoteOperation,
    required Future<T> Function() localOperation,
    required SyncableEntity Function(T) toEntity,
    bool requiresNetwork = true,
  }) async {
    try {
      if (!requiresNetwork || await _connectivity.hasConnection) {
        // 在线模式：执行远程操作并同步到本地
        final result = await remoteOperation();
        await _saveToLocal(toEntity(result));
        return Success(result);
      } else {
        // 离线模式：执行本地操作并加入同步队列
        final result = await localOperation();
        final entity = toEntity(result);
        await _saveToLocal(entity);
        _addToSyncQueue(SyncOperation(
          id: operationId,
          type: SyncOperationType.create,
          entity: entity,
          timestamp: DateTime.now(),
        ));
        return Success(result);
      }
    } catch (e, s) {
      _logger.error('Operation failed', e, s);
      return Failure(
        OperationFailure(
          message: 'Operation failed: ${e.toString()}',
          originalError: e,
          stackTrace: s,
        ),
      );
    }
  }
  
  // 启动自动同步
  void startAutoSync() {
    _connectivity.onConnectionRestored.listen((_) {
      _processSyncQueue();
    });
    
    // 定期检查同步队列
    Timer.periodic(const Duration(minutes: 5), (_) {
      if (_connectivity.isConnected) {
        _processSyncQueue();
      }
    });
  }
  
  // 处理同步队列
  Future<void> _processSyncQueue() async {
    if (_syncQueue.isEmpty) return;
    
    _syncStatusController.add(SyncStatus.syncing(_syncQueue.length));
    
    final failedOperations = <SyncOperation>[];
    
    while (_syncQueue.isNotEmpty) {
      final operation = _syncQueue.removeFirst();
      
      try {
        await _syncOperation(operation);
      } catch (e) {
        _logger.error('Sync failed for operation ${operation.id}', e);
        failedOperations.add(operation);
      }
    }
    
    // 将失败的操作重新加入队列
    _syncQueue.addAll(failedOperations);
    
    _syncStatusController.add(
      failedOperations.isEmpty 
        ? SyncStatus.completed() 
        : SyncStatus.partial(failedOperations.length),
    );
  }
  
  // 冲突解决
  Future<T> _resolveConflict<T extends SyncableEntity>(
    T local,
    T remote,
  ) async {
    switch (strategy) {
      case ConflictResolutionStrategy.lastWrite:
        return local.lastModified.isAfter(remote.lastModified) ? local : remote;
      case ConflictResolutionStrategy.remote:
        return remote;
      case ConflictResolutionStrategy.local:
        return local;
      case ConflictResolutionStrategy.manual:
        // 触发UI让用户选择
        return await _showConflictDialog(local, remote);
    }
  }
}
```

### 10. 表单管理系统

```dart
// packages/shared/form_management/lib/src/form_controller.dart
class FormController<T> {
  final _fields = <String, FormFieldController>{};
  final _errors = <String, List<String>>{};
  final _touched = <String>{};
  final _streamController = StreamController<FormState>.broadcast();
  
  // 表单验证器
  final List<FormValidator> validators;
  
  // 自动保存
  Timer? _autoSaveTimer;
  final Duration autoSaveInterval;
  final Future<void> Function(Map<String, dynamic>)? onAutoSave;
  
  // 表单状态
  Stream<FormState> get stateStream => _streamController.stream;
  FormState get currentState => FormState(
    values: values,
    errors: _errors,
    touched: _touched,
    isValid: isValid,
    isDirty: isDirty,
    isSubmitting: _isSubmitting,
  );
  
  FormController({
    required this.validators,
    this.autoSaveInterval = const Duration(seconds: 30),
    this.onAutoSave,
  });
  
  // 注册字段
  void registerField(String name, FormFieldController field) {
    _fields[name] = field;
    
    // 监听字段变化
    field.valueStream.listen((_) {
      _onFieldChanged(name);
    });
  }
  
  // 字段变化处理
  void _onFieldChanged(String name) {
    // 标记为已触碰
    _touched.add(name);
    
    // 验证单个字段
    _validateField(name);
    
    // 重置自动保存计时器
    _resetAutoSaveTimer();
    
    // 发送状态更新
    _streamController.add(currentState);
  }
  
  // 验证表单
  bool validate() {
    _errors.clear();
    
    // 验证所有字段
    for (final entry in _fields.entries) {
      _validateField(entry.key);
    }
    
    // 运行表单级验证器
    for (final validator in validators) {
      final errors = validator.validate(values);
      errors.forEach((field, messages) {
        _errors[field] = [...?_errors[field], ...messages];
      });
    }
    
    _streamController.add(currentState);
    return _errors.isEmpty;
  }
  
  // 提交表单
  Future<Result<T, Failure>> submit(
    Future<T> Function(Map<String, dynamic>) onSubmit,
  ) async {
    if (!validate()) {
      return Failure(ValidationFailure(
        message: 'Form validation failed',
        errors: _errors,
      ));
    }
    
    _isSubmitting = true;
    _streamController.add(currentState);
    
    try {
      final result = await onSubmit(values);
      await clearDraft();
      return Success(result);
    } catch (e) {
      return Failure(SubmitFailure(e.toString()));
    } finally {
      _isSubmitting = false;
      _streamController.add(currentState);
    }
  }
  
  // 自动保存
  void _resetAutoSaveTimer() {
    _autoSaveTimer?.cancel();
    
    if (onAutoSave != null) {
      _autoSaveTimer = Timer(autoSaveInterval, () async {
        await onAutoSave!(values);
      });
    }
  }
}

// 表单字段控制器
class