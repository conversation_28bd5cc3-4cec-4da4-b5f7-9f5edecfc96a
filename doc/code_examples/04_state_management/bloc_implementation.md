# BLoC 状态管理实现示例

## 1. 核心BLoC基类

### 基础BLoC抽象类
```dart
// packages/core/core_bloc/lib/src/base_bloc.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:core_error/core_error.dart';

/// 基础状态类
abstract class BaseState extends Equatable {
  const BaseState();

  @override
  List<Object?> get props => [];
}

/// 基础事件类
abstract class BaseEvent extends Equatable {
  const BaseEvent();

  @override
  List<Object?> get props => [];
}

/// 基础BLoC类
abstract class BaseBloc<Event extends BaseEvent, State extends BaseState>
    extends Bloc<Event, State> {
  BaseBloc(super.initialState) {
    // 注册通用事件处理器
    _registerEventHandlers();
  }

  /// 注册事件处理器
  void _registerEventHandlers() {
    // 子类可以重写此方法来注册特定的事件处理器
  }

  /// 处理错误的通用方法
  State handleError(Failure failure) {
    // 记录错误
    _logError(failure);
    
    // 返回错误状态（子类需要实现）
    return createErrorState(failure);
  }

  /// 创建错误状态（子类必须实现）
  State createErrorState(Failure failure);

  /// 记录错误
  void _logError(Failure failure) {
    print('BLoC Error [${runtimeType}]: ${failure.message}');
    
    // 在生产环境中，可以发送到错误报告服务
    // if (kReleaseMode) {
    //   CrashlyticsService.recordError(failure, StackTrace.current);
    // }
  }

  /// 安全的状态发射
  void safeEmit(State state) {
    if (!isClosed) {
      emit(state);
    }
  }

  /// 执行异步操作的通用方法
  Future<void> executeAsyncOperation<T>(
    Future<Result<T>> operation,
    void Function(T data) onSuccess,
    State loadingState,
  ) async {
    safeEmit(loadingState);
    
    final result = await operation;
    result.fold(
      onSuccess: onSuccess,
      onFailure: (failure) => safeEmit(handleError(failure)),
    );
  }
}
```

### 分页BLoC基类
```dart
// packages/core/core_bloc/lib/src/pagination_bloc.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:core_error/core_error.dart';
import 'base_bloc.dart';

/// 分页状态
abstract class PaginationState<T> extends BaseState {
  const PaginationState({
    required this.items,
    required this.hasReachedMax,
    required this.isLoading,
    required this.isRefreshing,
    this.error,
  });

  final List<T> items;
  final bool hasReachedMax;
  final bool isLoading;
  final bool isRefreshing;
  final String? error;

  @override
  List<Object?> get props => [
        items,
        hasReachedMax,
        isLoading,
        isRefreshing,
        error,
      ];

  /// 复制状态
  PaginationState<T> copyWith({
    List<T>? items,
    bool? hasReachedMax,
    bool? isLoading,
    bool? isRefreshing,
    String? error,
  });
}

/// 分页事件
abstract class PaginationEvent extends BaseEvent {
  const PaginationEvent();
}

/// 加载更多事件
class LoadMoreRequested extends PaginationEvent {
  const LoadMoreRequested();
}

/// 刷新事件
class RefreshRequested extends PaginationEvent {
  const RefreshRequested();
}

/// 分页BLoC基类
abstract class PaginationBloc<Event extends PaginationEvent,
        State extends PaginationState<T>, T>
    extends BaseBloc<Event, State> {
  PaginationBloc(super.initialState) {
    on<LoadMoreRequested>(_onLoadMoreRequested);
    on<RefreshRequested>(_onRefreshRequested);
  }

  int _currentPage = 1;
  static const int _pageSize = 20;

  /// 获取当前页码
  int get currentPage => _currentPage;

  /// 获取页面大小
  int get pageSize => _pageSize;

  /// 加载数据的抽象方法
  Future<Result<List<T>>> loadData(int page, int pageSize);

  /// 创建初始状态
  State createInitialState();

  /// 处理加载更多
  Future<void> _onLoadMoreRequested(
    LoadMoreRequested event,
    Emitter<State> emit,
  ) async {
    if (state.hasReachedMax || state.isLoading) return;

    emit(state.copyWith(isLoading: true) as State);

    final result = await loadData(_currentPage, _pageSize);
    result.fold(
      onSuccess: (newItems) {
        final hasReachedMax = newItems.length < _pageSize;
        final updatedItems = List<T>.from(state.items)..addAll(newItems);
        
        _currentPage++;
        
        emit(state.copyWith(
          items: updatedItems,
          hasReachedMax: hasReachedMax,
          isLoading: false,
          error: null,
        ) as State);
      },
      onFailure: (failure) {
        emit(state.copyWith(
          isLoading: false,
          error: failure.message,
        ) as State);
      },
    );
  }

  /// 处理刷新
  Future<void> _onRefreshRequested(
    RefreshRequested event,
    Emitter<State> emit,
  ) async {
    emit(state.copyWith(isRefreshing: true) as State);

    _currentPage = 1;
    final result = await loadData(_currentPage, _pageSize);
    
    result.fold(
      onSuccess: (newItems) {
        final hasReachedMax = newItems.length < _pageSize;
        _currentPage++;
        
        emit(state.copyWith(
          items: newItems,
          hasReachedMax: hasReachedMax,
          isRefreshing: false,
          error: null,
        ) as State);
      },
      onFailure: (failure) {
        emit(state.copyWith(
          isRefreshing: false,
          error: failure.message,
        ) as State);
      },
    );
  }

  /// 重置分页状态
  void resetPagination() {
    _currentPage = 1;
    emit(createInitialState());
  }
}
```

## 2. 具体功能BLoC实现

### 用户列表BLoC
```dart
// packages/features/feature_user/lib/src/presentation/bloc/user_list_bloc.dart
import 'package:injectable/injectable.dart';
import 'package:shared_models/shared_models.dart';
import 'package:core_bloc/core_bloc.dart';
import 'package:core_error/core_error.dart';
import '../../domain/usecases/get_users_usecase.dart';
import '../../domain/usecases/search_users_usecase.dart';

/// 用户列表状态
class UserListState extends PaginationState<UserEntity> {
  const UserListState({
    required super.items,
    required super.hasReachedMax,
    required super.isLoading,
    required super.isRefreshing,
    super.error,
    this.searchQuery = '',
    this.isSearching = false,
  });

  final String searchQuery;
  final bool isSearching;

  @override
  List<Object?> get props => [
        ...super.props,
        searchQuery,
        isSearching,
      ];

  @override
  UserListState copyWith({
    List<UserEntity>? items,
    bool? hasReachedMax,
    bool? isLoading,
    bool? isRefreshing,
    String? error,
    String? searchQuery,
    bool? isSearching,
  }) {
    return UserListState(
      items: items ?? this.items,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      error: error ?? this.error,
      searchQuery: searchQuery ?? this.searchQuery,
      isSearching: isSearching ?? this.isSearching,
    );
  }
}

/// 用户列表事件
abstract class UserListEvent extends PaginationEvent {
  const UserListEvent();
}

/// 搜索用户事件
class SearchUsersRequested extends UserListEvent {
  const SearchUsersRequested(this.query);

  final String query;

  @override
  List<Object> get props => [query];
}

/// 清除搜索事件
class ClearSearchRequested extends UserListEvent {
  const ClearSearchRequested();
}

/// 用户列表BLoC
@injectable
class UserListBloc extends PaginationBloc<UserListEvent, UserListState, UserEntity> {
  UserListBloc(
    this._getUsersUseCase,
    this._searchUsersUseCase,
  ) : super(const UserListState(
          items: [],
          hasReachedMax: false,
          isLoading: false,
          isRefreshing: false,
        )) {
    on<SearchUsersRequested>(_onSearchUsersRequested);
    on<ClearSearchRequested>(_onClearSearchRequested);
    
    // 初始加载
    add(const LoadMoreRequested());
  }

  final GetUsersUseCase _getUsersUseCase;
  final SearchUsersUseCase _searchUsersUseCase;

  @override
  Future<Result<List<UserEntity>>> loadData(int page, int pageSize) async {
    if (state.searchQuery.isNotEmpty) {
      return _searchUsersUseCase(SearchUsersParams(
        query: state.searchQuery,
        page: page,
        pageSize: pageSize,
      ));
    } else {
      return _getUsersUseCase(GetUsersParams(
        page: page,
        pageSize: pageSize,
      ));
    }
  }

  @override
  UserListState createInitialState() {
    return const UserListState(
      items: [],
      hasReachedMax: false,
      isLoading: false,
      isRefreshing: false,
    );
  }

  @override
  UserListState createErrorState(Failure failure) {
    return state.copyWith(
      isLoading: false,
      isRefreshing: false,
      error: failure.message,
    );
  }

  /// 处理搜索用户
  Future<void> _onSearchUsersRequested(
    SearchUsersRequested event,
    Emitter<UserListState> emit,
  ) async {
    emit(state.copyWith(
      searchQuery: event.query,
      isSearching: true,
    ));

    // 重置分页并开始搜索
    resetPagination();
    add(const LoadMoreRequested());
  }

  /// 处理清除搜索
  Future<void> _onClearSearchRequested(
    ClearSearchRequested event,
    Emitter<UserListState> emit,
  ) async {
    emit(state.copyWith(
      searchQuery: '',
      isSearching: false,
    ));

    // 重置分页并重新加载
    resetPagination();
    add(const LoadMoreRequested());
  }
}
```

### 用户详情BLoC
```dart
// packages/features/feature_user/lib/src/presentation/bloc/user_detail_bloc.dart
import 'package:injectable/injectable.dart';
import 'package:shared_models/shared_models.dart';
import 'package:core_bloc/core_bloc.dart';
import 'package:core_error/core_error.dart';
import '../../domain/usecases/get_user_detail_usecase.dart';
import '../../domain/usecases/follow_user_usecase.dart';
import '../../domain/usecases/unfollow_user_usecase.dart';

/// 用户详情状态
abstract class UserDetailState extends BaseState {
  const UserDetailState();
}

/// 初始状态
class UserDetailInitial extends UserDetailState {
  const UserDetailInitial();
}

/// 加载中状态
class UserDetailLoading extends UserDetailState {
  const UserDetailLoading();
}

/// 加载成功状态
class UserDetailLoaded extends UserDetailState {
  const UserDetailLoaded({
    required this.user,
    required this.isFollowing,
    this.isFollowLoading = false,
  });

  final UserEntity user;
  final bool isFollowing;
  final bool isFollowLoading;

  @override
  List<Object> get props => [user, isFollowing, isFollowLoading];

  UserDetailLoaded copyWith({
    UserEntity? user,
    bool? isFollowing,
    bool? isFollowLoading,
  }) {
    return UserDetailLoaded(
      user: user ?? this.user,
      isFollowing: isFollowing ?? this.isFollowing,
      isFollowLoading: isFollowLoading ?? this.isFollowLoading,
    );
  }
}

/// 加载失败状态
class UserDetailError extends UserDetailState {
  const UserDetailError(this.message);

  final String message;

  @override
  List<Object> get props => [message];
}

/// 用户详情事件
abstract class UserDetailEvent extends BaseEvent {
  const UserDetailEvent();
}

/// 加载用户详情事件
class LoadUserDetailRequested extends UserDetailEvent {
  const LoadUserDetailRequested(this.userId);

  final String userId;

  @override
  List<Object> get props => [userId];
}

/// 关注用户事件
class FollowUserRequested extends UserDetailEvent {
  const FollowUserRequested();
}

/// 取消关注用户事件
class UnfollowUserRequested extends UserDetailEvent {
  const UnfollowUserRequested();
}

/// 刷新用户详情事件
class RefreshUserDetailRequested extends UserDetailEvent {
  const RefreshUserDetailRequested();
}

/// 用户详情BLoC
@injectable
class UserDetailBloc extends BaseBloc<UserDetailEvent, UserDetailState> {
  UserDetailBloc(
    this._getUserDetailUseCase,
    this._followUserUseCase,
    this._unfollowUserUseCase,
  ) : super(const UserDetailInitial()) {
    on<LoadUserDetailRequested>(_onLoadUserDetailRequested);
    on<FollowUserRequested>(_onFollowUserRequested);
    on<UnfollowUserRequested>(_onUnfollowUserRequested);
    on<RefreshUserDetailRequested>(_onRefreshUserDetailRequested);
  }

  final GetUserDetailUseCase _getUserDetailUseCase;
  final FollowUserUseCase _followUserUseCase;
  final UnfollowUserUseCase _unfollowUserUseCase;

  String? _currentUserId;

  @override
  UserDetailState createErrorState(Failure failure) {
    return UserDetailError(failure.message);
  }

  /// 处理加载用户详情
  Future<void> _onLoadUserDetailRequested(
    LoadUserDetailRequested event,
    Emitter<UserDetailState> emit,
  ) async {
    _currentUserId = event.userId;
    
    await executeAsyncOperation(
      _getUserDetailUseCase(event.userId),
      (userDetail) {
        safeEmit(UserDetailLoaded(
          user: userDetail.user,
          isFollowing: userDetail.isFollowing,
        ));
      },
      const UserDetailLoading(),
    );
  }

  /// 处理关注用户
  Future<void> _onFollowUserRequested(
    FollowUserRequested event,
    Emitter<UserDetailState> emit,
  ) async {
    final currentState = state;
    if (currentState is! UserDetailLoaded || _currentUserId == null) return;

    emit(currentState.copyWith(isFollowLoading: true));

    final result = await _followUserUseCase(_currentUserId!);
    result.fold(
      onSuccess: (_) {
        safeEmit(currentState.copyWith(
          isFollowing: true,
          isFollowLoading: false,
        ));
      },
      onFailure: (failure) {
        safeEmit(currentState.copyWith(isFollowLoading: false));
        // 可以显示错误提示
      },
    );
  }

  /// 处理取消关注用户
  Future<void> _onUnfollowUserRequested(
    UnfollowUserRequested event,
    Emitter<UserDetailState> emit,
  ) async {
    final currentState = state;
    if (currentState is! UserDetailLoaded || _currentUserId == null) return;

    emit(currentState.copyWith(isFollowLoading: true));

    final result = await _unfollowUserUseCase(_currentUserId!);
    result.fold(
      onSuccess: (_) {
        safeEmit(currentState.copyWith(
          isFollowing: false,
          isFollowLoading: false,
        ));
      },
      onFailure: (failure) {
        safeEmit(currentState.copyWith(isFollowLoading: false));
        // 可以显示错误提示
      },
    );
  }

  /// 处理刷新用户详情
  Future<void> _onRefreshUserDetailRequested(
    RefreshUserDetailRequested event,
    Emitter<UserDetailState> emit,
  ) async {
    if (_currentUserId != null) {
      add(LoadUserDetailRequested(_currentUserId!));
    }
  }
}
```

## 3. 复杂状态管理示例

### 购物车BLoC
```dart
// packages/features/feature_shopping/lib/src/presentation/bloc/cart_bloc.dart
import 'package:injectable/injectable.dart';
import 'package:shared_models/shared_models.dart';
import 'package:core_bloc/core_bloc.dart';
import 'package:core_error/core_error.dart';
import '../../domain/entities/cart_entity.dart';
import '../../domain/entities/cart_item_entity.dart';
import '../../domain/usecases/get_cart_usecase.dart';
import '../../domain/usecases/add_to_cart_usecase.dart';
import '../../domain/usecases/remove_from_cart_usecase.dart';
import '../../domain/usecases/update_cart_item_usecase.dart';
import '../../domain/usecases/clear_cart_usecase.dart';

/// 购物车状态
abstract class CartState extends BaseState {
  const CartState();
}

/// 初始状态
class CartInitial extends CartState {
  const CartInitial();
}

/// 加载中状态
class CartLoading extends CartState {
  const CartLoading();
}

/// 加载成功状态
class CartLoaded extends CartState {
  const CartLoaded({
    required this.cart,
    this.isUpdating = false,
    this.updatingItemId,
  });

  final CartEntity cart;
  final bool isUpdating;
  final String? updatingItemId;

  @override
  List<Object?> get props => [cart, isUpdating, updatingItemId];

  CartLoaded copyWith({
    CartEntity? cart,
    bool? isUpdating,
    String? updatingItemId,
  }) {
    return CartLoaded(
      cart: cart ?? this.cart,
      isUpdating: isUpdating ?? this.isUpdating,
      updatingItemId: updatingItemId ?? this.updatingItemId,
    );
  }

  /// 获取总价
  double get totalPrice => cart.totalPrice;

  /// 获取商品总数
  int get totalItems => cart.totalItems;

  /// 检查是否为空
  bool get isEmpty => cart.items.isEmpty;

  /// 检查特定商品是否在更新中
  bool isItemUpdating(String itemId) {
    return isUpdating && updatingItemId == itemId;
  }
}

/// 加载失败状态
class CartError extends CartState {
  const CartError(this.message);

  final String message;

  @override
  List<Object> get props => [message];
}

/// 购物车事件
abstract class CartEvent extends BaseEvent {
  const CartEvent();
}

/// 加载购物车事件
class LoadCartRequested extends CartEvent {
  const LoadCartRequested();
}

/// 添加到购物车事件
class AddToCartRequested extends CartEvent {
  const AddToCartRequested({
    required this.productId,
    required this.quantity,
    this.options,
  });

  final String productId;
  final int quantity;
  final Map<String, dynamic>? options;

  @override
  List<Object?> get props => [productId, quantity, options];
}

/// 从购物车移除事件
class RemoveFromCartRequested extends CartEvent {
  const RemoveFromCartRequested(this.itemId);

  final String itemId;

  @override
  List<Object> get props => [itemId];
}

/// 更新购物车商品事件
class UpdateCartItemRequested extends CartEvent {
  const UpdateCartItemRequested({
    required this.itemId,
    required this.quantity,
  });

  final String itemId;
  final int quantity;

  @override
  List<Object> get props => [itemId, quantity];
}

/// 清空购物车事件
class ClearCartRequested extends CartEvent {
  const ClearCartRequested();
}

/// 购物车BLoC
@injectable
class CartBloc extends BaseBloc<CartEvent, CartState> {
  CartBloc(
    this._getCartUseCase,
    this._addToCartUseCase,
    this._removeFromCartUseCase,
    this._updateCartItemUseCase,
    this._clearCartUseCase,
  ) : super(const CartInitial()) {
    on<LoadCartRequested>(_onLoadCartRequested);
    on<AddToCartRequested>(_onAddToCartRequested);
    on<RemoveFromCartRequested>(_onRemoveFromCartRequested);
    on<UpdateCartItemRequested>(_onUpdateCartItemRequested);
    on<ClearCartRequested>(_onClearCartRequested);
    
    // 自动加载购物车
    add(const LoadCartRequested());
  }

  final GetCartUseCase _getCartUseCase;
  final AddToCartUseCase _addToCartUseCase;
  final RemoveFromCartUseCase _removeFromCartUseCase;
  final UpdateCartItemUseCase _updateCartItemUseCase;
  final ClearCartUseCase _clearCartUseCase;

  @override
  CartState createErrorState(Failure failure) {
    return CartError(failure.message);
  }

  /// 处理加载购物车
  Future<void> _onLoadCartRequested(
    LoadCartRequested event,
    Emitter<CartState> emit,
  ) async {
    await executeAsyncOperation(
      _getCartUseCase(),
      (cart) => safeEmit(CartLoaded(cart: cart)),
      const CartLoading(),
    );
  }

  /// 处理添加到购物车
  Future<void> _onAddToCartRequested(
    AddToCartRequested event,
    Emitter<CartState> emit,
  ) async {
    final currentState = state;
    if (currentState is! CartLoaded) return;

    emit(currentState.copyWith(isUpdating: true));

    final params = AddToCartParams(
      productId: event.productId,
      quantity: event.quantity,
      options: event.options,
    );

    final result = await _addToCartUseCase(params);
    result.fold(
      onSuccess: (updatedCart) {
        safeEmit(CartLoaded(
          cart: updatedCart,
          isUpdating: false,
        ));
      },
      onFailure: (failure) {
        safeEmit(currentState.copyWith(isUpdating: false));
        // 可以显示错误提示
      },
    );
  }

  /// 处理从购物车移除
  Future<void> _onRemoveFromCartRequested(
    RemoveFromCartRequested event,
    Emitter<CartState> emit,
  ) async {
    final currentState = state;
    if (currentState is! CartLoaded) return;

    emit(currentState.copyWith(
      isUpdating: true,
      updatingItemId: event.itemId,
    ));

    final result = await _removeFromCartUseCase(event.itemId);
    result.fold(
      onSuccess: (updatedCart) {
        safeEmit(CartLoaded(
          cart: updatedCart,
          isUpdating: false,
        ));
      },
      onFailure: (failure) {
        safeEmit(currentState.copyWith(
          isUpdating: false,
          updatingItemId: null,
        ));
      },
    );
  }

  /// 处理更新购物车商品
  Future<void> _onUpdateCartItemRequested(
    UpdateCartItemRequested event,
    Emitter<CartState> emit,
  ) async {
    final currentState = state;
    if (currentState is! CartLoaded) return;

    // 如果数量为0，则移除商品
    if (event.quantity <= 0) {
      add(RemoveFromCartRequested(event.itemId));
      return;
    }

    emit(currentState.copyWith(
      isUpdating: true,
      updatingItemId: event.itemId,
    ));

    final params = UpdateCartItemParams(
      itemId: event.itemId,
      quantity: event.quantity,
    );

    final result = await _updateCartItemUseCase(params);
    result.fold(
      onSuccess: (updatedCart) {
        safeEmit(CartLoaded(
          cart: updatedCart,
          isUpdating: false,
        ));
      },
      onFailure: (failure) {
        safeEmit(currentState.copyWith(
          isUpdating: false,
          updatingItemId: null,
        ));
      },
    );
  }

  /// 处理清空购物车
  Future<void> _onClearCartRequested(
    ClearCartRequested event,
    Emitter<CartState> emit,
  ) async {
    final currentState = state;
    if (currentState is! CartLoaded) return;

    emit(currentState.copyWith(isUpdating: true));

    final result = await _clearCartUseCase();
    result.fold(
      onSuccess: (emptyCart) {
        safeEmit(CartLoaded(
          cart: emptyCart,
          isUpdating: false,
        ));
      },
      onFailure: (failure) {
        safeEmit(currentState.copyWith(isUpdating: false));
      },
    );
  }

  /// 获取购物车中特定商品的数量
  int getItemQuantity(String productId) {
    final currentState = state;
    if (currentState is CartLoaded) {
      final item = currentState.cart.items.firstWhere(
        (item) => item.productId == productId,
        orElse: () => CartItemEntity.empty(),
      );
      return item.quantity;
    }
    return 0;
  }

  /// 检查商品是否在购物车中
  bool hasItem(String productId) {
    return getItemQuantity(productId) > 0;
  }
}
```

## 4. BLoC组合和通信

### BLoC监听器
```dart
// packages/core/core_bloc/lib/src/bloc_listener_mixin.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:async';

/// BLoC监听器混入
mixin BlocListenerMixin<Event, State> on Bloc<Event, State> {
  final List<StreamSubscription> _subscriptions = [];

  /// 监听其他BLoC的状态变化
  void listenToBloc<T extends StateStreamable<S>, S>(
    T bloc,
    void Function(S state) listener,
  ) {
    final subscription = bloc.stream.listen(listener);
    _subscriptions.add(subscription);
  }

  /// 监听多个BLoC的状态变化
  void listenToMultipleBlocs(Map<StateStreamable, Function> listeners) {
    listeners.forEach((bloc, listener) {
      final subscription = bloc.stream.listen(listener);
      _subscriptions.add(subscription);
    });
  }

  @override
  Future<void> close() {
    // 取消所有订阅
    for (final subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();
    return super.close();
  }
}
```

### 应用级状态BLoC
```dart
// packages/core/core_bloc/lib/src/app_bloc.dart
import 'package:injectable/injectable.dart';
import 'package:shared_models/shared_models.dart';
import 'package:core_bloc/core_bloc.dart';
import 'package:feature_auth/feature_auth.dart';
import 'package:feature_user/feature_user.dart';

/// 应用状态
abstract class AppState extends BaseState {
  const AppState();
}

/// 应用初始化中
class AppInitializing extends AppState {
  const AppInitializing();
}

/// 应用已认证
class AppAuthenticated extends AppState {
  const AppAuthenticated(this.user);

  final UserEntity user;

  @override
  List<Object> get props => [user];
}

/// 应用未认证
class AppUnauthenticated extends AppState {
  const AppUnauthenticated();
}

/// 应用错误
class AppError extends AppState {
  const AppError(this.message);

  final String message;

  @override
  List<Object> get props => [message];
}

/// 应用事件
abstract class AppEvent extends BaseEvent {
  const AppEvent();
}

/// 应用启动事件
class AppStarted extends AppEvent {
  const AppStarted();
}

/// 用户登录事件
class AppUserLoggedIn extends AppEvent {
  const AppUserLoggedIn(this.user);

  final UserEntity user;

  @override
  List<Object> get props => [user];
}

/// 用户登出事件
class AppUserLoggedOut extends AppEvent {
  const AppUserLoggedOut();
}

/// 应用BLoC
@singleton
class AppBloc extends BaseBloc<AppEvent, AppState> with BlocListenerMixin {
  AppBloc(
    this._authBloc,
  ) : super(const AppInitializing()) {
    on<AppStarted>(_onAppStarted);
    on<AppUserLoggedIn>(_onAppUserLoggedIn);
    on<AppUserLoggedOut>(_onAppUserLoggedOut);
    
    // 监听认证状态变化
    listenToBloc<AuthBloc, AuthState>(
      _authBloc,
      _onAuthStateChanged,
    );
  }

  final AuthBloc _authBloc;

  @override
  AppState createErrorState(Failure failure) {
    return AppError(failure.message);
  }

  /// 处理应用启动
  Future<void> _onAppStarted(
    AppStarted event,
    Emitter<AppState> emit,
  ) async {
    // 检查认证状态
    _authBloc.add(const AuthCheckRequested());
  }

  /// 处理用户登录
  Future<void> _onAppUserLoggedIn(
    AppUserLoggedIn event,
    Emitter<AppState> emit,
  ) async {
    safeEmit(AppAuthenticated(event.user));
  }

  /// 处理用户登出
  Future<void> _onAppUserLoggedOut(
    AppUserLoggedOut event,
    Emitter<AppState> emit,
  ) async {
    safeEmit(const AppUnauthenticated());
  }

  /// 监听认证状态变化
  void _onAuthStateChanged(AuthState authState) {
    if (authState is AuthAuthenticated) {
      add(AppUserLoggedIn(authState.user));
    } else if (authState is AuthUnauthenticated) {
      add(const AppUserLoggedOut());
    }
  }
}
```

这个BLoC状态管理实现展示了：

1. **基础架构**：可复用的基类和混入
2. **分页支持**：通用的分页BLoC实现
3. **复杂状态**：购物车等复杂业务逻辑
4. **BLoC通信**：不同BLoC之间的状态监听
5. **错误处理**：统一的错误处理机制
6. **生命周期管理**：正确的资源清理

所有实现都遵循BLoC模式的最佳实践，确保状态管理的可预测性和可测试性。