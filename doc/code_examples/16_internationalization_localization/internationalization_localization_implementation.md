# Flutter 企业级应用国际化和本地化实现

本文档提供了 Flutter 企业级应用中国际化（i18n）和本地化（l10n）的完整实现示例，包括多语言支持、动态语言切换、本地化资源管理、RTL 支持和文化适配。

## 1. 基础配置

### 1.1 依赖配置

```yaml
# pubspec.yaml
name: flutter_enterprise_app
description: Flutter 企业级应用国际化示例

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  
  # 国际化相关
  intl: ^0.19.0
  intl_utils: ^2.8.7
  
  # 状态管理
  flutter_bloc: ^8.1.3
  
  # 本地存储
  shared_preferences: ^2.2.2
  
  # 网络请求
  dio: ^5.3.2
  
  # 依赖注入
  get_it: ^7.6.4
  
  # 日期时间
  timezone: ^0.9.2
  
  # 货币格式化
  money2: ^5.0.0
  
  # 电话号码格式化
  phone_numbers_parser: ^8.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  build_runner: ^2.4.7

flutter:
  uses-material-design: true
  generate: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/
    - assets/locales/
  
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
    - family: NotoSans
      fonts:
        - asset: assets/fonts/NotoSans-Regular.ttf
        - asset: assets/fonts/NotoSans-Bold.ttf
          weight: 700
    - family: NotoSansArabic
      fonts:
        - asset: assets/fonts/NotoSansArabic-Regular.ttf
        - asset: assets/fonts/NotoSansArabic-Bold.ttf
          weight: 700
```

### 1.2 本地化配置

```yaml
# l10n.yaml
arb-dir: lib/l10n
template-arb-file: app_en.arb
output-localization-file: app_localizations.dart
output-class: AppLocalizations
output-dir: lib/l10n/generated
preferred-supported-locales: ["en", "zh", "ar", "es", "fr", "de", "ja", "ko"]
```

## 2. 本地化资源文件

### 2.1 英文资源文件

```json
// lib/l10n/app_en.arb
{
  "@@locale": "en",
  "@@last_modified": "2024-01-01T00:00:00.000Z",
  
  "appTitle": "Enterprise App",
  "@appTitle": {
    "description": "The title of the application"
  },
  
  "welcome": "Welcome",
  "@welcome": {
    "description": "Welcome message"
  },
  
  "welcomeMessage": "Welcome, {name}!",
  "@welcomeMessage": {
    "description": "Welcome message with user name",
    "placeholders": {
      "name": {
        "type": "String",
        "example": "John"
      }
    }
  },
  
  "itemCount": "{count, plural, =0{No items} =1{One item} other{{count} items}}",
  "@itemCount": {
    "description": "Number of items",
    "placeholders": {
      "count": {
        "type": "int",
        "format": "compact"
      }
    }
  },
  
  "lastUpdated": "Last updated: {date}",
  "@lastUpdated": {
    "description": "Last updated date",
    "placeholders": {
      "date": {
        "type": "DateTime",
        "format": "yMMMd"
      }
    }
  },
  
  "price": "Price: {amount}",
  "@price": {
    "description": "Price with currency",
    "placeholders": {
      "amount": {
        "type": "double",
        "format": "currency",
        "optionalParameters": {
          "symbol": "$"
        }
      }
    }
  },
  
  "login": "Login",
  "logout": "Logout",
  "email": "Email",
  "password": "Password",
  "forgotPassword": "Forgot Password?",
  "register": "Register",
  "cancel": "Cancel",
  "confirm": "Confirm",
  "save": "Save",
  "delete": "Delete",
  "edit": "Edit",
  "add": "Add",
  "search": "Search",
  "filter": "Filter",
  "sort": "Sort",
  "refresh": "Refresh",
  "loading": "Loading...",
  "error": "Error",
  "success": "Success",
  "warning": "Warning",
  "info": "Information",
  
  "validationRequired": "This field is required",
  "validationEmail": "Please enter a valid email address",
  "validationPassword": "Password must be at least 8 characters",
  "validationPasswordMatch": "Passwords do not match",
  
  "networkError": "Network connection error",
  "serverError": "Server error occurred",
  "unknownError": "An unknown error occurred",
  
  "settings": "Settings",
  "language": "Language",
  "theme": "Theme",
  "notifications": "Notifications",
  "privacy": "Privacy",
  "about": "About",
  
  "profile": "Profile",
  "firstName": "First Name",
  "lastName": "Last Name",
  "phoneNumber": "Phone Number",
  "address": "Address",
  "city": "City",
  "country": "Country",
  "zipCode": "ZIP Code",
  
  "dashboard": "Dashboard",
  "analytics": "Analytics",
  "reports": "Reports",
  "users": "Users",
  "products": "Products",
  "orders": "Orders",
  "inventory": "Inventory",
  
  "today": "Today",
  "yesterday": "Yesterday",
  "thisWeek": "This Week",
  "thisMonth": "This Month",
  "thisYear": "This Year"
}
```

### 2.2 中文资源文件

```json
// lib/l10n/app_zh.arb
{
  "@@locale": "zh",
  "@@last_modified": "2024-01-01T00:00:00.000Z",
  
  "appTitle": "企业应用",
  "welcome": "欢迎",
  "welcomeMessage": "欢迎，{name}！",
  "itemCount": "{count, plural, =0{没有项目} =1{一个项目} other{{count} 个项目}}",
  "lastUpdated": "最后更新：{date}",
  "price": "价格：{amount}",
  
  "login": "登录",
  "logout": "退出登录",
  "email": "邮箱",
  "password": "密码",
  "forgotPassword": "忘记密码？",
  "register": "注册",
  "cancel": "取消",
  "confirm": "确认",
  "save": "保存",
  "delete": "删除",
  "edit": "编辑",
  "add": "添加",
  "search": "搜索",
  "filter": "筛选",
  "sort": "排序",
  "refresh": "刷新",
  "loading": "加载中...",
  "error": "错误",
  "success": "成功",
  "warning": "警告",
  "info": "信息",
  
  "validationRequired": "此字段为必填项",
  "validationEmail": "请输入有效的邮箱地址",
  "validationPassword": "密码至少需要8个字符",
  "validationPasswordMatch": "密码不匹配",
  
  "networkError": "网络连接错误",
  "serverError": "服务器错误",
  "unknownError": "发生未知错误",
  
  "settings": "设置",
  "language": "语言",
  "theme": "主题",
  "notifications": "通知",
  "privacy": "隐私",
  "about": "关于",
  
  "profile": "个人资料",
  "firstName": "名",
  "lastName": "姓",
  "phoneNumber": "电话号码",
  "address": "地址",
  "city": "城市",
  "country": "国家",
  "zipCode": "邮政编码",
  
  "dashboard": "仪表板",
  "analytics": "分析",
  "reports": "报告",
  "users": "用户",
  "products": "产品",
  "orders": "订单",
  "inventory": "库存",
  
  "today": "今天",
  "yesterday": "昨天",
  "thisWeek": "本周",
  "thisMonth": "本月",
  "thisYear": "今年"
}
```

### 2.3 阿拉伯文资源文件

```json
// lib/l10n/app_ar.arb
{
  "@@locale": "ar",
  "@@last_modified": "2024-01-01T00:00:00.000Z",
  
  "appTitle": "تطبيق المؤسسة",
  "welcome": "مرحباً",
  "welcomeMessage": "مرحباً، {name}!",
  "itemCount": "{count, plural, =0{لا توجد عناصر} =1{عنصر واحد} other{{count} عناصر}}",
  "lastUpdated": "آخر تحديث: {date}",
  "price": "السعر: {amount}",
  
  "login": "تسجيل الدخول",
  "logout": "تسجيل الخروج",
  "email": "البريد الإلكتروني",
  "password": "كلمة المرور",
  "forgotPassword": "نسيت كلمة المرور؟",
  "register": "التسجيل",
  "cancel": "إلغاء",
  "confirm": "تأكيد",
  "save": "حفظ",
  "delete": "حذف",
  "edit": "تعديل",
  "add": "إضافة",
  "search": "بحث",
  "filter": "تصفية",
  "sort": "ترتيب",
  "refresh": "تحديث",
  "loading": "جاري التحميل...",
  "error": "خطأ",
  "success": "نجح",
  "warning": "تحذير",
  "info": "معلومات",
  
  "validationRequired": "هذا الحقل مطلوب",
  "validationEmail": "يرجى إدخال عنوان بريد إلكتروني صحيح",
  "validationPassword": "يجب أن تكون كلمة المرور 8 أحرف على الأقل",
  "validationPasswordMatch": "كلمات المرور غير متطابقة",
  
  "networkError": "خطأ في الاتصال بالشبكة",
  "serverError": "حدث خطأ في الخادم",
  "unknownError": "حدث خطأ غير معروف",
  
  "settings": "الإعدادات",
  "language": "اللغة",
  "theme": "المظهر",
  "notifications": "الإشعارات",
  "privacy": "الخصوصية",
  "about": "حول",
  
  "profile": "الملف الشخصي",
  "firstName": "الاسم الأول",
  "lastName": "اسم العائلة",
  "phoneNumber": "رقم الهاتف",
  "address": "العنوان",
  "city": "المدينة",
  "country": "البلد",
  "zipCode": "الرمز البريدي",
  
  "dashboard": "لوحة التحكم",
  "analytics": "التحليلات",
  "reports": "التقارير",
  "users": "المستخدمون",
  "products": "المنتجات",
  "orders": "الطلبات",
  "inventory": "المخزون",
  
  "today": "اليوم",
  "yesterday": "أمس",
  "thisWeek": "هذا الأسبوع",
  "thisMonth": "هذا الشهر",
  "thisYear": "هذا العام"
}
```

## 3. 本地化管理服务

### 3.1 语言配置模型

```dart
// lib/core/localization/models/language_config.dart
import 'package:flutter/material.dart';

class LanguageConfig {
  final String code;
  final String name;
  final String nativeName;
  final Locale locale;
  final String flagAsset;
  final bool isRTL;
  final String fontFamily;
  final TextDirection textDirection;

  const LanguageConfig({
    required this.code,
    required this.name,
    required this.nativeName,
    required this.locale,
    required this.flagAsset,
    required this.isRTL,
    required this.fontFamily,
  }) : textDirection = isRTL ? TextDirection.rtl : TextDirection.ltr;

  Map<String, dynamic> toJson() => {
        'code': code,
        'name': name,
        'nativeName': nativeName,
        'locale': {
          'languageCode': locale.languageCode,
          'countryCode': locale.countryCode,
        },
        'flagAsset': flagAsset,
        'isRTL': isRTL,
        'fontFamily': fontFamily,
      };

  factory LanguageConfig.fromJson(Map<String, dynamic> json) {
    final localeData = json['locale'] as Map<String, dynamic>;
    return LanguageConfig(
      code: json['code'] as String,
      name: json['name'] as String,
      nativeName: json['nativeName'] as String,
      locale: Locale(
        localeData['languageCode'] as String,
        localeData['countryCode'] as String?,
      ),
      flagAsset: json['flagAsset'] as String,
      isRTL: json['isRTL'] as bool,
      fontFamily: json['fontFamily'] as String,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LanguageConfig &&
          runtimeType == other.runtimeType &&
          code == other.code;

  @override
  int get hashCode => code.hashCode;

  @override
  String toString() => 'LanguageConfig(code: $code, name: $name)';
}

// 预定义的语言配置
class SupportedLanguages {
  static const english = LanguageConfig(
    code: 'en',
    name: 'English',
    nativeName: 'English',
    locale: Locale('en', 'US'),
    flagAsset: 'assets/images/flags/us.png',
    isRTL: false,
    fontFamily: 'Roboto',
  );

  static const chinese = LanguageConfig(
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文',
    locale: Locale('zh', 'CN'),
    flagAsset: 'assets/images/flags/cn.png',
    isRTL: false,
    fontFamily: 'NotoSans',
  );

  static const arabic = LanguageConfig(
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    locale: Locale('ar', 'SA'),
    flagAsset: 'assets/images/flags/sa.png',
    isRTL: true,
    fontFamily: 'NotoSansArabic',
  );

  static const spanish = LanguageConfig(
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    locale: Locale('es', 'ES'),
    flagAsset: 'assets/images/flags/es.png',
    isRTL: false,
    fontFamily: 'Roboto',
  );

  static const french = LanguageConfig(
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    locale: Locale('fr', 'FR'),
    flagAsset: 'assets/images/flags/fr.png',
    isRTL: false,
    fontFamily: 'Roboto',
  );

  static const german = LanguageConfig(
    code: 'de',
    name: 'German',
    nativeName: 'Deutsch',
    locale: Locale('de', 'DE'),
    flagAsset: 'assets/images/flags/de.png',
    isRTL: false,
    fontFamily: 'Roboto',
  );

  static const japanese = LanguageConfig(
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    locale: Locale('ja', 'JP'),
    flagAsset: 'assets/images/flags/jp.png',
    isRTL: false,
    fontFamily: 'NotoSans',
  );

  static const korean = LanguageConfig(
    code: 'ko',
    name: 'Korean',
    nativeName: '한국어',
    locale: Locale('ko', 'KR'),
    flagAsset: 'assets/images/flags/kr.png',
    isRTL: false,
    fontFamily: 'NotoSans',
  );

  static const List<LanguageConfig> all = [
    english,
    chinese,
    arabic,
    spanish,
    french,
    german,
    japanese,
    korean,
  ];

  static LanguageConfig? getByCode(String code) {
    try {
      return all.firstWhere((lang) => lang.code == code);
    } catch (e) {
      return null;
    }
  }

  static LanguageConfig? getByLocale(Locale locale) {
    try {
      return all.firstWhere(
        (lang) => lang.locale.languageCode == locale.languageCode,
      );
    } catch (e) {
      return null;
    }
  }
}
```

### 3.2 本地化服务

```dart
// lib/core/localization/services/localization_service.dart
import 'dart:convert';
import 'dart:ui';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/language_config.dart';

abstract class LocalizationService {
  Future<void> initialize();
  Future<void> setLanguage(LanguageConfig language);
  LanguageConfig get currentLanguage;
  List<LanguageConfig> get supportedLanguages;
  LanguageConfig get systemLanguage;
  Future<Map<String, String>> loadTranslations(String languageCode);
  Stream<LanguageConfig> get languageStream;
}

class LocalizationServiceImpl implements LocalizationService {
  static const String _languageKey = 'selected_language';
  
  late SharedPreferences _prefs;
  late LanguageConfig _currentLanguage;
  final Map<String, Map<String, String>> _translationsCache = {};
  
  // 语言变更流控制器
  final _languageController = StreamController<LanguageConfig>.broadcast();
  
  @override
  Stream<LanguageConfig> get languageStream => _languageController.stream;

  @override
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    
    // 获取保存的语言设置
    final savedLanguageCode = _prefs.getString(_languageKey);
    
    if (savedLanguageCode != null) {
      final savedLanguage = SupportedLanguages.getByCode(savedLanguageCode);
      _currentLanguage = savedLanguage ?? systemLanguage;
    } else {
      _currentLanguage = systemLanguage;
    }
    
    // 预加载当前语言的翻译
    await loadTranslations(_currentLanguage.code);
  }

  @override
  Future<void> setLanguage(LanguageConfig language) async {
    if (_currentLanguage == language) return;
    
    _currentLanguage = language;
    
    // 保存语言设置
    await _prefs.setString(_languageKey, language.code);
    
    // 预加载翻译
    await loadTranslations(language.code);
    
    // 通知语言变更
    _languageController.add(language);
  }

  @override
  LanguageConfig get currentLanguage => _currentLanguage;

  @override
  List<LanguageConfig> get supportedLanguages => SupportedLanguages.all;

  @override
  LanguageConfig get systemLanguage {
    final systemLocale = PlatformDispatcher.instance.locale;
    return SupportedLanguages.getByLocale(systemLocale) ?? 
           SupportedLanguages.english;
  }

  @override
  Future<Map<String, String>> loadTranslations(String languageCode) async {
    // 检查缓存
    if (_translationsCache.containsKey(languageCode)) {
      return _translationsCache[languageCode]!;
    }
    
    try {
      // 从 assets 加载翻译文件
      final jsonString = await rootBundle.loadString(
        'lib/l10n/app_$languageCode.arb',
      );
      
      final Map<String, dynamic> jsonMap = json.decode(jsonString);
      final Map<String, String> translations = {};
      
      // 过滤掉元数据，只保留翻译文本
      for (final entry in jsonMap.entries) {
        if (!entry.key.startsWith('@') && !entry.key.startsWith('@@')) {
          translations[entry.key] = entry.value.toString();
        }
      }
      
      // 缓存翻译
      _translationsCache[languageCode] = translations;
      
      return translations;
    } catch (e) {
      // 如果加载失败，返回英文翻译作为后备
      if (languageCode != 'en') {
        return await loadTranslations('en');
      }
      
      throw Exception('Failed to load translations for $languageCode: $e');
    }
  }
  
  // 获取翻译文本
  String translate(String key, [Map<String, dynamic>? params]) {
    final translations = _translationsCache[_currentLanguage.code];
    if (translations == null) {
      return key; // 返回 key 作为后备
    }
    
    String translation = translations[key] ?? key;
    
    // 处理参数替换
    if (params != null) {
      params.forEach((paramKey, value) {
        translation = translation.replaceAll('{$paramKey}', value.toString());
      });
    }
    
    return translation;
  }
  
  // 清理资源
  void dispose() {
    _languageController.close();
  }
}
```

### 3.3 本地化状态管理

```dart
// lib/core/localization/bloc/localization_bloc.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../models/language_config.dart';
import '../services/localization_service.dart';

// Events
abstract class LocalizationEvent extends Equatable {
  const LocalizationEvent();
  
  @override
  List<Object?> get props => [];
}

class InitializeLocalization extends LocalizationEvent {
  const InitializeLocalization();
}

class ChangeLanguage extends LocalizationEvent {
  final LanguageConfig language;
  
  const ChangeLanguage(this.language);
  
  @override
  List<Object?> get props => [language];
}

class LoadTranslations extends LocalizationEvent {
  final String languageCode;
  
  const LoadTranslations(this.languageCode);
  
  @override
  List<Object?> get props => [languageCode];
}

// States
abstract class LocalizationState extends Equatable {
  const LocalizationState();
  
  @override
  List<Object?> get props => [];
}

class LocalizationInitial extends LocalizationState {
  const LocalizationInitial();
}

class LocalizationLoading extends LocalizationState {
  const LocalizationLoading();
}

class LocalizationLoaded extends LocalizationState {
  final LanguageConfig currentLanguage;
  final List<LanguageConfig> supportedLanguages;
  final Map<String, String> translations;
  
  const LocalizationLoaded({
    required this.currentLanguage,
    required this.supportedLanguages,
    required this.translations,
  });
  
  @override
  List<Object?> get props => [
    currentLanguage,
    supportedLanguages,
    translations,
  ];
}

class LocalizationError extends LocalizationState {
  final String message;
  
  const LocalizationError(this.message);
  
  @override
  List<Object?> get props => [message];
}

// BLoC
class LocalizationBloc extends Bloc<LocalizationEvent, LocalizationState> {
  final LocalizationService _localizationService;
  
  LocalizationBloc(this._localizationService) : super(const LocalizationInitial()) {
    on<InitializeLocalization>(_onInitializeLocalization);
    on<ChangeLanguage>(_onChangeLanguage);
    on<LoadTranslations>(_onLoadTranslations);
    
    // 监听语言变更
    _localizationService.languageStream.listen((language) {
      add(LoadTranslations(language.code));
    });
  }
  
  Future<void> _onInitializeLocalization(
    InitializeLocalization event,
    Emitter<LocalizationState> emit,
  ) async {
    try {
      emit(const LocalizationLoading());
      
      await _localizationService.initialize();
      
      final currentLanguage = _localizationService.currentLanguage;
      final supportedLanguages = _localizationService.supportedLanguages;
      final translations = await _localizationService.loadTranslations(
        currentLanguage.code,
      );
      
      emit(LocalizationLoaded(
        currentLanguage: currentLanguage,
        supportedLanguages: supportedLanguages,
        translations: translations,
      ));
    } catch (e) {
      emit(LocalizationError('Failed to initialize localization: $e'));
    }
  }
  
  Future<void> _onChangeLanguage(
    ChangeLanguage event,
    Emitter<LocalizationState> emit,
  ) async {
    try {
      await _localizationService.setLanguage(event.language);
      
      final translations = await _localizationService.loadTranslations(
        event.language.code,
      );
      
      if (state is LocalizationLoaded) {
        final currentState = state as LocalizationLoaded;
        emit(LocalizationLoaded(
          currentLanguage: event.language,
          supportedLanguages: currentState.supportedLanguages,
          translations: translations,
        ));
      }
    } catch (e) {
      emit(LocalizationError('Failed to change language: $e'));
    }
  }
  
  Future<void> _onLoadTranslations(
    LoadTranslations event,
    Emitter<LocalizationState> emit,
  ) async {
    try {
      final translations = await _localizationService.loadTranslations(
        event.languageCode,
      );
      
      if (state is LocalizationLoaded) {
        final currentState = state as LocalizationLoaded;
        emit(LocalizationLoaded(
          currentLanguage: currentState.currentLanguage,
          supportedLanguages: currentState.supportedLanguages,
          translations: translations,
        ));
      }
    } catch (e) {
      emit(LocalizationError('Failed to load translations: $e'));
    }
  }
}
```

## 4. 格式化工具

### 4.1 日期时间格式化

```dart
// lib/core/localization/formatters/date_time_formatter.dart
import 'package:intl/intl.dart';
import 'package:timezone/timezone.dart' as tz;
import '../models/language_config.dart';

class DateTimeFormatter {
  final LanguageConfig _languageConfig;
  
  DateTimeFormatter(this._languageConfig);
  
  // 格式化日期
  String formatDate(DateTime date, {String? pattern}) {
    final locale = _languageConfig.locale.toString();
    final formatter = DateFormat(pattern ?? 'yMMMd', locale);
    return formatter.format(date);
  }
  
  // 格式化时间
  String formatTime(DateTime time, {bool use24Hour = false}) {
    final locale = _languageConfig.locale.toString();
    final pattern = use24Hour ? 'HH:mm' : 'h:mm a';
    final formatter = DateFormat(pattern, locale);
    return formatter.format(time);
  }
  
  // 格式化日期时间
  String formatDateTime(DateTime dateTime, {String? pattern}) {
    final locale = _languageConfig.locale.toString();
    final formatter = DateFormat(pattern ?? 'yMMMd HH:mm', locale);
    return formatter.format(dateTime);
  }
  
  // 相对时间格式化
  String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return _getTranslation('yesterday');
      } else if (difference.inDays < 7) {
        return '${difference.inDays} ${_getTranslation('daysAgo')}';
      } else {
        return formatDate(dateTime);
      }
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ${_getTranslation('hoursAgo')}';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} ${_getTranslation('minutesAgo')}';
    } else {
      return _getTranslation('justNow');
    }
  }
  
  // 格式化时区
  String formatWithTimezone(DateTime dateTime, String timezoneName) {
    final location = tz.getLocation(timezoneName);
    final zonedDateTime = tz.TZDateTime.from(dateTime, location);
    return formatDateTime(zonedDateTime);
  }
  
  // 格式化持续时间
  String formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '$hours:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }
  
  // 获取月份名称
  List<String> getMonthNames() {
    final locale = _languageConfig.locale.toString();
    final formatter = DateFormat('MMMM', locale);
    return List.generate(12, (index) {
      final date = DateTime(2024, index + 1, 1);
      return formatter.format(date);
    });
  }
  
  // 获取星期名称
  List<String> getWeekdayNames() {
    final locale = _languageConfig.locale.toString();
    final formatter = DateFormat('EEEE', locale);
    return List.generate(7, (index) {
      final date = DateTime(2024, 1, index + 1); // 2024年1月1日是星期一
      return formatter.format(date);
    });
  }
  
  String _getTranslation(String key) {
    // 这里应该从本地化服务获取翻译
    // 为了简化，这里返回英文
    switch (key) {
      case 'yesterday': return 'Yesterday';
      case 'daysAgo': return 'days ago';
      case 'hoursAgo': return 'hours ago';
      case 'minutesAgo': return 'minutes ago';
      case 'justNow': return 'Just now';
      default: return key;
    }
  }
}
```

### 4.2 数字和货币格式化

```dart
// lib/core/localization/formatters/number_formatter.dart
import 'package:intl/intl.dart';
import 'package:money2/money2.dart';
import '../models/language_config.dart';

class NumberFormatter {
  final LanguageConfig _languageConfig;
  
  NumberFormatter(this._languageConfig);
  
  // 格式化整数
  String formatInteger(int number) {
    final locale = _languageConfig.locale.toString();
    final formatter = NumberFormat('#,###', locale);
    return formatter.format(number);
  }
  
  // 格式化小数
  String formatDecimal(double number, {int decimalPlaces = 2}) {
    final locale = _languageConfig.locale.toString();
    final pattern = '#,##0.${'0' * decimalPlaces}';
    final formatter = NumberFormat(pattern, locale);
    return formatter.format(number);
  }
  
  // 格式化百分比
  String formatPercentage(double number, {int decimalPlaces = 1}) {
    final locale = _languageConfig.locale.toString();
    final formatter = NumberFormat.percentPattern(locale);
    formatter.minimumFractionDigits = decimalPlaces;
    formatter.maximumFractionDigits = decimalPlaces;
    return formatter.format(number);
  }
  
  // 格式化货币
  String formatCurrency(double amount, String currencyCode) {
    final locale = _languageConfig.locale.toString();
    final formatter = NumberFormat.currency(
      locale: locale,
      symbol: _getCurrencySymbol(currencyCode),
      decimalDigits: _getCurrencyDecimalDigits(currencyCode),
    );
    return formatter.format(amount);
  }
  
  // 使用 Money2 包格式化货币
  String formatMoneyAmount(Money money) {
    final locale = _languageConfig.locale.toString();
    return money.format(locale);
  }
  
  // 格式化紧凑数字（如 1K, 1M）
  String formatCompact(num number) {
    final locale = _languageConfig.locale.toString();
    final formatter = NumberFormat.compact(locale: locale);
    return formatter.format(number);
  }
  
  // 格式化科学计数法
  String formatScientific(double number) {
    final locale = _languageConfig.locale.toString();
    final formatter = NumberFormat.scientificPattern(locale);
    return formatter.format(number);
  }
  
  // 格式化文件大小
  String formatFileSize(int bytes) {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    double size = bytes.toDouble();
    int unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return '${formatDecimal(size, decimalPlaces: unitIndex == 0 ? 0 : 1)} ${units[unitIndex]}';
  }
  
  // 格式化序数（1st, 2nd, 3rd, etc.）
  String formatOrdinal(int number) {
    final locale = _languageConfig.locale.toString();
    
    // 英文序数规则
    if (locale.startsWith('en')) {
      if (number % 100 >= 11 && number % 100 <= 13) {
        return '${number}th';
      }
      switch (number % 10) {
        case 1: return '${number}st';
        case 2: return '${number}nd';
        case 3: return '${number}rd';
        default: return '${number}th';
      }
    }
    
    // 其他语言直接返回数字
    return number.toString();
  }
  
  // 解析数字
  double? parseNumber(String text) {
    try {
      final locale = _languageConfig.locale.toString();
      final formatter = NumberFormat('#,###.##', locale);
      return formatter.parse(text).toDouble();
    } catch (e) {
      return null;
    }
  }
  
  // 获取货币符号
  String _getCurrencySymbol(String currencyCode) {
    switch (currencyCode.toUpperCase()) {
      case 'USD': return r'$';
      case 'EUR': return '€';
      case 'GBP': return '£';
      case 'JPY': return '¥';
      case 'CNY': return '¥';
      case 'KRW': return '₩';
      case 'SAR': return 'ر.س';
      default: return currencyCode;
    }
  }
  
  // 获取货币小数位数
  int _getCurrencyDecimalDigits(String currencyCode) {
    switch (currencyCode.toUpperCase()) {
      case 'JPY':
      case 'KRW':
        return 0;
      default:
        return 2;
    }
  }
}
```

### 4.3 电话号码格式化

```dart
// lib/core/localization/formatters/phone_formatter.dart
import 'package:phone_numbers_parser/phone_numbers_parser.dart';
import '../models/language_config.dart';

class PhoneFormatter {
  final LanguageConfig _languageConfig;
  
  PhoneFormatter(this._languageConfig);
  
  // 格式化电话号码
  String formatPhoneNumber(String phoneNumber, {String? countryCode}) {
    try {
      final isoCode = countryCode ?? _getCountryCodeFromLocale();
      final parsedNumber = PhoneNumber.parse(phoneNumber, destinationCountry: IsoCode.fromCode(isoCode));
      return parsedNumber.formatNsn();
    } catch (e) {
      return phoneNumber; // 如果解析失败，返回原始号码
    }
  }
  
  // 格式化国际电话号码
  String formatInternationalPhoneNumber(String phoneNumber, {String? countryCode}) {
    try {
      final isoCode = countryCode ?? _getCountryCodeFromLocale();
      final parsedNumber = PhoneNumber.parse(phoneNumber, destinationCountry: IsoCode.fromCode(isoCode));
      return parsedNumber.formatE164();
    } catch (e) {
      return phoneNumber;
    }
  }
  
  // 验证电话号码
  bool isValidPhoneNumber(String phoneNumber, {String? countryCode}) {
    try {
      final isoCode = countryCode ?? _getCountryCodeFromLocale();
      final parsedNumber = PhoneNumber.parse(phoneNumber, destinationCountry: IsoCode.fromCode(isoCode));
      return parsedNumber.isValid();
    } catch (e) {
      return false;
    }
  }
  
  // 获取电话号码类型
  PhoneNumberType? getPhoneNumberType(String phoneNumber, {String? countryCode}) {
    try {
      final isoCode = countryCode ?? _getCountryCodeFromLocale();
      final parsedNumber = PhoneNumber.parse(phoneNumber, destinationCountry: IsoCode.fromCode(isoCode));
      return parsedNumber.phoneNumberType;
    } catch (e) {
      return null;
    }
  }
  
  // 从语言配置获取国家代码
  String _getCountryCodeFromLocale() {
    return _languageConfig.locale.countryCode ?? 'US';
  }
  
  // 格式化为显示格式
  String formatForDisplay(String phoneNumber, {String? countryCode}) {
    try {
      final isoCode = countryCode ?? _getCountryCodeFromLocale();
      final parsedNumber = PhoneNumber.parse(phoneNumber, destinationCountry: IsoCode.fromCode(isoCode));
      
      // 根据地区使用不同的格式
      switch (isoCode) {
        case 'US':
        case 'CA':
          return parsedNumber.formatNsn(); // (*************
        case 'CN':
          return parsedNumber.formatNsn(); // 138 0013 8000
        case 'SA':
        case 'AE':
          return parsedNumber.formatNsn(); // ************
        default:
          return parsedNumber.formatNsn();
      }
    } catch (e) {
      return phoneNumber;
    }
  }
}
```

## 5. 本地化组件

### 5.1 语言选择器

```dart
// lib/core/localization/widgets/language_selector.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/localization_bloc.dart';
import '../models/language_config.dart';

class LanguageSelector extends StatelessWidget {
  final bool showFlags;
  final bool showNativeNames;
  final EdgeInsetsGeometry? padding;
  final TextStyle? textStyle;
  final Color? backgroundColor;
  final double? borderRadius;
  
  const LanguageSelector({
    super.key,
    this.showFlags = true,
    this.showNativeNames = true,
    this.padding,
    this.textStyle,
    this.backgroundColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LocalizationBloc, LocalizationState>(
      builder: (context, state) {
        if (state is! LocalizationLoaded) {
          return const SizedBox.shrink();
        }
        
        return Container(
          padding: padding ?? const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: backgroundColor ?? Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(borderRadius ?? 8),
            border: Border.all(
              color: Theme.of(context).dividerColor,
              width: 1,
            ),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<LanguageConfig>(
              value: state.currentLanguage,
              icon: const Icon(Icons.arrow_drop_down),
              isExpanded: true,
              items: state.supportedLanguages.map((language) {
                return DropdownMenuItem<LanguageConfig>(
                  value: language,
                  child: _buildLanguageItem(language),
                );
              }).toList(),
              onChanged: (LanguageConfig? newLanguage) {
                if (newLanguage != null) {
                  context.read<LocalizationBloc>().add(
                    ChangeLanguage(newLanguage),
                  );
                }
              },
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildLanguageItem(LanguageConfig language) {
    return Row(
      children: [
        if (showFlags) ..[
          Image.asset(
            language.flagAsset,
            width: 24,
            height: 16,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                width: 24,
                height: 16,
                color: Colors.grey[300],
                child: const Icon(Icons.flag, size: 12),
              );
            },
          ),
          const SizedBox(width: 8),
        ],
        Expanded(
          child: Text(
            showNativeNames ? language.nativeName : language.name,
            style: textStyle,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
```

### 5.2 本地化文本组件

```dart
// lib/core/localization/widgets/localized_text.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/localization_bloc.dart';
import '../services/localization_service.dart';

class LocalizedText extends StatelessWidget {
  final String key;
  final Map<String, dynamic>? params;
  final TextStyle? style;
  final TextAlign? textAlign;
  final TextDirection? textDirection;
  final int? maxLines;
  final TextOverflow? overflow;
  final String? fallback;
  
  const LocalizedText(
    this.key, {
    super.key,
    this.params,
    this.style,
    this.textAlign,
    this.textDirection,
    this.maxLines,
    this.overflow,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LocalizationBloc, LocalizationState>(
      builder: (context, state) {
        String text;
        
        if (state is LocalizationLoaded) {
          text = _getTranslatedText(state.translations);
        } else {
          text = fallback ?? key;
        }
        
        return Text(
          text,
          style: style,
          textAlign: textAlign,
          textDirection: textDirection,
          maxLines: maxLines,
          overflow: overflow,
        );
      },
    );
  }
  
  String _getTranslatedText(Map<String, String> translations) {
    String text = translations[key] ?? fallback ?? key;
    
    // 处理参数替换
    if (params != null) {
      params!.forEach((paramKey, value) {
        text = text.replaceAll('{$paramKey}', value.toString());
      });
    }
    
    return text;
  }
}

// 便捷的扩展方法
extension LocalizationExtension on BuildContext {
  String tr(String key, [Map<String, dynamic>? params]) {
    final localizationService = read<LocalizationService>();
    return localizationService.translate(key, params);
  }
  
  LanguageConfig get currentLanguage {
    final state = read<LocalizationBloc>().state;
    if (state is LocalizationLoaded) {
      return state.currentLanguage;
    }
    return SupportedLanguages.english;
  }
}
```

### 5.3 RTL 支持组件

```dart
// lib/core/localization/widgets/rtl_support.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/localization_bloc.dart';

class RTLSupport extends StatelessWidget {
  final Widget child;
  
  const RTLSupport({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LocalizationBloc, LocalizationState>(
      builder: (context, state) {
        if (state is LocalizationLoaded) {
          return Directionality(
            textDirection: state.currentLanguage.textDirection,
            child: child,
          );
        }
        
        return child;
      },
    );
  }
}

// RTL 感知的 Padding
class RTLPadding extends StatelessWidget {
  final EdgeInsetsGeometry padding;
  final Widget child;
  
  const RTLPadding({
    super.key,
    required this.padding,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LocalizationBloc, LocalizationState>(
      builder: (context, state) {
        EdgeInsetsGeometry effectivePadding = padding;
        
        if (state is LocalizationLoaded && state.currentLanguage.isRTL) {
          // 对于 RTL 语言，翻转左右边距
          if (padding is EdgeInsets) {
            final edgeInsets = padding as EdgeInsets;
            effectivePadding = EdgeInsets.only(
              top: edgeInsets.top,
              right: edgeInsets.left,
              bottom: edgeInsets.bottom,
              left: edgeInsets.right,
            );
          }
        }
        
        return Padding(
          padding: effectivePadding,
          child: child,
        );
      },
    );
  }
}

// RTL 感知的 Row
class RTLRow extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;
  
  const RTLRow({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LocalizationBloc, LocalizationState>(
      builder: (context, state) {
        List<Widget> effectiveChildren = children;
        MainAxisAlignment effectiveAlignment = mainAxisAlignment;
        
        if (state is LocalizationLoaded && state.currentLanguage.isRTL) {
          // 对于 RTL 语言，翻转子组件顺序和对齐方式
          effectiveChildren = children.reversed.toList();
          
          switch (mainAxisAlignment) {
            case MainAxisAlignment.start:
              effectiveAlignment = MainAxisAlignment.end;
              break;
            case MainAxisAlignment.end:
              effectiveAlignment = MainAxisAlignment.start;
              break;
            default:
              effectiveAlignment = mainAxisAlignment;
          }
        }
        
        return Row(
          mainAxisAlignment: effectiveAlignment,
          crossAxisAlignment: crossAxisAlignment,
          mainAxisSize: mainAxisSize,
          children: effectiveChildren,
        );
      },
    );
  }
}

// RTL 感知的图标按钮
class RTLIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final String? tooltip;
  final Color? color;
  final double? iconSize;
  
  const RTLIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.tooltip,
    this.color,
    this.iconSize,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LocalizationBloc, LocalizationState>(
      builder: (context, state) {
        IconData effectiveIcon = icon;
        
        if (state is LocalizationLoaded && state.currentLanguage.isRTL) {
          // 对于 RTL 语言，翻转方向性图标
          switch (icon) {
            case Icons.arrow_back:
              effectiveIcon = Icons.arrow_forward;
              break;
            case Icons.arrow_forward:
              effectiveIcon = Icons.arrow_back;
              break;
            case Icons.chevron_left:
              effectiveIcon = Icons.chevron_right;
              break;
            case Icons.chevron_right:
              effectiveIcon = Icons.chevron_left;
              break;
            case Icons.keyboard_arrow_left:
              effectiveIcon = Icons.keyboard_arrow_right;
              break;
            case Icons.keyboard_arrow_right:
              effectiveIcon = Icons.keyboard_arrow_left;
              break;
          }
        }
        
        return IconButton(
          icon: Icon(effectiveIcon),
          onPressed: onPressed,
          tooltip: tooltip,
          color: color,
          iconSize: iconSize,
        );
      },
    );
  }
}
```

## 6. 应用集成

### 6.1 主应用配置

```dart
// lib/main.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get_it/get_it.dart';
import 'package:timezone/data/latest.dart' as tz;

import 'core/localization/bloc/localization_bloc.dart';
import 'core/localization/services/localization_service.dart';
import 'core/localization/widgets/rtl_support.dart';
import 'l10n/generated/app_localizations.dart';
import 'app.dart';

final getIt = GetIt.instance;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化时区数据
  tz.initializeTimeZones();
  
  // 注册依赖
  await _setupDependencies();
  
  runApp(const MyApp());
}

Future<void> _setupDependencies() async {
  // 注册本地化服务
  getIt.registerSingleton<LocalizationService>(
    LocalizationServiceImpl(),
  );
  
  // 初始化本地化服务
  await getIt<LocalizationService>().initialize();
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => LocalizationBloc(getIt<LocalizationService>())
        ..add(const InitializeLocalization()),
      child: BlocBuilder<LocalizationBloc, LocalizationState>(
        builder: (context, state) {
          if (state is LocalizationLoaded) {
            return RTLSupport(
              child: MaterialApp(
                title: 'Enterprise App',
                debugShowCheckedModeBanner: false,
                
                // 本地化配置
                locale: state.currentLanguage.locale,
                localizationsDelegates: const [
                  AppLocalizations.delegate,
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                ],
                supportedLocales: state.supportedLanguages
                    .map((lang) => lang.locale)
                    .toList(),
                
                // 主题配置
                theme: _buildTheme(state.currentLanguage),
                darkTheme: _buildDarkTheme(state.currentLanguage),
                
                home: const AppHomePage(),
              ),
            );
          }
          
          return MaterialApp(
            home: Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            ),
          );
        },
      ),
    );
  }
  
  ThemeData _buildTheme(LanguageConfig languageConfig) {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
      fontFamily: languageConfig.fontFamily,
      
      // RTL 支持
      visualDensity: VisualDensity.adaptivePlatformDensity,
      
      // 文本主题
      textTheme: TextTheme(
        displayLarge: TextStyle(
          fontFamily: languageConfig.fontFamily,
          fontSize: languageConfig.isRTL ? 56 : 57,
        ),
        displayMedium: TextStyle(
          fontFamily: languageConfig.fontFamily,
          fontSize: languageConfig.isRTL ? 44 : 45,
        ),
        displaySmall: TextStyle(
          fontFamily: languageConfig.fontFamily,
          fontSize: languageConfig.isRTL ? 35 : 36,
        ),
        headlineLarge: TextStyle(
          fontFamily: languageConfig.fontFamily,
          fontSize: languageConfig.isRTL ? 31 : 32,
        ),
        headlineMedium: TextStyle(
          fontFamily: languageConfig.fontFamily,
          fontSize: languageConfig.isRTL ? 27 : 28,
        ),
        headlineSmall: TextStyle(
          fontFamily: languageConfig.fontFamily,
          fontSize: languageConfig.isRTL ? 23 : 24,
        ),
        titleLarge: TextStyle(
          fontFamily: languageConfig.fontFamily,
          fontSize: languageConfig.isRTL ? 21 : 22,
        ),
        titleMedium: TextStyle(
          fontFamily: languageConfig.fontFamily,
          fontSize: languageConfig.isRTL ? 15 : 16,
        ),
        titleSmall: TextStyle(
          fontFamily: languageConfig.fontFamily,
          fontSize: languageConfig.isRTL ? 13 : 14,
        ),
        bodyLarge: TextStyle(
          fontFamily: languageConfig.fontFamily,
          fontSize: languageConfig.isRTL ? 15 : 16,
        ),
        bodyMedium: TextStyle(
          fontFamily: languageConfig.fontFamily,
          fontSize: languageConfig.isRTL ? 13 : 14,
        ),
        bodySmall: TextStyle(
          fontFamily: languageConfig.fontFamily,
          fontSize: languageConfig.isRTL ? 11 : 12,
        ),
        labelLarge: TextStyle(
          fontFamily: languageConfig.fontFamily,
          fontSize: languageConfig.isRTL ? 13 : 14,
        ),
        labelMedium: TextStyle(
          fontFamily: languageConfig.fontFamily,
          fontSize: languageConfig.isRTL ? 11 : 12,
        ),
        labelSmall: TextStyle(
          fontFamily: languageConfig.fontFamily,
          fontSize: languageConfig.isRTL ? 10 : 11,
        ),
      ),
    );
  }
  
  ThemeData _buildDarkTheme(LanguageConfig languageConfig) {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.dark,
      ),
      fontFamily: languageConfig.fontFamily,
      visualDensity: VisualDensity.adaptivePlatformDensity,
    );
  }
}
```

### 6.2 应用主页

```dart
// lib/app.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'core/localization/bloc/localization_bloc.dart';
import 'core/localization/widgets/language_selector.dart';
import 'core/localization/widgets/localized_text.dart';
import 'core/localization/formatters/date_time_formatter.dart';
import 'core/localization/formatters/number_formatter.dart';
import 'l10n/generated/app_localizations.dart';

class AppHomePage extends StatelessWidget {
  const AppHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: LocalizedText('appTitle'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSettingsDialog(context),
          ),
        ],
      ),
      body: const SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _WelcomeSection(),
            SizedBox(height: 24),
            _NumberFormattingSection(),
            SizedBox(height: 24),
            _DateTimeFormattingSection(),
            SizedBox(height: 24),
            _ValidationSection(),
            SizedBox(height: 24),
            _BusinessSection(),
          ],
        ),
      ),
    );
  }
  
  void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: LocalizedText('settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.language),
              title: LocalizedText('language'),
              subtitle: const LanguageSelector(),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: LocalizedText('cancel'),
          ),
        ],
      ),
    );
  }
}

class _WelcomeSection extends StatelessWidget {
  const _WelcomeSection();

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            LocalizedText(
              'welcome',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            LocalizedText(
              'welcomeMessage',
              params: {'name': 'John Doe'},
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Icon(Icons.info_outline),
                const SizedBox(width: 8),
                Expanded(
                  child: LocalizedText(
                    'itemCount',
                    params: {'count': 42},
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _NumberFormattingSection extends StatelessWidget {
  const _NumberFormattingSection();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LocalizationBloc, LocalizationState>(
      builder: (context, state) {
        if (state is! LocalizationLoaded) {
          return const SizedBox.shrink();
        }
        
        final formatter = NumberFormatter(state.currentLanguage);
        
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Number Formatting',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 16),
                _buildFormatExample(
                  context,
                  'Integer',
                  formatter.formatInteger(1234567),
                ),
                _buildFormatExample(
                  context,
                  'Decimal',
                  formatter.formatDecimal(1234.567),
                ),
                _buildFormatExample(
                  context,
                  'Currency (USD)',
                  formatter.formatCurrency(1234.56, 'USD'),
                ),
                _buildFormatExample(
                  context,
                  'Percentage',
                  formatter.formatPercentage(0.1234),
                ),
                _buildFormatExample(
                  context,
                  'Compact',
                  formatter.formatCompact(1234567),
                ),
                _buildFormatExample(
                  context,
                  'File Size',
                  formatter.formatFileSize(1234567890),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildFormatExample(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}

class _DateTimeFormattingSection extends StatelessWidget {
  const _DateTimeFormattingSection();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LocalizationBloc, LocalizationState>(
      builder: (context, state) {
        if (state is! LocalizationLoaded) {
          return const SizedBox.shrink();
        }
        
        final formatter = DateTimeFormatter(state.currentLanguage);
        final now = DateTime.now();
        final yesterday = now.subtract(const Duration(days: 1));
        
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Date & Time Formatting',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 16),
                _buildFormatExample(
                  context,
                  'Date',
                  formatter.formatDate(now),
                ),
                _buildFormatExample(
                  context,
                  'Time',
                  formatter.formatTime(now),
                ),
                _buildFormatExample(
                  context,
                  'DateTime',
                  formatter.formatDateTime(now),
                ),
                _buildFormatExample(
                  context,
                  'Relative',
                  formatter.formatRelativeTime(yesterday),
                ),
                _buildFormatExample(
                  context,
                  'Duration',
                  formatter.formatDuration(const Duration(hours: 2, minutes: 30)),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildFormatExample(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}

class _ValidationSection extends StatelessWidget {
  const _ValidationSection();

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Validation Messages',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            _buildValidationExample(
              context,
              Icons.error_outline,
              Colors.red,
              'validationRequired',
            ),
            _buildValidationExample(
              context,
              Icons.email_outlined,
              Colors.orange,
              'validationEmail',
            ),
            _buildValidationExample(
              context,
              Icons.lock_outline,
              Colors.blue,
              'validationPassword',
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildValidationExample(
    BuildContext context,
    IconData icon,
    Color color,
    String messageKey,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: LocalizedText(
              messageKey,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: color,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _BusinessSection extends StatelessWidget {
  const _BusinessSection();

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Business Features',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            _buildBusinessItem(context, Icons.dashboard, 'dashboard'),
            _buildBusinessItem(context, Icons.analytics, 'analytics'),
            _buildBusinessItem(context, Icons.assessment, 'reports'),
            _buildBusinessItem(context, Icons.people, 'users'),
            _buildBusinessItem(context, Icons.inventory, 'products'),
            _buildBusinessItem(context, Icons.shopping_cart, 'orders'),
          ],
        ),
      ),
    );
  }
  
  Widget _buildBusinessItem(BuildContext context, IconData icon, String labelKey) {
    return ListTile(
      leading: Icon(icon),
      title: LocalizedText(labelKey),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: LocalizedText(
              'welcomeMessage',
              params: {'name': context.tr(labelKey)},
            ),
          ),
        );
      },
    );
  }
}
```

## 7. 高级功能

### 7.1 动态本地化资源加载

```dart
// lib/core/localization/services/remote_localization_service.dart
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'localization_service.dart';
import '../models/language_config.dart';

class RemoteLocalizationService extends LocalizationService {
  final Dio _dio;
  final SharedPreferences _prefs;
  
  Map<String, Map<String, String>> _remoteTranslations = {};
  
  RemoteLocalizationService(this._dio, this._prefs);
  
  @override
  Future<void> initialize() async {
    await super.initialize();
    await _loadRemoteTranslations();
  }
  
  Future<void> _loadRemoteTranslations() async {
    try {
      // 从缓存加载
      await _loadCachedTranslations();
      
      // 检查更新
      await _checkForUpdates();
    } catch (e) {
      print('Failed to load remote translations: $e');
    }
  }
  
  Future<void> _loadCachedTranslations() async {
    for (final language in SupportedLanguages.all) {
      final cacheKey = 'translations_${language.code}';
      final cachedData = _prefs.getString(cacheKey);
      
      if (cachedData != null) {
        final translations = Map<String, String>.from(
          json.decode(cachedData) as Map,
        );
        _remoteTranslations[language.code] = translations;
      }
    }
  }
  
  Future<void> _checkForUpdates() async {
    try {
      final response = await _dio.get('/api/translations/version');
      final serverVersion = response.data['version'] as String;
      final localVersion = _prefs.getString('translations_version') ?? '0';
      
      if (serverVersion != localVersion) {
        await _downloadTranslations();
        await _prefs.setString('translations_version', serverVersion);
      }
    } catch (e) {
      print('Failed to check translation updates: $e');
    }
  }
  
  Future<void> _downloadTranslations() async {
    for (final language in SupportedLanguages.all) {
      try {
        final response = await _dio.get(
          '/api/translations/${language.code}',
        );
        
        final translations = Map<String, String>.from(
          response.data as Map,
        );
        
        _remoteTranslations[language.code] = translations;
        
        // 缓存到本地
        final cacheKey = 'translations_${language.code}';
        await _prefs.setString(
          cacheKey,
          json.encode(translations),
        );
      } catch (e) {
        print('Failed to download translations for ${language.code}: $e');
      }
    }
  }
  
  @override
  String translate(
    String key, {
    Map<String, dynamic>? params,
    String? languageCode,
  }) {
    final targetLanguage = languageCode ?? _currentLanguage.code;
    
    // 优先使用远程翻译
    final remoteTranslation = _remoteTranslations[targetLanguage]?[key];
    if (remoteTranslation != null) {
      return _processTranslation(remoteTranslation, params);
    }
    
    // 回退到本地翻译
    return super.translate(key, params: params, languageCode: languageCode);
  }
  
  String _processTranslation(
    String translation,
    Map<String, dynamic>? params,
  ) {
    if (params == null || params.isEmpty) {
      return translation;
    }
    
    String result = translation;
    params.forEach((key, value) {
      result = result.replaceAll('{$key}', value.toString());
    });
    
    return result;
  }
  
  Future<void> refreshTranslations() async {
    await _downloadTranslations();
  }
}
```

### 7.2 本地化测试工具

```dart
// lib/core/localization/testing/localization_test_helper.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/localization_bloc.dart';
import '../services/localization_service.dart';
import '../models/language_config.dart';

class LocalizationTestHelper {
  static Widget wrapWithLocalization(
    Widget child, {
    LanguageConfig? language,
    LocalizationService? service,
  }) {
    final localizationService = service ?? MockLocalizationService();
    final targetLanguage = language ?? SupportedLanguages.english;
    
    return BlocProvider(
      create: (context) => LocalizationBloc(localizationService)
        ..add(ChangeLanguage(targetLanguage)),
      child: MaterialApp(
        home: child,
        locale: targetLanguage.locale,
      ),
    );
  }
  
  static Future<void> testAllLanguages(
    WidgetTester tester,
    Widget Function(LanguageConfig language) widgetBuilder,
  ) async {
    for (final language in SupportedLanguages.all) {
      await tester.pumpWidget(
        wrapWithLocalization(
          widgetBuilder(language),
          language: language,
        ),
      );
      
      await tester.pumpAndSettle();
      
      // 验证 RTL 布局
      if (language.isRTL) {
        final directionality = tester.widget<Directionality>(
          find.byType(Directionality).first,
        );
        expect(directionality.textDirection, TextDirection.rtl);
      }
      
      // 验证字体
      final textWidgets = tester.widgetList<Text>(find.byType(Text));
      for (final textWidget in textWidgets) {
        if (textWidget.style?.fontFamily != null) {
          expect(
            textWidget.style!.fontFamily,
            equals(language.fontFamily),
          );
        }
      }
    }
  }
  
  static void verifyTranslationKeys(List<String> requiredKeys) {
    for (final language in SupportedLanguages.all) {
      for (final key in requiredKeys) {
        final service = MockLocalizationService();
        final translation = service.translate(key, languageCode: language.code);
        
        expect(
          translation,
          isNot(equals(key)),
          reason: 'Missing translation for key "$key" in language "${language.code}"',
        );
      }
    }
  }
}

class MockLocalizationService extends LocalizationService {
  final Map<String, Map<String, String>> _mockTranslations = {
    'en': {
      'welcome': 'Welcome',
      'login': 'Login',
      'email': 'Email',
      'password': 'Password',
    },
    'zh': {
      'welcome': '欢迎',
      'login': '登录',
      'email': '邮箱',
      'password': '密码',
    },
    'ar': {
      'welcome': 'مرحبا',
      'login': 'تسجيل الدخول',
      'email': 'البريد الإلكتروني',
      'password': 'كلمة المرور',
    },
  };
  
  @override
  Future<void> initialize() async {
    _currentLanguage = SupportedLanguages.english;
  }
  
  @override
  String translate(
    String key, {
    Map<String, dynamic>? params,
    String? languageCode,
  }) {
    final targetLanguage = languageCode ?? _currentLanguage.code;
    return _mockTranslations[targetLanguage]?[key] ?? key;
  }
}
```

### 7.3 本地化性能优化

```dart
// lib/core/localization/services/cached_localization_service.dart
import 'dart:collection';
import 'localization_service.dart';
import '../models/language_config.dart';

class CachedLocalizationService extends LocalizationService {
  final LocalizationService _baseService;
  final int _maxCacheSize;
  
  // LRU 缓存
  final LinkedHashMap<String, String> _translationCache = LinkedHashMap();
  
  CachedLocalizationService(
    this._baseService, {
    int maxCacheSize = 1000,
  }) : _maxCacheSize = maxCacheSize;
  
  @override
  Future<void> initialize() async {
    await _baseService.initialize();
    _currentLanguage = _baseService._currentLanguage;
  }
  
  @override
  String translate(
    String key, {
    Map<String, dynamic>? params,
    String? languageCode,
  }) {
    final cacheKey = _buildCacheKey(key, params, languageCode);
    
    // 检查缓存
    if (_translationCache.containsKey(cacheKey)) {
      // 移动到最后（LRU）
      final value = _translationCache.remove(cacheKey)!;
      _translationCache[cacheKey] = value;
      return value;
    }
    
    // 获取翻译
    final translation = _baseService.translate(
      key,
      params: params,
      languageCode: languageCode,
    );
    
    // 添加到缓存
    _addToCache(cacheKey, translation);
    
    return translation;
  }
  
  @override
  Future<void> changeLanguage(LanguageConfig language) async {
    await _baseService.changeLanguage(language);
    _currentLanguage = language;
    
    // 清空缓存，因为语言改变了
    _translationCache.clear();
  }
  
  String _buildCacheKey(
    String key,
    Map<String, dynamic>? params,
    String? languageCode,
  ) {
    final language = languageCode ?? _currentLanguage.code;
    final paramsStr = params?.toString() ?? '';
    return '$language:$key:$paramsStr';
  }
  
  void _addToCache(String key, String value) {
    // 如果缓存已满，删除最旧的条目
    if (_translationCache.length >= _maxCacheSize) {
      _translationCache.remove(_translationCache.keys.first);
    }
    
    _translationCache[key] = value;
  }
  
  void clearCache() {
    _translationCache.clear();
  }
  
  int get cacheSize => _translationCache.length;
  
  double get cacheHitRate {
    // 这里需要额外的统计逻辑
    return 0.0;
  }
}
```

## 8. 最佳实践和建议

### 8.1 翻译管理最佳实践

1. **翻译键命名规范**
   ```dart
   // 好的命名
   "userProfile.name"
   "validation.email.required"
   "button.save"
   "message.success.userCreated"
   
   // 避免的命名
   "text1"
   "msg"
   "btn"
   ```

2. **参数化翻译**
   ```dart
   // 使用参数而不是字符串拼接
   "welcomeMessage": "Welcome, {name}!"
   
   // 避免
   "welcome" + userName + "!"
   ```

3. **复数形式处理**
   ```dart
   "itemCount": "{count, plural, =0{No items} =1{One item} other{{count} items}}"
   ```

### 8.2 性能优化建议

1. **延迟加载翻译资源**
2. **使用翻译缓存**
3. **预编译常用翻译**
4. **异步加载远程翻译**

### 8.3 测试策略

1. **单元测试翻译服务**
2. **Widget 测试多语言界面**
3. **集成测试语言切换**
4. **Golden 测试 RTL 布局**

### 8.4 维护建议

1. **定期审查翻译质量**
2. **建立翻译工作流**
3. **使用翻译管理工具**
4. **监控翻译覆盖率**

## 9. 总结

本文档提供了 Flutter 企业级应用国际化和本地化的完整实现方案，包括：

- **多语言支持**：完整的 ARB 资源文件和本地化配置
- **动态语言切换**：基于 BLoC 的状态管理和持久化存储
- **格式化工具**：日期时间、数字货币、电话号码等本地化格式化
- **RTL 支持**：完整的从右到左语言支持和 UI 适配
- **本地化组件**：可复用的本地化 Widget 和工具
- **高级功能**：远程翻译加载、性能优化、测试工具
- **最佳实践**：翻译管理、性能优化、测试策略等建议

该方案遵循 Flutter 官方国际化最佳实践，提供了生产级的多语言支持能力，确保应用能够为全球用户提供优质的本地化体验。