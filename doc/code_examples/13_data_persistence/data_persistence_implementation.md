# Flutter 企业级应用 - 数据持久化和缓存实现

本文档提供了 Flutter 企业级应用中数据持久化和缓存系统的完整实现示例，包括本地数据库、文件存储、内存缓存、分布式缓存等功能。

## 目录

1. [数据库抽象层](#1-数据库抽象层)
2. [SQLite 数据库实现](#2-sqlite-数据库实现)
3. [文件存储系统](#3-文件存储系统)
4. [缓存系统](#4-缓存系统)
5. [数据同步服务](#5-数据同步服务)
6. [使用示例](#6-使用示例)

## 1. 数据库抽象层

### 数据库接口定义

```dart
// lib/core/data/database/database_service.dart
import 'dart:async';

/// 数据库操作结果
class DatabaseResult<T> {
  final bool success;
  final T? data;
  final String? error;
  final int? affectedRows;

  const DatabaseResult({
    required this.success,
    this.data,
    this.error,
    this.affectedRows,
  });

  factory DatabaseResult.success(T data, {int? affectedRows}) {
    return DatabaseResult(
      success: true,
      data: data,
      affectedRows: affectedRows,
    );
  }

  factory DatabaseResult.failure(String error) {
    return DatabaseResult(
      success: false,
      error: error,
    );
  }
}

/// 数据库查询条件
class QueryCondition {
  final String column;
  final dynamic value;
  final String operator;

  const QueryCondition({
    required this.column,
    required this.value,
    this.operator = '=',
  });

  @override
  String toString() {
    if (value is String) {
      return '$column $operator "$value"';
    }
    return '$column $operator $value';
  }
}

/// 数据库排序
class OrderBy {
  final String column;
  final bool ascending;

  const OrderBy({
    required this.column,
    this.ascending = true,
  });

  @override
  String toString() {
    return '$column ${ascending ? 'ASC' : 'DESC'}';
  }
}

/// 数据库查询选项
class QueryOptions {
  final List<QueryCondition>? where;
  final List<OrderBy>? orderBy;
  final int? limit;
  final int? offset;
  final List<String>? columns;
  final String? groupBy;
  final String? having;

  const QueryOptions({
    this.where,
    this.orderBy,
    this.limit,
    this.offset,
    this.columns,
    this.groupBy,
    this.having,
  });
}

/// 数据库服务接口
abstract class DatabaseService {
  /// 初始化数据库
  Future<void> initialize();

  /// 关闭数据库
  Future<void> close();

  /// 创建表
  Future<DatabaseResult<void>> createTable(
    String tableName,
    Map<String, String> columns, {
    List<String>? primaryKeys,
    Map<String, String>? foreignKeys,
    List<String>? indexes,
  });

  /// 删除表
  Future<DatabaseResult<void>> dropTable(String tableName);

  /// 插入数据
  Future<DatabaseResult<int>> insert(
    String tableName,
    Map<String, dynamic> data, {
    bool replace = false,
  });

  /// 批量插入数据
  Future<DatabaseResult<List<int>>> insertBatch(
    String tableName,
    List<Map<String, dynamic>> dataList, {
    bool replace = false,
  });

  /// 更新数据
  Future<DatabaseResult<int>> update(
    String tableName,
    Map<String, dynamic> data,
    QueryOptions options,
  );

  /// 删除数据
  Future<DatabaseResult<int>> delete(
    String tableName,
    QueryOptions options,
  );

  /// 查询数据
  Future<DatabaseResult<List<Map<String, dynamic>>>> query(
    String tableName,
    QueryOptions options,
  );

  /// 查询单条数据
  Future<DatabaseResult<Map<String, dynamic>?>> queryFirst(
    String tableName,
    QueryOptions options,
  );

  /// 统计数据
  Future<DatabaseResult<int>> count(
    String tableName,
    QueryOptions options,
  );

  /// 执行原生 SQL
  Future<DatabaseResult<List<Map<String, dynamic>>>> rawQuery(
    String sql, {
    List<dynamic>? arguments,
  });

  /// 执行原生 SQL（无返回值）
  Future<DatabaseResult<int>> rawExecute(
    String sql, {
    List<dynamic>? arguments,
  });

  /// 开始事务
  Future<DatabaseTransaction> beginTransaction();

  /// 数据库版本
  int get version;

  /// 数据库路径
  String get path;

  /// 是否已初始化
  bool get isInitialized;
}

/// 数据库事务接口
abstract class DatabaseTransaction {
  /// 提交事务
  Future<void> commit();

  /// 回滚事务
  Future<void> rollback();

  /// 在事务中执行操作
  Future<T> execute<T>(Future<T> Function() operation);
}
```

### 实体基类

```dart
// lib/core/data/entities/base_entity.dart
import 'package:json_annotation/json_annotation.dart';

/// 实体基类
abstract class BaseEntity {
  /// 主键 ID
  final int? id;

  /// 创建时间
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;

  /// 更新时间
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;

  /// 是否已删除（软删除）
  @JsonKey(name: 'is_deleted')
  final bool isDeleted;

  /// 版本号（乐观锁）
  final int version;

  const BaseEntity({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.version = 1,
  });

  /// 转换为 Map
  Map<String, dynamic> toMap();

  /// 从 Map 创建实体
  static T fromMap<T extends BaseEntity>(Map<String, dynamic> map) {
    throw UnimplementedError('fromMap must be implemented in subclass');
  }

  /// 复制实体并更新字段
  BaseEntity copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isDeleted,
    int? version,
  });

  /// 获取表名
  String get tableName;

  /// 获取主键列名
  String get primaryKey => 'id';

  /// 获取表结构定义
  Map<String, String> get tableSchema;

  /// 获取索引定义
  List<String> get indexes => [];

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BaseEntity &&
        other.runtimeType == runtimeType &&
        other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return '$runtimeType{id: $id, createdAt: $createdAt, updatedAt: $updatedAt}';
  }
}

/// 数据访问对象基类
abstract class BaseDao<T extends BaseEntity> {
  final DatabaseService _database;
  final String _tableName;

  BaseDao(this._database, this._tableName);

  /// 创建表
  Future<void> createTable(T entity) async {
    await _database.createTable(
      _tableName,
      entity.tableSchema,
      primaryKeys: [entity.primaryKey],
      indexes: entity.indexes,
    );
  }

  /// 插入实体
  Future<DatabaseResult<int>> insert(T entity) async {
    final data = entity.toMap();
    data['created_at'] = DateTime.now().toIso8601String();
    data['updated_at'] = DateTime.now().toIso8601String();
    return await _database.insert(_tableName, data);
  }

  /// 批量插入实体
  Future<DatabaseResult<List<int>>> insertBatch(List<T> entities) async {
    final now = DateTime.now().toIso8601String();
    final dataList = entities.map((entity) {
      final data = entity.toMap();
      data['created_at'] = now;
      data['updated_at'] = now;
      return data;
    }).toList();
    return await _database.insertBatch(_tableName, dataList);
  }

  /// 更新实体
  Future<DatabaseResult<int>> update(T entity) async {
    if (entity.id == null) {
      return DatabaseResult.failure('Entity ID cannot be null for update');
    }
    
    final data = entity.toMap();
    data['updated_at'] = DateTime.now().toIso8601String();
    data['version'] = entity.version + 1;
    
    return await _database.update(
      _tableName,
      data,
      QueryOptions(
        where: [
          QueryCondition(column: 'id', value: entity.id),
          QueryCondition(column: 'version', value: entity.version),
        ],
      ),
    );
  }

  /// 软删除实体
  Future<DatabaseResult<int>> softDelete(int id) async {
    return await _database.update(
      _tableName,
      {
        'is_deleted': true,
        'updated_at': DateTime.now().toIso8601String(),
      },
      QueryOptions(
        where: [QueryCondition(column: 'id', value: id)],
      ),
    );
  }

  /// 硬删除实体
  Future<DatabaseResult<int>> hardDelete(int id) async {
    return await _database.delete(
      _tableName,
      QueryOptions(
        where: [QueryCondition(column: 'id', value: id)],
      ),
    );
  }

  /// 根据 ID 查找实体
  Future<DatabaseResult<T?>> findById(int id) async {
    final result = await _database.queryFirst(
      _tableName,
      QueryOptions(
        where: [
          QueryCondition(column: 'id', value: id),
          QueryCondition(column: 'is_deleted', value: false),
        ],
      ),
    );
    
    if (!result.success || result.data == null) {
      return DatabaseResult.failure(result.error ?? 'Entity not found');
    }
    
    return DatabaseResult.success(fromMap(result.data!));
  }

  /// 查找所有实体
  Future<DatabaseResult<List<T>>> findAll({
    QueryOptions? options,
    bool includeDeleted = false,
  }) async {
    final conditions = options?.where ?? [];
    if (!includeDeleted) {
      conditions.add(QueryCondition(column: 'is_deleted', value: false));
    }
    
    final result = await _database.query(
      _tableName,
      QueryOptions(
        where: conditions,
        orderBy: options?.orderBy,
        limit: options?.limit,
        offset: options?.offset,
        columns: options?.columns,
        groupBy: options?.groupBy,
        having: options?.having,
      ),
    );
    
    if (!result.success) {
      return DatabaseResult.failure(result.error!);
    }
    
    final entities = result.data!.map((map) => fromMap(map)).toList();
    return DatabaseResult.success(entities);
  }

  /// 统计实体数量
  Future<DatabaseResult<int>> count({
    QueryOptions? options,
    bool includeDeleted = false,
  }) async {
    final conditions = options?.where ?? [];
    if (!includeDeleted) {
      conditions.add(QueryCondition(column: 'is_deleted', value: false));
    }
    
    return await _database.count(
      _tableName,
      QueryOptions(where: conditions),
    );
  }

  /// 从 Map 创建实体（需要子类实现）
  T fromMap(Map<String, dynamic> map);
}
```

## 2. SQLite 数据库实现

### SQLite 数据库服务

```dart
// lib/core/data/database/sqlite_database_service.dart
import 'dart:async';
import 'dart:io';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:injectable/injectable.dart';
import '../../../core/data/database/database_service.dart';
import '../../../core/utils/logger_service.dart';

@LazySingleton(as: DatabaseService)
class SqliteDatabaseService implements DatabaseService {
  final LoggerService _logger;
  Database? _database;
  final String _databaseName;
  final int _version;
  final List<String> _migrationScripts;
  bool _isInitialized = false;

  SqliteDatabaseService(
    this._logger, {
    String databaseName = 'app_database.db',
    int version = 1,
    List<String> migrationScripts = const [],
  })  : _databaseName = databaseName,
        _version = version,
        _migrationScripts = migrationScripts;

  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, _databaseName);

      _database = await openDatabase(
        path,
        version: _version,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
        onDowngrade: _onDowngrade,
        onOpen: _onOpen,
      );

      _isInitialized = true;
      _logger.info('Database initialized: $path');
    } catch (e, stackTrace) {
      _logger.error('Failed to initialize database', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
      _isInitialized = false;
      _logger.info('Database closed');
    }
  }

  Future<void> _onCreate(Database db, int version) async {
    _logger.info('Creating database version $version');
    // 创建基础表结构
    await _createBaseTables(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    _logger.info('Upgrading database from $oldVersion to $newVersion');
    
    for (int i = oldVersion; i < newVersion; i++) {
      if (i < _migrationScripts.length) {
        await db.execute(_migrationScripts[i]);
        _logger.info('Applied migration script for version ${i + 1}');
      }
    }
  }

  Future<void> _onDowngrade(Database db, int oldVersion, int newVersion) async {
    _logger.warning('Downgrading database from $oldVersion to $newVersion');
    // 通常不建议降级，可以选择重建数据库
    await _recreateDatabase(db);
  }

  Future<void> _onOpen(Database db) async {
    _logger.info('Database opened');
    // 启用外键约束
    await db.execute('PRAGMA foreign_keys = ON');
  }

  Future<void> _createBaseTables(Database db) async {
    // 创建系统配置表
    await db.execute('''
      CREATE TABLE IF NOT EXISTS system_config (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // 创建缓存表
    await db.execute('''
      CREATE TABLE IF NOT EXISTS cache_entries (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT,
        expires_at TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // 创建索引
    await db.execute('CREATE INDEX IF NOT EXISTS idx_cache_key ON cache_entries(key)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_cache_expires ON cache_entries(expires_at)');
  }

  Future<void> _recreateDatabase(Database db) async {
    // 获取所有表名
    final tables = await db.rawQuery(
      "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'",
    );

    // 删除所有表
    for (final table in tables) {
      await db.execute('DROP TABLE IF EXISTS ${table['name']}');
    }

    // 重新创建表
    await _createBaseTables(db);
  }

  Database get _db {
    if (_database == null || !_isInitialized) {
      throw StateError('Database not initialized. Call initialize() first.');
    }
    return _database!;
  }

  @override
  Future<DatabaseResult<void>> createTable(
    String tableName,
    Map<String, String> columns, {
    List<String>? primaryKeys,
    Map<String, String>? foreignKeys,
    List<String>? indexes,
  }) async {
    try {
      final columnDefinitions = columns.entries
          .map((entry) => '${entry.key} ${entry.value}')
          .join(', ');

      String sql = 'CREATE TABLE IF NOT EXISTS $tableName ($columnDefinitions';

      if (primaryKeys != null && primaryKeys.isNotEmpty) {
        sql += ', PRIMARY KEY (${primaryKeys.join(', ')})';
      }

      if (foreignKeys != null && foreignKeys.isNotEmpty) {
        for (final entry in foreignKeys.entries) {
          sql += ', FOREIGN KEY (${entry.key}) REFERENCES ${entry.value}';
        }
      }

      sql += ')';

      await _db.execute(sql);

      // 创建索引
      if (indexes != null) {
        for (final index in indexes) {
          await _db.execute(
            'CREATE INDEX IF NOT EXISTS idx_${tableName}_$index ON $tableName($index)',
          );
        }
      }

      _logger.info('Table created: $tableName');
      return DatabaseResult.success(null);
    } catch (e, stackTrace) {
      _logger.error('Failed to create table: $tableName', e, stackTrace);
      return DatabaseResult.failure(e.toString());
    }
  }

  @override
  Future<DatabaseResult<void>> dropTable(String tableName) async {
    try {
      await _db.execute('DROP TABLE IF EXISTS $tableName');
      _logger.info('Table dropped: $tableName');
      return DatabaseResult.success(null);
    } catch (e, stackTrace) {
      _logger.error('Failed to drop table: $tableName', e, stackTrace);
      return DatabaseResult.failure(e.toString());
    }
  }

  @override
  Future<DatabaseResult<int>> insert(
    String tableName,
    Map<String, dynamic> data, {
    bool replace = false,
  }) async {
    try {
      final id = await _db.insert(
        tableName,
        data,
        conflictAlgorithm: replace ? ConflictAlgorithm.replace : ConflictAlgorithm.abort,
      );
      _logger.debug('Inserted record in $tableName with ID: $id');
      return DatabaseResult.success(id, affectedRows: 1);
    } catch (e, stackTrace) {
      _logger.error('Failed to insert into $tableName', e, stackTrace);
      return DatabaseResult.failure(e.toString());
    }
  }

  @override
  Future<DatabaseResult<List<int>>> insertBatch(
    String tableName,
    List<Map<String, dynamic>> dataList, {
    bool replace = false,
  }) async {
    try {
      final batch = _db.batch();
      for (final data in dataList) {
        batch.insert(
          tableName,
          data,
          conflictAlgorithm: replace ? ConflictAlgorithm.replace : ConflictAlgorithm.abort,
        );
      }
      final results = await batch.commit();
      final ids = results.cast<int>();
      _logger.debug('Batch inserted ${ids.length} records in $tableName');
      return DatabaseResult.success(ids, affectedRows: ids.length);
    } catch (e, stackTrace) {
      _logger.error('Failed to batch insert into $tableName', e, stackTrace);
      return DatabaseResult.failure(e.toString());
    }
  }

  @override
  Future<DatabaseResult<int>> update(
    String tableName,
    Map<String, dynamic> data,
    QueryOptions options,
  ) async {
    try {
      final whereClause = _buildWhereClause(options.where);
      final whereArgs = _buildWhereArgs(options.where);

      final affectedRows = await _db.update(
        tableName,
        data,
        where: whereClause,
        whereArgs: whereArgs,
      );

      _logger.debug('Updated $affectedRows records in $tableName');
      return DatabaseResult.success(affectedRows, affectedRows: affectedRows);
    } catch (e, stackTrace) {
      _logger.error('Failed to update $tableName', e, stackTrace);
      return DatabaseResult.failure(e.toString());
    }
  }

  @override
  Future<DatabaseResult<int>> delete(
    String tableName,
    QueryOptions options,
  ) async {
    try {
      final whereClause = _buildWhereClause(options.where);
      final whereArgs = _buildWhereArgs(options.where);

      final affectedRows = await _db.delete(
        tableName,
        where: whereClause,
        whereArgs: whereArgs,
      );

      _logger.debug('Deleted $affectedRows records from $tableName');
      return DatabaseResult.success(affectedRows, affectedRows: affectedRows);
    } catch (e, stackTrace) {
      _logger.error('Failed to delete from $tableName', e, stackTrace);
      return DatabaseResult.failure(e.toString());
    }
  }

  @override
  Future<DatabaseResult<List<Map<String, dynamic>>>> query(
    String tableName,
    QueryOptions options,
  ) async {
    try {
      final whereClause = _buildWhereClause(options.where);
      final whereArgs = _buildWhereArgs(options.where);
      final orderBy = _buildOrderBy(options.orderBy);

      final results = await _db.query(
        tableName,
        columns: options.columns,
        where: whereClause,
        whereArgs: whereArgs,
        groupBy: options.groupBy,
        having: options.having,
        orderBy: orderBy,
        limit: options.limit,
        offset: options.offset,
      );

      _logger.debug('Queried ${results.length} records from $tableName');
      return DatabaseResult.success(results);
    } catch (e, stackTrace) {
      _logger.error('Failed to query $tableName', e, stackTrace);
      return DatabaseResult.failure(e.toString());
    }
  }

  @override
  Future<DatabaseResult<Map<String, dynamic>?>> queryFirst(
    String tableName,
    QueryOptions options,
  ) async {
    final result = await query(
      tableName,
      QueryOptions(
        where: options.where,
        orderBy: options.orderBy,
        columns: options.columns,
        groupBy: options.groupBy,
        having: options.having,
        limit: 1,
      ),
    );

    if (!result.success) {
      return DatabaseResult.failure(result.error!);
    }

    final data = result.data!.isEmpty ? null : result.data!.first;
    return DatabaseResult.success(data);
  }

  @override
  Future<DatabaseResult<int>> count(
    String tableName,
    QueryOptions options,
  ) async {
    try {
      final whereClause = _buildWhereClause(options.where);
      final whereArgs = _buildWhereArgs(options.where);

      final results = await _db.query(
        tableName,
        columns: ['COUNT(*) as count'],
        where: whereClause,
        whereArgs: whereArgs,
        groupBy: options.groupBy,
        having: options.having,
      );

      final count = results.first['count'] as int;
      _logger.debug('Counted $count records in $tableName');
      return DatabaseResult.success(count);
    } catch (e, stackTrace) {
      _logger.error('Failed to count records in $tableName', e, stackTrace);
      return DatabaseResult.failure(e.toString());
    }
  }

  @override
  Future<DatabaseResult<List<Map<String, dynamic>>>> rawQuery(
    String sql, {
    List<dynamic>? arguments,
  }) async {
    try {
      final results = await _db.rawQuery(sql, arguments);
      _logger.debug('Raw query executed: $sql');
      return DatabaseResult.success(results);
    } catch (e, stackTrace) {
      _logger.error('Failed to execute raw query: $sql', e, stackTrace);
      return DatabaseResult.failure(e.toString());
    }
  }

  @override
  Future<DatabaseResult<int>> rawExecute(
    String sql, {
    List<dynamic>? arguments,
  }) async {
    try {
      final affectedRows = await _db.rawUpdate(sql, arguments);
      _logger.debug('Raw execute completed: $sql');
      return DatabaseResult.success(affectedRows, affectedRows: affectedRows);
    } catch (e, stackTrace) {
      _logger.error('Failed to execute raw SQL: $sql', e, stackTrace);
      return DatabaseResult.failure(e.toString());
    }
  }

  @override
  Future<DatabaseTransaction> beginTransaction() async {
    return SqliteTransaction(_db, _logger);
  }

  String? _buildWhereClause(List<QueryCondition>? conditions) {
    if (conditions == null || conditions.isEmpty) return null;
    return conditions.map((c) => '${c.column} ${c.operator} ?').join(' AND ');
  }

  List<dynamic>? _buildWhereArgs(List<QueryCondition>? conditions) {
    if (conditions == null || conditions.isEmpty) return null;
    return conditions.map((c) => c.value).toList();
  }

  String? _buildOrderBy(List<OrderBy>? orderBy) {
    if (orderBy == null || orderBy.isEmpty) return null;
    return orderBy.map((o) => o.toString()).join(', ');
  }

  @override
  int get version => _version;

  @override
  String get path => _database?.path ?? '';

  @override
  bool get isInitialized => _isInitialized;
}

/// SQLite 事务实现
class SqliteTransaction implements DatabaseTransaction {
  final Database _database;
  final LoggerService _logger;
  Transaction? _transaction;
  bool _isActive = false;

  SqliteTransaction(this._database, this._logger);

  @override
  Future<void> commit() async {
    if (_transaction != null && _isActive) {
      // SQLite 事务会自动提交
      _isActive = false;
      _logger.debug('Transaction committed');
    }
  }

  @override
  Future<void> rollback() async {
    if (_transaction != null && _isActive) {
      // SQLite 事务回滚通过抛出异常实现
      _isActive = false;
      _logger.debug('Transaction rolled back');
      throw Exception('Transaction rolled back');
    }
  }

  @override
  Future<T> execute<T>(Future<T> Function() operation) async {
    return await _database.transaction<T>((txn) async {
      _transaction = txn;
      _isActive = true;
      try {
        final result = await operation();
        _isActive = false;
        return result;
      } catch (e) {
        _isActive = false;
        rethrow;
      }
    });
  }
}
```

### 数据库迁移管理

```dart
// lib/core/data/database/database_migration.dart
import 'package:injectable/injectable.dart';
import '../../../core/utils/logger_service.dart';
import 'database_service.dart';

/// 数据库迁移脚本
class MigrationScript {
  final int version;
  final String description;
  final String sql;
  final List<String>? rollbackSql;

  const MigrationScript({
    required this.version,
    required this.description,
    required this.sql,
    this.rollbackSql,
  });
}

/// 数据库迁移管理器
@LazySingleton()
class DatabaseMigrationManager {
  final DatabaseService _database;
  final LoggerService _logger;
  
  static const List<MigrationScript> _migrations = [
    MigrationScript(
      version: 2,
      description: 'Add user table',
      sql: '''
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT UNIQUE NOT NULL,
          email TEXT UNIQUE NOT NULL,
          password_hash TEXT NOT NULL,
          first_name TEXT,
          last_name TEXT,
          avatar_url TEXT,
          is_active BOOLEAN DEFAULT 1,
          last_login_at TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          is_deleted BOOLEAN DEFAULT 0,
          version INTEGER DEFAULT 1
        );
        CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
        CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
        CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);
      ''',
      rollbackSql: ['DROP TABLE IF EXISTS users'],
    ),
    MigrationScript(
      version: 3,
      description: 'Add posts table',
      sql: '''
        CREATE TABLE IF NOT EXISTS posts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          title TEXT NOT NULL,
          content TEXT,
          status TEXT DEFAULT 'draft',
          published_at TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          is_deleted BOOLEAN DEFAULT 0,
          version INTEGER DEFAULT 1,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        );
        CREATE INDEX IF NOT EXISTS idx_posts_user_id ON posts(user_id);
        CREATE INDEX IF NOT EXISTS idx_posts_status ON posts(status);
        CREATE INDEX IF NOT EXISTS idx_posts_published ON posts(published_at);
      ''',
      rollbackSql: ['DROP TABLE IF EXISTS posts'],
    ),
    MigrationScript(
      version: 4,
      description: 'Add comments table',
      sql: '''
        CREATE TABLE IF NOT EXISTS comments (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          post_id INTEGER NOT NULL,
          user_id INTEGER NOT NULL,
          parent_id INTEGER,
          content TEXT NOT NULL,
          is_approved BOOLEAN DEFAULT 0,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          is_deleted BOOLEAN DEFAULT 0,
          version INTEGER DEFAULT 1,
          FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE
        );
        CREATE INDEX IF NOT EXISTS idx_comments_post_id ON comments(post_id);
        CREATE INDEX IF NOT EXISTS idx_comments_user_id ON comments(user_id);
        CREATE INDEX IF NOT EXISTS idx_comments_parent_id ON comments(parent_id);
      ''',
      rollbackSql: ['DROP TABLE IF EXISTS comments'],
    ),
  ];

  DatabaseMigrationManager(this._database, this._logger);

  /// 执行数据库迁移
  Future<void> migrate({int? targetVersion}) async {
    final currentVersion = _database.version;
    final maxVersion = targetVersion ?? _getMaxVersion();
    
    _logger.info('Starting migration from version $currentVersion to $maxVersion');
    
    if (currentVersion >= maxVersion) {
      _logger.info('Database is already at target version');
      return;
    }
    
    final transaction = await _database.beginTransaction();
    
    try {
      await transaction.execute(() async {
        for (int version = currentVersion + 1; version <= maxVersion; version++) {
          final migration = _getMigrationForVersion(version);
          if (migration != null) {
            await _executeMigration(migration);
          }
        }
      });
      
      await transaction.commit();
      _logger.info('Migration completed successfully');
    } catch (e, stackTrace) {
      await transaction.rollback();
      _logger.error('Migration failed, rolling back', e, stackTrace);
      rethrow;
    }
  }
  
  /// 回滚数据库到指定版本
  Future<void> rollback(int targetVersion) async {
    final currentVersion = _database.version;
    
    if (currentVersion <= targetVersion) {
      _logger.info('Database is already at or below target version');
      return;
    }
    
    _logger.info('Rolling back from version $currentVersion to $targetVersion');
    
    final transaction = await _database.beginTransaction();
    
    try {
      await transaction.execute(() async {
        for (int version = currentVersion; version > targetVersion; version--) {
          final migration = _getMigrationForVersion(version);
          if (migration?.rollbackSql != null) {
            await _executeRollback(migration!);
          }
        }
      });
      
      await transaction.commit();
      _logger.info('Rollback completed successfully');
    } catch (e, stackTrace) {
      await transaction.rollback();
      _logger.error('Rollback failed', e, stackTrace);
      rethrow;
    }
  }
  
  /// 获取迁移历史
  Future<List<Map<String, dynamic>>> getMigrationHistory() async {
    final result = await _database.rawQuery(
      'SELECT * FROM migration_history ORDER BY version DESC',
    );
    
    return result.success ? result.data! : [];
  }
  
  /// 创建迁移历史表
  Future<void> _createMigrationHistoryTable() async {
    await _database.rawExecute('''
      CREATE TABLE IF NOT EXISTS migration_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        version INTEGER UNIQUE NOT NULL,
        description TEXT NOT NULL,
        executed_at TEXT NOT NULL,
        execution_time_ms INTEGER NOT NULL
      )
    ''');
  }
  
  /// 执行迁移脚本
  Future<void> _executeMigration(MigrationScript migration) async {
    final startTime = DateTime.now();
    
    _logger.info('Executing migration ${migration.version}: ${migration.description}');
    
    // 分割并执行多个 SQL 语句
    final statements = migration.sql
        .split(';')
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();
    
    for (final statement in statements) {
      await _database.rawExecute(statement);
    }
    
    final endTime = DateTime.now();
    final executionTime = endTime.difference(startTime).inMilliseconds;
    
    // 记录迁移历史
    await _database.rawExecute(
      'INSERT INTO migration_history (version, description, executed_at, execution_time_ms) VALUES (?, ?, ?, ?)',
      arguments: [
        migration.version,
        migration.description,
        endTime.toIso8601String(),
        executionTime,
      ],
    );
    
    _logger.info('Migration ${migration.version} completed in ${executionTime}ms');
  }
  
  /// 执行回滚脚本
  Future<void> _executeRollback(MigrationScript migration) async {
    _logger.info('Rolling back migration ${migration.version}: ${migration.description}');
    
    for (final sql in migration.rollbackSql!) {
      await _database.rawExecute(sql);
    }
    
    // 删除迁移历史记录
    await _database.rawExecute(
      'DELETE FROM migration_history WHERE version = ?',
      arguments: [migration.version],
    );
    
    _logger.info('Migration ${migration.version} rolled back');
  }
  
  MigrationScript? _getMigrationForVersion(int version) {
    try {
      return _migrations.firstWhere((m) => m.version == version);
    } catch (e) {
      return null;
    }
  }
  
  int _getMaxVersion() {
    return _migrations.isEmpty ? 1 : _migrations.map((m) => m.version).reduce((a, b) => a > b ? a : b);
  }
}
```

## 3. 文件存储系统

### 文件存储服务

```dart
// lib/core/data/storage/file_storage_service.dart
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:injectable/injectable.dart';
import 'package:crypto/crypto.dart';
import '../../../core/utils/logger_service.dart';

/// 文件存储结果
class FileStorageResult<T> {
  final bool success;
  final T? data;
  final String? error;
  final String? filePath;
  final int? fileSize;

  const FileStorageResult({
    required this.success,
    this.data,
    this.error,
    this.filePath,
    this.fileSize,
  });

  factory FileStorageResult.success(T data, {String? filePath, int? fileSize}) {
    return FileStorageResult(
      success: true,
      data: data,
      filePath: filePath,
      fileSize: fileSize,
    );
  }

  factory FileStorageResult.failure(String error) {
    return FileStorageResult(
      success: false,
      error: error,
    );
  }
}

/// 文件信息
class FileInfo {
  final String name;
  final String path;
  final int size;
  final DateTime createdAt;
  final DateTime modifiedAt;
  final String extension;
  final String mimeType;
  final String? checksum;

  const FileInfo({
    required this.name,
    required this.path,
    required this.size,
    required this.createdAt,
    required this.modifiedAt,
    required this.extension,
    required this.mimeType,
    this.checksum,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'path': path,
      'size': size,
      'createdAt': createdAt.toIso8601String(),
      'modifiedAt': modifiedAt.toIso8601String(),
      'extension': extension,
      'mimeType': mimeType,
      'checksum': checksum,
    };
  }

  factory FileInfo.fromMap(Map<String, dynamic> map) {
    return FileInfo(
      name: map['name'],
      path: map['path'],
      size: map['size'],
      createdAt: DateTime.parse(map['createdAt']),
      modifiedAt: DateTime.parse(map['modifiedAt']),
      extension: map['extension'],
      mimeType: map['mimeType'],
      checksum: map['checksum'],
    );
  }
}

/// 文件存储配置
class FileStorageConfig {
  final String baseDirectory;
  final int maxFileSize;
  final List<String> allowedExtensions;
  final bool enableChecksum;
  final bool enableCompression;
  final bool enableEncryption;

  const FileStorageConfig({
    required this.baseDirectory,
    this.maxFileSize = 100 * 1024 * 1024, // 100MB
    this.allowedExtensions = const [],
    this.enableChecksum = true,
    this.enableCompression = false,
    this.enableEncryption = false,
  });
}

/// 文件存储服务接口
abstract class FileStorageService {
  /// 初始化存储服务
  Future<void> initialize();

  /// 保存文件
  Future<FileStorageResult<FileInfo>> saveFile(
    String fileName,
    Uint8List data, {
    String? directory,
    bool overwrite = false,
  });

  /// 保存文本文件
  Future<FileStorageResult<FileInfo>> saveTextFile(
    String fileName,
    String content, {
    String? directory,
    bool overwrite = false,
    Encoding encoding = utf8,
  });

  /// 保存 JSON 文件
  Future<FileStorageResult<FileInfo>> saveJsonFile(
    String fileName,
    Map<String, dynamic> data, {
    String? directory,
    bool overwrite = false,
  });

  /// 读取文件
  Future<FileStorageResult<Uint8List>> readFile(String filePath);

  /// 读取文本文件
  Future<FileStorageResult<String>> readTextFile(
    String filePath, {
    Encoding encoding = utf8,
  });

  /// 读取 JSON 文件
  Future<FileStorageResult<Map<String, dynamic>>> readJsonFile(String filePath);

  /// 复制文件
  Future<FileStorageResult<FileInfo>> copyFile(
    String sourcePath,
    String destinationPath, {
    bool overwrite = false,
  });

  /// 移动文件
  Future<FileStorageResult<FileInfo>> moveFile(
    String sourcePath,
    String destinationPath, {
    bool overwrite = false,
  });

  /// 删除文件
  Future<FileStorageResult<void>> deleteFile(String filePath);

  /// 检查文件是否存在
  Future<bool> fileExists(String filePath);

  /// 获取文件信息
  Future<FileStorageResult<FileInfo>> getFileInfo(String filePath);

  /// 列出目录中的文件
  Future<FileStorageResult<List<FileInfo>>> listFiles(
    String directoryPath, {
    bool recursive = false,
    String? pattern,
  });

  /// 创建目录
  Future<FileStorageResult<void>> createDirectory(String directoryPath);

  /// 删除目录
  Future<FileStorageResult<void>> deleteDirectory(
    String directoryPath, {
    bool recursive = false,
  });

  /// 获取存储统计信息
  Future<FileStorageResult<Map<String, dynamic>>> getStorageStats();

  /// 清理临时文件
  Future<FileStorageResult<int>> cleanupTempFiles();

  /// 获取基础目录路径
  String get baseDirectory;

  /// 获取临时目录路径
  String get tempDirectory;

  /// 获取缓存目录路径
  String get cacheDirectory;
}

@LazySingleton(as: FileStorageService)
class FileStorageServiceImpl implements FileStorageService {
  final LoggerService _logger;
  final FileStorageConfig _config;
  
  String? _baseDirectory;
  String? _tempDirectory;
  String? _cacheDirectory;
  bool _isInitialized = false;

  FileStorageServiceImpl(
    this._logger, {
    FileStorageConfig? config,
  }) : _config = config ?? const FileStorageConfig(baseDirectory: 'app_storage');

  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      final appDocDir = await getApplicationDocumentsDirectory();
      final tempDir = await getTemporaryDirectory();
      final cacheDir = await getApplicationCacheDirectory();

      _baseDirectory = path.join(appDocDir.path, _config.baseDirectory);
      _tempDirectory = tempDir.path;
      _cacheDirectory = cacheDir.path;

      // 创建基础目录
      await Directory(_baseDirectory!).create(recursive: true);
      await Directory(path.join(_cacheDirectory!, 'files')).create(recursive: true);

      _isInitialized = true;
      _logger.info('File storage initialized: $_baseDirectory');
    } catch (e, stackTrace) {
      _logger.error('Failed to initialize file storage', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<FileStorageResult<FileInfo>> saveFile(
    String fileName,
    Uint8List data, {
    String? directory,
    bool overwrite = false,
  }) async {
    try {
      _ensureInitialized();
      
      // 验证文件大小
      if (data.length > _config.maxFileSize) {
        return FileStorageResult.failure(
          'File size ${data.length} exceeds maximum allowed size ${_config.maxFileSize}',
        );
      }

      // 验证文件扩展名
      final extension = path.extension(fileName).toLowerCase();
      if (_config.allowedExtensions.isNotEmpty && 
          !_config.allowedExtensions.contains(extension)) {
        return FileStorageResult.failure(
          'File extension $extension is not allowed',
        );
      }

      // 构建文件路径
      final dirPath = directory != null 
          ? path.join(_baseDirectory!, directory)
          : _baseDirectory!;
      
      await Directory(dirPath).create(recursive: true);
      
      final filePath = path.join(dirPath, fileName);
      final file = File(filePath);

      // 检查文件是否已存在
      if (await file.exists() && !overwrite) {
        return FileStorageResult.failure('File already exists: $filePath');
      }

      // 写入文件
      await file.writeAsBytes(data);

      // 生成文件信息
      final fileInfo = await _createFileInfo(file);
      
      _logger.info('File saved: $filePath (${data.length} bytes)');
      return FileStorageResult.success(
        fileInfo,
        filePath: filePath,
        fileSize: data.length,
      );
    } catch (e, stackTrace) {
      _logger.error('Failed to save file: $fileName', e, stackTrace);
      return FileStorageResult.failure(e.toString());
    }
  }

  @override
  Future<FileStorageResult<FileInfo>> saveTextFile(
    String fileName,
    String content, {
    String? directory,
    bool overwrite = false,
    Encoding encoding = utf8,
  }) async {
    final data = Uint8List.fromList(encoding.encode(content));
    return await saveFile(fileName, data, directory: directory, overwrite: overwrite);
  }

  @override
  Future<FileStorageResult<FileInfo>> saveJsonFile(
    String fileName,
    Map<String, dynamic> data, {
    String? directory,
    bool overwrite = false,
  }) async {
    final jsonString = jsonEncode(data);
    return await saveTextFile(
      fileName,
      jsonString,
      directory: directory,
      overwrite: overwrite,
    );
  }

  @override
  Future<FileStorageResult<Uint8List>> readFile(String filePath) async {
    try {
      _ensureInitialized();
      
      final file = File(_getAbsolutePath(filePath));
      
      if (!await file.exists()) {
        return FileStorageResult.failure('File not found: $filePath');
      }

      final data = await file.readAsBytes();
      
      _logger.debug('File read: $filePath (${data.length} bytes)');
      return FileStorageResult.success(data, filePath: filePath, fileSize: data.length);
    } catch (e, stackTrace) {
      _logger.error('Failed to read file: $filePath', e, stackTrace);
      return FileStorageResult.failure(e.toString());
    }
  }

  @override
  Future<FileStorageResult<String>> readTextFile(
    String filePath, {
    Encoding encoding = utf8,
  }) async {
    final result = await readFile(filePath);
    
    if (!result.success) {
      return FileStorageResult.failure(result.error!);
    }

    try {
      final content = encoding.decode(result.data!);
      return FileStorageResult.success(content, filePath: filePath);
    } catch (e, stackTrace) {
      _logger.error('Failed to decode text file: $filePath', e, stackTrace);
      return FileStorageResult.failure('Failed to decode file content');
    }
  }

  @override
  Future<FileStorageResult<Map<String, dynamic>>> readJsonFile(String filePath) async {
    final result = await readTextFile(filePath);
    
    if (!result.success) {
      return FileStorageResult.failure(result.error!);
    }

    try {
      final data = jsonDecode(result.data!) as Map<String, dynamic>;
      return FileStorageResult.success(data, filePath: filePath);
    } catch (e, stackTrace) {
      _logger.error('Failed to parse JSON file: $filePath', e, stackTrace);
      return FileStorageResult.failure('Failed to parse JSON content');
    }
  }

  @override
  Future<FileStorageResult<FileInfo>> copyFile(
    String sourcePath,
    String destinationPath, {
    bool overwrite = false,
  }) async {
    try {
      _ensureInitialized();
      
      final sourceFile = File(_getAbsolutePath(sourcePath));
      final destFile = File(_getAbsolutePath(destinationPath));

      if (!await sourceFile.exists()) {
        return FileStorageResult.failure('Source file not found: $sourcePath');
      }

      if (await destFile.exists() && !overwrite) {
        return FileStorageResult.failure('Destination file already exists: $destinationPath');
      }

      // 创建目标目录
      await destFile.parent.create(recursive: true);
      
      // 复制文件
      await sourceFile.copy(destFile.path);
      
      final fileInfo = await _createFileInfo(destFile);
      
      _logger.info('File copied: $sourcePath -> $destinationPath');
      return FileStorageResult.success(fileInfo, filePath: destinationPath);
    } catch (e, stackTrace) {
      _logger.error('Failed to copy file: $sourcePath -> $destinationPath', e, stackTrace);
      return FileStorageResult.failure(e.toString());
    }
  }

  @override
  Future<FileStorageResult<FileInfo>> moveFile(
    String sourcePath,
    String destinationPath, {
    bool overwrite = false,
  }) async {
    try {
      _ensureInitialized();
      
      final sourceFile = File(_getAbsolutePath(sourcePath));
      final destFile = File(_getAbsolutePath(destinationPath));

      if (!await sourceFile.exists()) {
        return FileStorageResult.failure('Source file not found: $sourcePath');
      }

      if (await destFile.exists() && !overwrite) {
        return FileStorageResult.failure('Destination file already exists: $destinationPath');
      }

      // 创建目标目录
      await destFile.parent.create(recursive: true);
      
      // 移动文件
      await sourceFile.rename(destFile.path);
      
      final fileInfo = await _createFileInfo(destFile);
      
      _logger.info('File moved: $sourcePath -> $destinationPath');
      return FileStorageResult.success(fileInfo, filePath: destinationPath);
    } catch (e, stackTrace) {
      _logger.error('Failed to move file: $sourcePath -> $destinationPath', e, stackTrace);
      return FileStorageResult.failure(e.toString());
    }
  }

  @override
  Future<FileStorageResult<void>> deleteFile(String filePath) async {
    try {
      _ensureInitialized();
      
      final file = File(_getAbsolutePath(filePath));
      
      if (!await file.exists()) {
        return FileStorageResult.failure('File not found: $filePath');
      }

      await file.delete();
      
      _logger.info('File deleted: $filePath');
      return FileStorageResult.success(null);
    } catch (e, stackTrace) {
      _logger.error('Failed to delete file: $filePath', e, stackTrace);
      return FileStorageResult.failure(e.toString());
    }
  }

  @override
  Future<bool> fileExists(String filePath) async {
    try {
      _ensureInitialized();
      final file = File(_getAbsolutePath(filePath));
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  @override
  Future<FileStorageResult<FileInfo>> getFileInfo(String filePath) async {
    try {
      _ensureInitialized();
      
      final file = File(_getAbsolutePath(filePath));
      
      if (!await file.exists()) {
        return FileStorageResult.failure('File not found: $filePath');
      }

      final fileInfo = await _createFileInfo(file);
      return FileStorageResult.success(fileInfo);
    } catch (e, stackTrace) {
      _logger.error('Failed to get file info: $filePath', e, stackTrace);
      return FileStorageResult.failure(e.toString());
    }
  }

  @override
  Future<FileStorageResult<List<FileInfo>>> listFiles(
    String directoryPath, {
    bool recursive = false,
    String? pattern,
  }) async {
    try {
      _ensureInitialized();
      
      final directory = Directory(_getAbsolutePath(directoryPath));
      
      if (!await directory.exists()) {
        return FileStorageResult.failure('Directory not found: $directoryPath');
      }

      final files = <FileInfo>[];
      final entities = directory.listSync(recursive: recursive);
      
      for (final entity in entities) {
        if (entity is File) {
          if (pattern == null || path.basename(entity.path).contains(pattern!)) {
            final fileInfo = await _createFileInfo(entity);
            files.add(fileInfo);
          }
        }
      }
      
      _logger.debug('Listed ${files.length} files in $directoryPath');
      return FileStorageResult.success(files);
    } catch (e, stackTrace) {
      _logger.error('Failed to list files in: $directoryPath', e, stackTrace);
      return FileStorageResult.failure(e.toString());
    }
  }

  @override
  Future<FileStorageResult<void>> createDirectory(String directoryPath) async {
    try {
      _ensureInitialized();
      
      final directory = Directory(_getAbsolutePath(directoryPath));
      await directory.create(recursive: true);
      
      _logger.info('Directory created: $directoryPath');
      return FileStorageResult.success(null);
    } catch (e, stackTrace) {
      _logger.error('Failed to create directory: $directoryPath', e, stackTrace);
      return FileStorageResult.failure(e.toString());
    }
  }

  @override
  Future<FileStorageResult<void>> deleteDirectory(
    String directoryPath, {
    bool recursive = false,
  }) async {
    try {
      _ensureInitialized();
      
      final directory = Directory(_getAbsolutePath(directoryPath));
      
      if (!await directory.exists()) {
        return FileStorageResult.failure('Directory not found: $directoryPath');
      }

      await directory.delete(recursive: recursive);
      
      _logger.info('Directory deleted: $directoryPath');
      return FileStorageResult.success(null);
    } catch (e, stackTrace) {
      _logger.error('Failed to delete directory: $directoryPath', e, stackTrace);
      return FileStorageResult.failure(e.toString());
    }
  }

  @override
  Future<FileStorageResult<Map<String, dynamic>>> getStorageStats() async {
    try {
      _ensureInitialized();
      
      final baseDir = Directory(_baseDirectory!);
      final tempDir = Directory(_tempDirectory!);
      final cacheDir = Directory(_cacheDirectory!);
      
      final stats = <String, dynamic>{
        'baseDirectory': _baseDirectory,
        'tempDirectory': _tempDirectory,
        'cacheDirectory': _cacheDirectory,
        'baseDirSize': await _getDirectorySize(baseDir),
        'tempDirSize': await _getDirectorySize(tempDir),
        'cacheDirSize': await _getDirectorySize(cacheDir),
        'totalFiles': await _countFiles(baseDir),
        'lastUpdated': DateTime.now().toIso8601String(),
      };
      
      return FileStorageResult.success(stats);
    } catch (e, stackTrace) {
      _logger.error('Failed to get storage stats', e, stackTrace);
      return FileStorageResult.failure(e.toString());
    }
  }

  @override
  Future<FileStorageResult<int>> cleanupTempFiles() async {
    try {
      _ensureInitialized();
      
      final tempDir = Directory(_tempDirectory!);
      int deletedCount = 0;
      
      if (await tempDir.exists()) {
        final entities = tempDir.listSync(recursive: true);
        final now = DateTime.now();
        
        for (final entity in entities) {
          if (entity is File) {
            final stat = await entity.stat();
            final age = now.difference(stat.modified);
            
            // 删除超过24小时的临时文件
            if (age.inHours > 24) {
              await entity.delete();
              deletedCount++;
            }
          }
        }
      }
      
      _logger.info('Cleaned up $deletedCount temporary files');
      return FileStorageResult.success(deletedCount);
    } catch (e, stackTrace) {
      _logger.error('Failed to cleanup temp files', e, stackTrace);
      return FileStorageResult.failure(e.toString());
    }
  }

  Future<FileInfo> _createFileInfo(File file) async {
    final stat = await file.stat();
    final fileName = path.basename(file.path);
    final extension = path.extension(fileName);
    
    String? checksum;
    if (_config.enableChecksum) {
      final bytes = await file.readAsBytes();
      checksum = sha256.convert(bytes).toString();
    }
    
    return FileInfo(
      name: fileName,
      path: file.path,
      size: stat.size,
      createdAt: stat.changed,
      modifiedAt: stat.modified,
      extension: extension,
      mimeType: _getMimeType(extension),
      checksum: checksum,
    );
  }

  String _getMimeType(String extension) {
    switch (extension.toLowerCase()) {
      case '.txt':
        return 'text/plain';
      case '.json':
        return 'application/json';
      case '.xml':
        return 'application/xml';
      case '.html':
        return 'text/html';
      case '.css':
        return 'text/css';
      case '.js':
        return 'application/javascript';
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.gif':
        return 'image/gif';
      case '.pdf':
        return 'application/pdf';
      case '.zip':
        return 'application/zip';
      default:
        return 'application/octet-stream';
    }
  }

  Future<int> _getDirectorySize(Directory directory) async {
    int size = 0;
    if (await directory.exists()) {
      final entities = directory.listSync(recursive: true);
      for (final entity in entities) {
        if (entity is File) {
          final stat = await entity.stat();
          size += stat.size;
        }
      }
    }
    return size;
  }

  Future<int> _countFiles(Directory directory) async {
    int count = 0;
    if (await directory.exists()) {
      final entities = directory.listSync(recursive: true);
      for (final entity in entities) {
        if (entity is File) {
          count++;
        }
      }
    }
    return count;
  }

  String _getAbsolutePath(String filePath) {
    if (path.isAbsolute(filePath)) {
      return filePath;
    }
    return path.join(_baseDirectory!, filePath);
  }

  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('FileStorageService not initialized. Call initialize() first.');
    }
  }

  @override
  String get baseDirectory => _baseDirectory ?? '';

  @override
  String get tempDirectory => _tempDirectory ?? '';

  @override
  String get cacheDirectory => _cacheDirectory ?? '';
}
```

## 3. 缓存系统

### 3.1 缓存接口定义

```dart
// lib/core/cache/cache_service.dart
abstract class CacheService {
  Future<void> initialize();
  Future<void> dispose();
  
  // 基本操作
  Future<T?> get<T>(String key);
  Future<void> set<T>(String key, T value, {Duration? ttl});
  Future<void> delete(String key);
  Future<bool> exists(String key);
  Future<void> clear();
  
  // 批量操作
  Future<Map<String, T?>> getMultiple<T>(List<String> keys);
  Future<void> setMultiple<T>(Map<String, T> keyValues, {Duration? ttl});
  Future<void> deleteMultiple(List<String> keys);
  
  // 缓存信息
  Future<int> size();
  Future<List<String>> keys({String? pattern});
  Future<Map<String, dynamic>> stats();
  
  // TTL 操作
  Future<Duration?> getTtl(String key);
  Future<void> setTtl(String key, Duration ttl);
  Future<void> removeTtl(String key);
}

// 缓存项
class CacheItem<T> {
  final String key;
  final T value;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final int accessCount;
  final DateTime lastAccessedAt;
  
  const CacheItem({
    required this.key,
    required this.value,
    required this.createdAt,
    this.expiresAt,
    this.accessCount = 0,
    required this.lastAccessedAt,
  });
  
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }
  
  CacheItem<T> copyWith({
    String? key,
    T? value,
    DateTime? createdAt,
    DateTime? expiresAt,
    int? accessCount,
    DateTime? lastAccessedAt,
  }) {
    return CacheItem<T>(
      key: key ?? this.key,
      value: value ?? this.value,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      accessCount: accessCount ?? this.accessCount,
      lastAccessedAt: lastAccessedAt ?? this.lastAccessedAt,
    );
  }
}

// 缓存配置
class CacheConfig {
  final int maxSize;
  final Duration defaultTtl;
  final bool enableCompression;
  final bool enableEncryption;
  final Duration cleanupInterval;
  final bool enableStats;
  
  const CacheConfig({
    this.maxSize = 1000,
    this.defaultTtl = const Duration(hours: 1),
    this.enableCompression = false,
    this.enableEncryption = false,
    this.cleanupInterval = const Duration(minutes: 5),
    this.enableStats = true,
  });
}

// 缓存统计
class CacheStats {
  final int hitCount;
  final int missCount;
  final int evictionCount;
  final int size;
  final int maxSize;
  final double hitRate;
  final DateTime lastResetAt;
  
  const CacheStats({
    required this.hitCount,
    required this.missCount,
    required this.evictionCount,
    required this.size,
    required this.maxSize,
    required this.hitRate,
    required this.lastResetAt,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'hitCount': hitCount,
      'missCount': missCount,
      'evictionCount': evictionCount,
      'size': size,
      'maxSize': maxSize,
      'hitRate': hitRate,
      'lastResetAt': lastResetAt.toIso8601String(),
    };
  }
}
```

### 3.2 内存缓存实现

```dart
// lib/core/cache/memory_cache_service.dart
import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'dart:io';

class MemoryCacheService implements CacheService {
  final CacheConfig _config;
  final Logger _logger;
  
  late final LinkedHashMap<String, CacheItem<dynamic>> _cache;
  late Timer _cleanupTimer;
  
  int _hitCount = 0;
  int _missCount = 0;
  int _evictionCount = 0;
  DateTime _statsResetAt = DateTime.now();
  
  bool _isInitialized = false;
  
  MemoryCacheService({
    required CacheConfig config,
    required Logger logger,
  }) : _config = config,
       _logger = logger;
  
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    _cache = LinkedHashMap<String, CacheItem<dynamic>>();
    
    // 启动定期清理任务
    _cleanupTimer = Timer.periodic(_config.cleanupInterval, (_) {
      _cleanupExpiredItems();
    });
    
    _isInitialized = true;
    _logger.info('MemoryCacheService initialized with maxSize: ${_config.maxSize}');
  }
  
  @override
  Future<void> dispose() async {
    if (!_isInitialized) return;
    
    _cleanupTimer.cancel();
    _cache.clear();
    _isInitialized = false;
    
    _logger.info('MemoryCacheService disposed');
  }
  
  @override
  Future<T?> get<T>(String key) async {
    _ensureInitialized();
    
    final item = _cache[key];
    
    if (item == null) {
      _missCount++;
      _logger.debug('Cache miss for key: $key');
      return null;
    }
    
    if (item.isExpired) {
      _cache.remove(key);
      _missCount++;
      _logger.debug('Cache expired for key: $key');
      return null;
    }
    
    // 更新访问信息
    final updatedItem = item.copyWith(
      accessCount: item.accessCount + 1,
      lastAccessedAt: DateTime.now(),
    );
    _cache[key] = updatedItem;
    
    _hitCount++;
    _logger.debug('Cache hit for key: $key');
    
    return item.value as T?;
  }
  
  @override
  Future<void> set<T>(String key, T value, {Duration? ttl}) async {
    _ensureInitialized();
    
    final now = DateTime.now();
    final effectiveTtl = ttl ?? _config.defaultTtl;
    final expiresAt = now.add(effectiveTtl);
    
    final item = CacheItem<T>(
      key: key,
      value: value,
      createdAt: now,
      expiresAt: expiresAt,
      lastAccessedAt: now,
    );
    
    _cache[key] = item;
    
    // 检查缓存大小限制
    if (_cache.length > _config.maxSize) {
      _evictLeastRecentlyUsed();
    }
    
    _logger.debug('Cache set for key: $key, expires at: $expiresAt');
  }
  
  @override
  Future<void> delete(String key) async {
    _ensureInitialized();
    
    final removed = _cache.remove(key);
    if (removed != null) {
      _logger.debug('Cache deleted for key: $key');
    }
  }
  
  @override
  Future<bool> exists(String key) async {
    _ensureInitialized();
    
    final item = _cache[key];
    if (item == null) return false;
    
    if (item.isExpired) {
      _cache.remove(key);
      return false;
    }
    
    return true;
  }
  
  @override
  Future<void> clear() async {
    _ensureInitialized();
    
    final size = _cache.length;
    _cache.clear();
    _resetStats();
    
    _logger.info('Cache cleared, removed $size items');
  }
  
  @override
  Future<Map<String, T?>> getMultiple<T>(List<String> keys) async {
    final result = <String, T?>{};
    
    for (final key in keys) {
      result[key] = await get<T>(key);
    }
    
    return result;
  }
  
  @override
  Future<void> setMultiple<T>(Map<String, T> keyValues, {Duration? ttl}) async {
    for (final entry in keyValues.entries) {
      await set<T>(entry.key, entry.value, ttl: ttl);
    }
  }
  
  @override
  Future<void> deleteMultiple(List<String> keys) async {
    for (final key in keys) {
      await delete(key);
    }
  }
  
  @override
  Future<int> size() async {
    _ensureInitialized();
    return _cache.length;
  }
  
  @override
  Future<List<String>> keys({String? pattern}) async {
    _ensureInitialized();
    
    if (pattern == null) {
      return _cache.keys.toList();
    }
    
    final regex = RegExp(pattern);
    return _cache.keys.where((key) => regex.hasMatch(key)).toList();
  }
  
  @override
  Future<Map<String, dynamic>> stats() async {
    _ensureInitialized();
    
    final hitRate = _hitCount + _missCount > 0 
        ? _hitCount / (_hitCount + _missCount) 
        : 0.0;
    
    final stats = CacheStats(
      hitCount: _hitCount,
      missCount: _missCount,
      evictionCount: _evictionCount,
      size: _cache.length,
      maxSize: _config.maxSize,
      hitRate: hitRate,
      lastResetAt: _statsResetAt,
    );
    
    return stats.toJson();
  }
  
  @override
  Future<Duration?> getTtl(String key) async {
    _ensureInitialized();
    
    final item = _cache[key];
    if (item?.expiresAt == null) return null;
    
    final remaining = item!.expiresAt!.difference(DateTime.now());
    return remaining.isNegative ? null : remaining;
  }
  
  @override
  Future<void> setTtl(String key, Duration ttl) async {
    _ensureInitialized();
    
    final item = _cache[key];
    if (item == null) return;
    
    final updatedItem = item.copyWith(
      expiresAt: DateTime.now().add(ttl),
    );
    _cache[key] = updatedItem;
  }
  
  @override
  Future<void> removeTtl(String key) async {
    _ensureInitialized();
    
    final item = _cache[key];
    if (item == null) return;
    
    final updatedItem = item.copyWith(expiresAt: null);
    _cache[key] = updatedItem;
  }
  
  void _cleanupExpiredItems() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _cache.entries) {
      if (entry.value.expiresAt != null && 
          now.isAfter(entry.value.expiresAt!)) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _cache.remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      _logger.debug('Cleaned up ${expiredKeys.length} expired cache items');
    }
  }
  
  void _evictLeastRecentlyUsed() {
    if (_cache.isEmpty) return;
    
    // LinkedHashMap 保持插入顺序，移除最旧的项
    final firstKey = _cache.keys.first;
    _cache.remove(firstKey);
    _evictionCount++;
    
    _logger.debug('Evicted LRU cache item: $firstKey');
  }
  
  void _resetStats() {
    _hitCount = 0;
    _missCount = 0;
    _evictionCount = 0;
    _statsResetAt = DateTime.now();
  }
  
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('MemoryCacheService not initialized. Call initialize() first.');
    }
  }
}
```

### 3.3 持久化缓存实现

```dart
// lib/core/cache/persistent_cache_service.dart
import 'dart:convert';
import 'dart:io';
import 'package:path/path.dart' as path;

class PersistentCacheService implements CacheService {
  final CacheConfig _config;
  final Logger _logger;
  final String _cacheDirectory;
  
  late Timer _cleanupTimer;
  
  int _hitCount = 0;
  int _missCount = 0;
  int _evictionCount = 0;
  DateTime _statsResetAt = DateTime.now();
  
  bool _isInitialized = false;
  
  PersistentCacheService({
    required CacheConfig config,
    required Logger logger,
    required String cacheDirectory,
  }) : _config = config,
       _logger = logger,
       _cacheDirectory = cacheDirectory;
  
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    // 创建缓存目录
    final directory = Directory(_cacheDirectory);
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    
    // 启动定期清理任务
    _cleanupTimer = Timer.periodic(_config.cleanupInterval, (_) {
      _cleanupExpiredItems();
    });
    
    _isInitialized = true;
    _logger.info('PersistentCacheService initialized at: $_cacheDirectory');
  }
  
  @override
  Future<void> dispose() async {
    if (!_isInitialized) return;
    
    _cleanupTimer.cancel();
    _isInitialized = false;
    
    _logger.info('PersistentCacheService disposed');
  }
  
  @override
  Future<T?> get<T>(String key) async {
    _ensureInitialized();
    
    try {
      final file = File(_getFilePath(key));
      
      if (!await file.exists()) {
        _missCount++;
        _logger.debug('Cache miss for key: $key');
        return null;
      }
      
      final content = await file.readAsString();
      final data = jsonDecode(content) as Map<String, dynamic>;
      
      final item = CacheItem<T>(
        key: data['key'] as String,
        value: data['value'] as T,
        createdAt: DateTime.parse(data['createdAt'] as String),
        expiresAt: data['expiresAt'] != null 
            ? DateTime.parse(data['expiresAt'] as String) 
            : null,
        accessCount: data['accessCount'] as int? ?? 0,
        lastAccessedAt: DateTime.parse(data['lastAccessedAt'] as String),
      );
      
      if (item.isExpired) {
        await file.delete();
        _missCount++;
        _logger.debug('Cache expired for key: $key');
        return null;
      }
      
      // 更新访问信息
      final updatedItem = item.copyWith(
        accessCount: item.accessCount + 1,
        lastAccessedAt: DateTime.now(),
      );
      await _saveItem(updatedItem);
      
      _hitCount++;
      _logger.debug('Cache hit for key: $key');
      
      return item.value;
    } catch (e, stackTrace) {
      _logger.error('Failed to get cache item: $key', e, stackTrace);
      _missCount++;
      return null;
    }
  }
  
  @override
  Future<void> set<T>(String key, T value, {Duration? ttl}) async {
    _ensureInitialized();
    
    try {
      final now = DateTime.now();
      final effectiveTtl = ttl ?? _config.defaultTtl;
      final expiresAt = now.add(effectiveTtl);
      
      final item = CacheItem<T>(
        key: key,
        value: value,
        createdAt: now,
        expiresAt: expiresAt,
        lastAccessedAt: now,
      );
      
      await _saveItem(item);
      
      // 检查缓存大小限制
      await _enforceMaxSize();
      
      _logger.debug('Cache set for key: $key, expires at: $expiresAt');
    } catch (e, stackTrace) {
      _logger.error('Failed to set cache item: $key', e, stackTrace);
    }
  }
  
  @override
  Future<void> delete(String key) async {
    _ensureInitialized();
    
    try {
      final file = File(_getFilePath(key));
      
      if (await file.exists()) {
        await file.delete();
        _logger.debug('Cache deleted for key: $key');
      }
    } catch (e, stackTrace) {
      _logger.error('Failed to delete cache item: $key', e, stackTrace);
    }
  }
  
  @override
  Future<bool> exists(String key) async {
    _ensureInitialized();
    
    try {
      final file = File(_getFilePath(key));
      
      if (!await file.exists()) {
        return false;
      }
      
      final content = await file.readAsString();
      final data = jsonDecode(content) as Map<String, dynamic>;
      
      final expiresAt = data['expiresAt'] != null 
          ? DateTime.parse(data['expiresAt'] as String) 
          : null;
      
      if (expiresAt != null && DateTime.now().isAfter(expiresAt)) {
        await file.delete();
        return false;
      }
      
      return true;
    } catch (e, stackTrace) {
      _logger.error('Failed to check cache existence: $key', e, stackTrace);
      return false;
    }
  }
  
  @override
  Future<void> clear() async {
    _ensureInitialized();
    
    try {
      final directory = Directory(_cacheDirectory);
      
      if (await directory.exists()) {
        final files = directory.listSync().whereType<File>();
        int deletedCount = 0;
        
        for (final file in files) {
          if (file.path.endsWith('.cache')) {
            await file.delete();
            deletedCount++;
          }
        }
        
        _resetStats();
        _logger.info('Cache cleared, removed $deletedCount items');
      }
    } catch (e, stackTrace) {
      _logger.error('Failed to clear cache', e, stackTrace);
    }
  }
  
  @override
  Future<Map<String, T?>> getMultiple<T>(List<String> keys) async {
    final result = <String, T?>{};
    
    for (final key in keys) {
      result[key] = await get<T>(key);
    }
    
    return result;
  }
  
  @override
  Future<void> setMultiple<T>(Map<String, T> keyValues, {Duration? ttl}) async {
    for (final entry in keyValues.entries) {
      await set<T>(entry.key, entry.value, ttl: ttl);
    }
  }
  
  @override
  Future<void> deleteMultiple(List<String> keys) async {
    for (final key in keys) {
      await delete(key);
    }
  }
  
  @override
  Future<int> size() async {
    _ensureInitialized();
    
    try {
      final directory = Directory(_cacheDirectory);
      
      if (!await directory.exists()) {
        return 0;
      }
      
      final files = directory.listSync().whereType<File>();
      return files.where((file) => file.path.endsWith('.cache')).length;
    } catch (e, stackTrace) {
      _logger.error('Failed to get cache size', e, stackTrace);
      return 0;
    }
  }
  
  @override
  Future<List<String>> keys({String? pattern}) async {
    _ensureInitialized();
    
    try {
      final directory = Directory(_cacheDirectory);
      
      if (!await directory.exists()) {
        return [];
      }
      
      final files = directory.listSync().whereType<File>();
      final keys = <String>[];
      
      for (final file in files) {
        if (file.path.endsWith('.cache')) {
          final fileName = path.basenameWithoutExtension(file.path);
          final key = Uri.decodeComponent(fileName);
          
          if (pattern == null || RegExp(pattern).hasMatch(key)) {
            keys.add(key);
          }
        }
      }
      
      return keys;
    } catch (e, stackTrace) {
      _logger.error('Failed to get cache keys', e, stackTrace);
      return [];
    }
  }
  
  @override
  Future<Map<String, dynamic>> stats() async {
    _ensureInitialized();
    
    final hitRate = _hitCount + _missCount > 0 
        ? _hitCount / (_hitCount + _missCount) 
        : 0.0;
    
    final cacheSize = await size();
    
    final stats = CacheStats(
      hitCount: _hitCount,
      missCount: _missCount,
      evictionCount: _evictionCount,
      size: cacheSize,
      maxSize: _config.maxSize,
      hitRate: hitRate,
      lastResetAt: _statsResetAt,
    );
    
    return stats.toJson();
  }
  
  @override
  Future<Duration?> getTtl(String key) async {
    _ensureInitialized();
    
    try {
      final file = File(_getFilePath(key));
      
      if (!await file.exists()) {
        return null;
      }
      
      final content = await file.readAsString();
      final data = jsonDecode(content) as Map<String, dynamic>;
      
      final expiresAt = data['expiresAt'] != null 
          ? DateTime.parse(data['expiresAt'] as String) 
          : null;
      
      if (expiresAt == null) return null;
      
      final remaining = expiresAt.difference(DateTime.now());
      return remaining.isNegative ? null : remaining;
    } catch (e, stackTrace) {
      _logger.error('Failed to get TTL for key: $key', e, stackTrace);
      return null;
    }
  }
  
  @override
  Future<void> setTtl(String key, Duration ttl) async {
    final item = await get(key);
    if (item != null) {
      await set(key, item, ttl: ttl);
    }
  }
  
  @override
  Future<void> removeTtl(String key) async {
    final item = await get(key);
    if (item != null) {
      await set(key, item, ttl: const Duration(days: 365 * 10)); // 设置很长的TTL
    }
  }
  
  Future<void> _saveItem<T>(CacheItem<T> item) async {
    final file = File(_getFilePath(item.key));
    
    final data = {
      'key': item.key,
      'value': item.value,
      'createdAt': item.createdAt.toIso8601String(),
      'expiresAt': item.expiresAt?.toIso8601String(),
      'accessCount': item.accessCount,
      'lastAccessedAt': item.lastAccessedAt.toIso8601String(),
    };
    
    await file.writeAsString(jsonEncode(data));
  }
  
  Future<void> _cleanupExpiredItems() async {
    try {
      final directory = Directory(_cacheDirectory);
      
      if (!await directory.exists()) {
        return;
      }
      
      final files = directory.listSync().whereType<File>();
      final now = DateTime.now();
      int deletedCount = 0;
      
      for (final file in files) {
        if (file.path.endsWith('.cache')) {
          try {
            final content = await file.readAsString();
            final data = jsonDecode(content) as Map<String, dynamic>;
            
            final expiresAt = data['expiresAt'] != null 
                ? DateTime.parse(data['expiresAt'] as String) 
                : null;
            
            if (expiresAt != null && now.isAfter(expiresAt)) {
              await file.delete();
              deletedCount++;
            }
          } catch (e) {
            // 删除损坏的缓存文件
            await file.delete();
            deletedCount++;
          }
        }
      }
      
      if (deletedCount > 0) {
        _logger.debug('Cleaned up $deletedCount expired cache items');
      }
    } catch (e, stackTrace) {
      _logger.error('Failed to cleanup expired items', e, stackTrace);
    }
  }
  
  Future<void> _enforceMaxSize() async {
    try {
      final directory = Directory(_cacheDirectory);
      
      if (!await directory.exists()) {
        return;
      }
      
      final files = directory.listSync().whereType<File>()
          .where((file) => file.path.endsWith('.cache'))
          .toList();
      
      if (files.length <= _config.maxSize) {
        return;
      }
      
      // 按最后访问时间排序，删除最旧的文件
      final fileInfos = <MapEntry<File, DateTime>>[];
      
      for (final file in files) {
        try {
          final content = await file.readAsString();
          final data = jsonDecode(content) as Map<String, dynamic>;
          final lastAccessedAt = DateTime.parse(data['lastAccessedAt'] as String);
          fileInfos.add(MapEntry(file, lastAccessedAt));
        } catch (e) {
          // 删除损坏的文件
          await file.delete();
        }
      }
      
      fileInfos.sort((a, b) => a.value.compareTo(b.value));
      
      final toDelete = fileInfos.length - _config.maxSize;
      for (int i = 0; i < toDelete; i++) {
        await fileInfos[i].key.delete();
        _evictionCount++;
      }
      
      if (toDelete > 0) {
        _logger.debug('Evicted $toDelete cache items to enforce max size');
      }
    } catch (e, stackTrace) {
      _logger.error('Failed to enforce max cache size', e, stackTrace);
    }
  }
  
  String _getFilePath(String key) {
    final encodedKey = Uri.encodeComponent(key);
    return path.join(_cacheDirectory, '$encodedKey.cache');
  }
  
  void _resetStats() {
    _hitCount = 0;
    _missCount = 0;
    _evictionCount = 0;
    _statsResetAt = DateTime.now();
  }
  
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('PersistentCacheService not initialized. Call initialize() first.');
    }
  }
}
```

### 3.4 LRU 缓存实现

```dart
// lib/core/cache/lru_cache_service.dart
import 'dart:collection';

class LruCacheService implements CacheService {
  final CacheConfig _config;
  final Logger _logger;
  
  late final LinkedHashMap<String, CacheItem<dynamic>> _cache;
  late Timer _cleanupTimer;
  
  int _hitCount = 0;
  int _missCount = 0;
  int _evictionCount = 0;
  DateTime _statsResetAt = DateTime.now();
  
  bool _isInitialized = false;
  
  LruCacheService({
    required CacheConfig config,
    required Logger logger,
  }) : _config = config,
       _logger = logger;
  
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    _cache = LinkedHashMap<String, CacheItem<dynamic>>();
    
    // 启动定期清理任务
    _cleanupTimer = Timer.periodic(_config.cleanupInterval, (_) {
      _cleanupExpiredItems();
    });
    
    _isInitialized = true;
    _logger.info('LruCacheService initialized with maxSize: ${_config.maxSize}');
  }
  
  @override
  Future<void> dispose() async {
    if (!_isInitialized) return;
    
    _cleanupTimer.cancel();
    _cache.clear();
    _isInitialized = false;
    
    _logger.info('LruCacheService disposed');
  }
  
  @override
  Future<T?> get<T>(String key) async {
    _ensureInitialized();
    
    final item = _cache.remove(key); // 移除并重新插入以更新LRU顺序
    
    if (item == null) {
      _missCount++;
      _logger.debug('Cache miss for key: $key');
      return null;
    }
    
    if (item.isExpired) {
      _missCount++;
      _logger.debug('Cache expired for key: $key');
      return null;
    }
    
    // 更新访问信息并重新插入到末尾（最近使用）
    final updatedItem = item.copyWith(
      accessCount: item.accessCount + 1,
      lastAccessedAt: DateTime.now(),
    );
    _cache[key] = updatedItem;
    
    _hitCount++;
    _logger.debug('Cache hit for key: $key');
    
    return item.value as T?;
  }
  
  @override
  Future<void> set<T>(String key, T value, {Duration? ttl}) async {
    _ensureInitialized();
    
    final now = DateTime.now();
    final effectiveTtl = ttl ?? _config.defaultTtl;
    final expiresAt = now.add(effectiveTtl);
    
    // 如果键已存在，先移除
    _cache.remove(key);
    
    final item = CacheItem<T>(
      key: key,
      value: value,
      createdAt: now,
      expiresAt: expiresAt,
      lastAccessedAt: now,
    );
    
    _cache[key] = item;
    
    // 检查缓存大小限制，移除最少使用的项
    while (_cache.length > _config.maxSize) {
      _evictLeastRecentlyUsed();
    }
    
    _logger.debug('Cache set for key: $key, expires at: $expiresAt');
  }
  
  @override
  Future<void> delete(String key) async {
    _ensureInitialized();
    
    final removed = _cache.remove(key);
    if (removed != null) {
      _logger.debug('Cache deleted for key: $key');
    }
  }
  
  @override
  Future<bool> exists(String key) async {
    _ensureInitialized();
    
    final item = _cache[key];
    if (item == null) return false;
    
    if (item.isExpired) {
      _cache.remove(key);
      return false;
    }
    
    return true;
  }
  
  @override
  Future<void> clear() async {
    _ensureInitialized();
    
    final size = _cache.length;
    _cache.clear();
    _resetStats();
    
    _logger.info('Cache cleared, removed $size items');
  }
  
  @override
  Future<Map<String, T?>> getMultiple<T>(List<String> keys) async {
    final result = <String, T?>{};
    
    for (final key in keys) {
      result[key] = await get<T>(key);
    }
    
    return result;
  }
  
  @override
  Future<void> setMultiple<T>(Map<String, T> keyValues, {Duration? ttl}) async {
    for (final entry in keyValues.entries) {
      await set<T>(entry.key, entry.value, ttl: ttl);
    }
  }
  
  @override
  Future<void> deleteMultiple(List<String> keys) async {
    for (final key in keys) {
      await delete(key);
    }
  }
  
  @override
  Future<int> size() async {
    _ensureInitialized();
    return _cache.length;
  }
  
  @override
  Future<List<String>> keys({String? pattern}) async {
    _ensureInitialized();
    
    if (pattern == null) {
      return _cache.keys.toList();
    }
    
    final regex = RegExp(pattern);
    return _cache.keys.where((key) => regex.hasMatch(key)).toList();
  }
  
  @override
  Future<Map<String, dynamic>> stats() async {
    _ensureInitialized();
    
    final hitRate = _hitCount + _missCount > 0 
        ? _hitCount / (_hitCount + _missCount) 
        : 0.0;
    
    final stats = CacheStats(
      hitCount: _hitCount,
      missCount: _missCount,
      evictionCount: _evictionCount,
      size: _cache.length,
      maxSize: _config.maxSize,
      hitRate: hitRate,
      lastResetAt: _statsResetAt,
    );
    
    return stats.toJson();
  }
  
  @override
  Future<Duration?> getTtl(String key) async {
    _ensureInitialized();
    
    final item = _cache[key];
    if (item?.expiresAt == null) return null;
    
    final remaining = item!.expiresAt!.difference(DateTime.now());
    return remaining.isNegative ? null : remaining;
  }
  
  @override
  Future<void> setTtl(String key, Duration ttl) async {
    _ensureInitialized();
    
    final item = _cache[key];
    if (item == null) return;
    
    final updatedItem = item.copyWith(
      expiresAt: DateTime.now().add(ttl),
    );
    _cache[key] = updatedItem;
  }
  
  @override
  Future<void> removeTtl(String key) async {
    _ensureInitialized();
    
    final item = _cache[key];
    if (item == null) return;
    
    final updatedItem = item.copyWith(expiresAt: null);
    _cache[key] = updatedItem;
  }
  
  void _cleanupExpiredItems() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _cache.entries) {
      if (entry.value.expiresAt != null && 
          now.isAfter(entry.value.expiresAt!)) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _cache.remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      _logger.debug('Cleaned up ${expiredKeys.length} expired cache items');
    }
  }
  
  void _evictLeastRecentlyUsed() {
    if (_cache.isEmpty) return;
    
    // LinkedHashMap 中第一个键是最少使用的
    final firstKey = _cache.keys.first;
    _cache.remove(firstKey);
    _evictionCount++;
    
    _logger.debug('Evicted LRU cache item: $firstKey');
  }
  
  void _resetStats() {
    _hitCount = 0;
    _missCount = 0;
    _evictionCount = 0;
    _statsResetAt = DateTime.now();
  }
  
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('LruCacheService not initialized. Call initialize() first.');
    }
  }
}
```

### 3.5 缓存管理器

```dart
// lib/core/cache/cache_manager.dart
enum CacheType {
  memory,
  persistent,
  lru,
}

class CacheManager {
  final Map<CacheType, CacheService> _caches = {};
  final Logger _logger;
  
  bool _isInitialized = false;
  
  CacheManager({required Logger logger}) : _logger = logger;
  
  Future<void> initialize({
    required String cacheDirectory,
    CacheConfig? memoryConfig,
    CacheConfig? persistentConfig,
    CacheConfig? lruConfig,
  }) async {
    if (_isInitialized) return;
    
    // 初始化内存缓存
    final memoryCache = MemoryCacheService(
      config: memoryConfig ?? const CacheConfig(),
      logger: _logger,
    );
    await memoryCache.initialize();
    _caches[CacheType.memory] = memoryCache;
    
    // 初始化持久化缓存
    final persistentCache = PersistentCacheService(
      config: persistentConfig ?? const CacheConfig(),
      logger: _logger,
      cacheDirectory: cacheDirectory,
    );
    await persistentCache.initialize();
    _caches[CacheType.persistent] = persistentCache;
    
    // 初始化LRU缓存
    final lruCache = LruCacheService(
      config: lruConfig ?? const CacheConfig(),
      logger: _logger,
    );
    await lruCache.initialize();
    _caches[CacheType.lru] = lruCache;
    
    _isInitialized = true;
    _logger.info('CacheManager initialized with ${_caches.length} cache types');
  }
  
  Future<void> dispose() async {
    if (!_isInitialized) return;
    
    for (final cache in _caches.values) {
      await cache.dispose();
    }
    _caches.clear();
    _isInitialized = false;
    
    _logger.info('CacheManager disposed');
  }
  
  CacheService getCache(CacheType type) {
    _ensureInitialized();
    
    final cache = _caches[type];
    if (cache == null) {
      throw ArgumentError('Cache type $type not initialized');
    }
    
    return cache;
  }
  
  Future<T?> get<T>(String key, {CacheType type = CacheType.memory}) async {
    return await getCache(type).get<T>(key);
  }
  
  Future<void> set<T>(
    String key, 
    T value, {
    CacheType type = CacheType.memory,
    Duration? ttl,
  }) async {
    await getCache(type).set<T>(key, value, ttl: ttl);
  }
  
  Future<void> delete(String key, {CacheType? type}) async {
    if (type != null) {
      await getCache(type).delete(key);
    } else {
      // 从所有缓存中删除
      for (final cache in _caches.values) {
        await cache.delete(key);
      }
    }
  }
  
  Future<bool> exists(String key, {CacheType type = CacheType.memory}) async {
    return await getCache(type).exists(key);
  }
  
  Future<void> clear({CacheType? type}) async {
    if (type != null) {
      await getCache(type).clear();
    } else {
      // 清空所有缓存
      for (final cache in _caches.values) {
        await cache.clear();
      }
    }
  }
  
  Future<Map<CacheType, Map<String, dynamic>>> getAllStats() async {
    _ensureInitialized();
    
    final stats = <CacheType, Map<String, dynamic>>{};
    
    for (final entry in _caches.entries) {
      stats[entry.key] = await entry.value.stats();
    }
    
    return stats;
  }
  
  // 多级缓存策略：先查内存，再查持久化
  Future<T?> getWithFallback<T>(String key) async {
    // 先从内存缓存获取
    T? value = await get<T>(key, type: CacheType.memory);
    if (value != null) {
      return value;
    }
    
    // 从持久化缓存获取
    value = await get<T>(key, type: CacheType.persistent);
    if (value != null) {
      // 回写到内存缓存
      await set<T>(key, value, type: CacheType.memory);
      return value;
    }
    
    return null;
  }
  
  // 多级缓存写入：同时写入内存和持久化缓存
  Future<void> setMultiLevel<T>(
    String key, 
    T value, {
    Duration? memoryTtl,
    Duration? persistentTtl,
  }) async {
    await Future.wait([
      set<T>(key, value, type: CacheType.memory, ttl: memoryTtl),
      set<T>(key, value, type: CacheType.persistent, ttl: persistentTtl),
    ]);
  }
  
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('CacheManager not initialized. Call initialize() first.');
    }
  }
}
```

## 4. 数据同步服务

### 4.1 数据同步接口

```dart
// lib/core/sync/data_sync_service.dart
abstract class DataSyncService {
  Future<void> initialize();
  Future<void> dispose();
  
  // 同步操作
  Future<SyncResult> syncUp();
  Future<SyncResult> syncDown();
  Future<SyncResult> fullSync();
  
  // 冲突解决
  Future<SyncResult> resolveConflicts(List<SyncConflict> conflicts);
  
  // 同步状态
  Stream<SyncStatus> get syncStatusStream;
  Future<SyncStatus> getSyncStatus();
  
  // 离线支持
  Future<void> enableOfflineMode();
  Future<void> disableOfflineMode();
  bool get isOfflineMode;
  
  // 数据变更监听
  void addChangeListener(String entityType, DataChangeListener listener);
  void removeChangeListener(String entityType, DataChangeListener listener);
}

// 同步结果
class SyncResult {
  final bool success;
  final String? error;
  final int uploadedCount;
  final int downloadedCount;
  final int conflictCount;
  final List<SyncConflict> conflicts;
  final DateTime timestamp;
  
  const SyncResult({
    required this.success,
    this.error,
    this.uploadedCount = 0,
    this.downloadedCount = 0,
    this.conflictCount = 0,
    this.conflicts = const [],
    required this.timestamp,
  });
  
  factory SyncResult.success({
    int uploadedCount = 0,
    int downloadedCount = 0,
    int conflictCount = 0,
    List<SyncConflict> conflicts = const [],
  }) {
    return SyncResult(
      success: true,
      uploadedCount: uploadedCount,
      downloadedCount: downloadedCount,
      conflictCount: conflictCount,
      conflicts: conflicts,
      timestamp: DateTime.now(),
    );
  }
  
  factory SyncResult.failure(String error) {
    return SyncResult(
      success: false,
      error: error,
      timestamp: DateTime.now(),
    );
  }
}

// 同步冲突
class SyncConflict {
  final String entityId;
  final String entityType;
  final Map<String, dynamic> localData;
  final Map<String, dynamic> remoteData;
  final DateTime localModifiedAt;
  final DateTime remoteModifiedAt;
  final ConflictType type;
  
  const SyncConflict({
    required this.entityId,
    required this.entityType,
    required this.localData,
    required this.remoteData,
    required this.localModifiedAt,
    required this.remoteModifiedAt,
    required this.type,
  });
}

enum ConflictType {
  update,
  delete,
  create,
}

// 同步状态
enum SyncStatus {
  idle,
  syncing,
  success,
  error,
  conflict,
}

// 数据变更监听器
typedef DataChangeListener = void Function(DataChangeEvent event);

// 数据变更事件
class DataChangeEvent {
  final String entityId;
  final String entityType;
  final DataChangeType type;
  final Map<String, dynamic>? oldData;
  final Map<String, dynamic>? newData;
  final DateTime timestamp;
  
  const DataChangeEvent({
    required this.entityId,
    required this.entityType,
    required this.type,
    this.oldData,
    this.newData,
    required this.timestamp,
  });
}

enum DataChangeType {
  create,
  update,
  delete,
}
```

### 4.2 数据同步实现

```dart
// lib/core/sync/data_sync_service_impl.dart
import 'dart:async';
import 'dart:convert';

class DataSyncServiceImpl implements DataSyncService {
  final DatabaseService _databaseService;
  final ApiService _apiService;
  final Logger _logger;
  final Duration _syncInterval;
  
  final StreamController<SyncStatus> _syncStatusController = 
      StreamController<SyncStatus>.broadcast();
  final Map<String, List<DataChangeListener>> _changeListeners = {};
  
  Timer? _syncTimer;
  SyncStatus _currentStatus = SyncStatus.idle;
  bool _isOfflineMode = false;
  bool _isInitialized = false;
  
  DataSyncServiceImpl({
    required DatabaseService databaseService,
    required ApiService apiService,
    required Logger logger,
    Duration syncInterval = const Duration(minutes: 5),
  }) : _databaseService = databaseService,
       _apiService = apiService,
       _logger = logger,
       _syncInterval = syncInterval;
  
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    // 启动定期同步
    _syncTimer = Timer.periodic(_syncInterval, (_) {
      if (!_isOfflineMode && _currentStatus == SyncStatus.idle) {
        fullSync();
      }
    });
    
    _isInitialized = true;
    _logger.info('DataSyncService initialized with sync interval: $_syncInterval');
  }
  
  @override
  Future<void> dispose() async {
    if (!_isInitialized) return;
    
    _syncTimer?.cancel();
    await _syncStatusController.close();
    _changeListeners.clear();
    _isInitialized = false;
    
    _logger.info('DataSyncService disposed');
  }
  
  @override
  Stream<SyncStatus> get syncStatusStream => _syncStatusController.stream;
  
  @override
  Future<SyncStatus> getSyncStatus() async {
    return _currentStatus;
  }
  
  @override
  bool get isOfflineMode => _isOfflineMode;
  
  @override
  Future<void> enableOfflineMode() async {
    _isOfflineMode = true;
    _logger.info('Offline mode enabled');
  }
  
  @override
  Future<void> disableOfflineMode() async {
    _isOfflineMode = false;
    _logger.info('Offline mode disabled');
    
    // 立即执行同步
    if (_currentStatus == SyncStatus.idle) {
      await fullSync();
    }
  }
  
  @override
  Future<SyncResult> syncUp() async {
    if (_isOfflineMode) {
      return SyncResult.failure('Cannot sync in offline mode');
    }
    
    _updateStatus(SyncStatus.syncing);
    
    try {
      // 获取本地待上传的数据
      final pendingUploads = await _getPendingUploads();
      int uploadedCount = 0;
      
      for (final item in pendingUploads) {
        try {
          await _uploadItem(item);
          await _markAsUploaded(item);
          uploadedCount++;
        } catch (e) {
          _logger.error('Failed to upload item: ${item.id}', e);
        }
      }
      
      _updateStatus(SyncStatus.success);
      
      final result = SyncResult.success(uploadedCount: uploadedCount);
      _logger.info('Sync up completed: uploaded $uploadedCount items');
      
      return result;
    } catch (e, stackTrace) {
      _logger.error('Sync up failed', e, stackTrace);
      _updateStatus(SyncStatus.error);
      return SyncResult.failure(e.toString());
    }
  }
  
  @override
  Future<SyncResult> syncDown() async {
    if (_isOfflineMode) {
      return SyncResult.failure('Cannot sync in offline mode');
    }
    
    _updateStatus(SyncStatus.syncing);
    
    try {
      // 获取远程数据的最后同步时间戳
      final lastSyncTimestamp = await _getLastSyncTimestamp();
      
      // 下载远程更新
      final remoteUpdates = await _downloadUpdates(lastSyncTimestamp);
      int downloadedCount = 0;
      final conflicts = <SyncConflict>[];
      
      for (final update in remoteUpdates) {
        try {
          final conflict = await _applyRemoteUpdate(update);
          if (conflict != null) {
            conflicts.add(conflict);
          } else {
            downloadedCount++;
          }
        } catch (e) {
          _logger.error('Failed to apply remote update: ${update.id}', e);
        }
      }
      
      // 更新最后同步时间戳
      await _updateLastSyncTimestamp(DateTime.now());
      
      if (conflicts.isNotEmpty) {
        _updateStatus(SyncStatus.conflict);
      } else {
        _updateStatus(SyncStatus.success);
      }
      
      final result = SyncResult.success(
        downloadedCount: downloadedCount,
        conflictCount: conflicts.length,
        conflicts: conflicts,
      );
      
      _logger.info('Sync down completed: downloaded $downloadedCount items, ${conflicts.length} conflicts');
      
      return result;
    } catch (e, stackTrace) {
      _logger.error('Sync down failed', e, stackTrace);
      _updateStatus(SyncStatus.error);
      return SyncResult.failure(e.toString());
    }
  }
  
  @override
  Future<SyncResult> fullSync() async {
    if (_isOfflineMode) {
      return SyncResult.failure('Cannot sync in offline mode');
    }
    
    _updateStatus(SyncStatus.syncing);
    
    try {
      // 先上传本地更改
      final uploadResult = await syncUp();
      
      // 再下载远程更新
      final downloadResult = await syncDown();
      
      final combinedResult = SyncResult.success(
        uploadedCount: uploadResult.uploadedCount,
        downloadedCount: downloadResult.downloadedCount,
        conflictCount: downloadResult.conflictCount,
        conflicts: downloadResult.conflicts,
      );
      
      _logger.info('Full sync completed');
      
      return combinedResult;
    } catch (e, stackTrace) {
      _logger.error('Full sync failed', e, stackTrace);
      _updateStatus(SyncStatus.error);
      return SyncResult.failure(e.toString());
    }
  }
  
  @override
  Future<SyncResult> resolveConflicts(List<SyncConflict> conflicts) async {
    _updateStatus(SyncStatus.syncing);
    
    try {
      int resolvedCount = 0;
      
      for (final conflict in conflicts) {
        try {
          await _resolveConflict(conflict);
          resolvedCount++;
        } catch (e) {
          _logger.error('Failed to resolve conflict for ${conflict.entityId}', e);
        }
      }
      
      _updateStatus(SyncStatus.success);
      
      final result = SyncResult.success(downloadedCount: resolvedCount);
      _logger.info('Resolved $resolvedCount conflicts');
      
      return result;
    } catch (e, stackTrace) {
      _logger.error('Conflict resolution failed', e, stackTrace);
      _updateStatus(SyncStatus.error);
      return SyncResult.failure(e.toString());
    }
  }
  
  @override
  void addChangeListener(String entityType, DataChangeListener listener) {
    _changeListeners.putIfAbsent(entityType, () => []).add(listener);
  }
  
  @override
  void removeChangeListener(String entityType, DataChangeListener listener) {
    _changeListeners[entityType]?.remove(listener);
  }
  
  void _updateStatus(SyncStatus status) {
    _currentStatus = status;
    _syncStatusController.add(status);
  }
  
  void _notifyDataChange(DataChangeEvent event) {
    final listeners = _changeListeners[event.entityType];
    if (listeners != null) {
      for (final listener in listeners) {
        try {
          listener(event);
        } catch (e) {
          _logger.error('Error in data change listener', e);
        }
      }
    }
  }
  
  Future<List<PendingUploadItem>> _getPendingUploads() async {
    // 从数据库获取待上传的项目
    final result = await _databaseService.query(
      'pending_uploads',
      where: 'uploaded = ?',
      whereArgs: [0],
    );
    
    return result.data.map((item) => PendingUploadItem.fromJson(item)).toList();
  }
  
  Future<void> _uploadItem(PendingUploadItem item) async {
    // 上传到远程服务器
    await _apiService.post('/sync/upload', data: item.toJson());
  }
  
  Future<void> _markAsUploaded(PendingUploadItem item) async {
    await _databaseService.update(
      'pending_uploads',
      {'uploaded': 1, 'uploaded_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [item.id],
    );
  }
  
  Future<DateTime?> _getLastSyncTimestamp() async {
    final result = await _databaseService.query(
      'sync_metadata',
      where: 'key = ?',
      whereArgs: ['last_sync_timestamp'],
    );
    
    if (result.data.isNotEmpty) {
      return DateTime.parse(result.data.first['value'] as String);
    }
    
    return null;
  }
  
  Future<List<RemoteUpdateItem>> _downloadUpdates(DateTime? since) async {
    final params = since != null ? {'since': since.toIso8601String()} : <String, dynamic>{};
    final response = await _apiService.get('/sync/updates', queryParameters: params);
    
    return (response.data as List)
        .map((item) => RemoteUpdateItem.fromJson(item))
        .toList();
  }
  
  Future<SyncConflict?> _applyRemoteUpdate(RemoteUpdateItem update) async {
    // 检查本地是否有冲突的更改
    final localItem = await _getLocalItem(update.entityType, update.entityId);
    
    if (localItem != null && 
        localItem.modifiedAt.isAfter(update.modifiedAt) &&
        !_isDataEqual(localItem.data, update.data)) {
      // 发现冲突
      return SyncConflict(
        entityId: update.entityId,
        entityType: update.entityType,
        localData: localItem.data,
        remoteData: update.data,
        localModifiedAt: localItem.modifiedAt,
        remoteModifiedAt: update.modifiedAt,
        type: ConflictType.update,
      );
    }
    
    // 应用远程更新
    await _applyUpdate(update);
    
    // 通知数据变更
    _notifyDataChange(DataChangeEvent(
      entityId: update.entityId,
      entityType: update.entityType,
      type: DataChangeType.update,
      newData: update.data,
      timestamp: DateTime.now(),
    ));
    
    return null;
  }
  
  Future<void> _updateLastSyncTimestamp(DateTime timestamp) async {
    await _databaseService.insertOrUpdate(
      'sync_metadata',
      {
        'key': 'last_sync_timestamp',
        'value': timestamp.toIso8601String(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }
  
  Future<LocalItem?> _getLocalItem(String entityType, String entityId) async {
    final result = await _databaseService.query(
      entityType,
      where: 'id = ?',
      whereArgs: [entityId],
    );
    
    if (result.data.isNotEmpty) {
      return LocalItem.fromJson(result.data.first);
    }
    
    return null;
  }
  
  bool _isDataEqual(Map<String, dynamic> data1, Map<String, dynamic> data2) {
    return jsonEncode(data1) == jsonEncode(data2);
  }
  
  Future<void> _applyUpdate(RemoteUpdateItem update) async {
    await _databaseService.insertOrUpdate(
      update.entityType,
      update.data,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }
  
  Future<void> _resolveConflict(SyncConflict conflict) async {
    // 简单的冲突解决策略：使用远程数据
    await _databaseService.update(
      conflict.entityType,
      conflict.remoteData,
      where: 'id = ?',
      whereArgs: [conflict.entityId],
    );
    
    _notifyDataChange(DataChangeEvent(
      entityId: conflict.entityId,
      entityType: conflict.entityType,
      type: DataChangeType.update,
      oldData: conflict.localData,
      newData: conflict.remoteData,
      timestamp: DateTime.now(),
    ));
  }
}

// 辅助类
class PendingUploadItem {
  final String id;
  final String entityType;
  final String entityId;
  final Map<String, dynamic> data;
  final DateTime createdAt;
  
  const PendingUploadItem({
    required this.id,
    required this.entityType,
    required this.entityId,
    required this.data,
    required this.createdAt,
  });
  
  factory PendingUploadItem.fromJson(Map<String, dynamic> json) {
    return PendingUploadItem(
      id: json['id'] as String,
      entityType: json['entity_type'] as String,
      entityId: json['entity_id'] as String,
      data: jsonDecode(json['data'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'entity_type': entityType,
      'entity_id': entityId,
      'data': jsonEncode(data),
      'created_at': createdAt.toIso8601String(),
    };
  }
}

class RemoteUpdateItem {
  final String entityId;
  final String entityType;
  final Map<String, dynamic> data;
  final DateTime modifiedAt;
  
  const RemoteUpdateItem({
    required this.entityId,
    required this.entityType,
    required this.data,
    required this.modifiedAt,
  });
  
  factory RemoteUpdateItem.fromJson(Map<String, dynamic> json) {
    return RemoteUpdateItem(
      entityId: json['entity_id'] as String,
      entityType: json['entity_type'] as String,
      data: json['data'] as Map<String, dynamic>,
      modifiedAt: DateTime.parse(json['modified_at'] as String),
    );
  }
}

class LocalItem {
  final String id;
  final Map<String, dynamic> data;
  final DateTime modifiedAt;
  
  const LocalItem({
    required this.id,
    required this.data,
    required this.modifiedAt,
  });
  
  factory LocalItem.fromJson(Map<String, dynamic> json) {
    return LocalItem(
      id: json['id'] as String,
      data: json,
      modifiedAt: DateTime.parse(json['modified_at'] as String),
    );
  }
}
```

## 5. 数据持久化服务集成

### 5.1 数据持久化管理器

```dart
// lib/core/persistence/data_persistence_manager.dart
class DataPersistenceManager {
  final DatabaseService _databaseService;
  final FileStorageService _fileStorageService;
  final CacheManager _cacheManager;
  final DataSyncService _dataSyncService;
  final Logger _logger;
  
  bool _isInitialized = false;
  
  DataPersistenceManager({
    required DatabaseService databaseService,
    required FileStorageService fileStorageService,
    required CacheManager cacheManager,
    required DataSyncService dataSyncService,
    required Logger logger,
  }) : _databaseService = databaseService,
       _fileStorageService = fileStorageService,
       _cacheManager = cacheManager,
       _dataSyncService = dataSyncService,
       _logger = logger;
  
  Future<void> initialize({
    required String databasePath,
    required String fileStoragePath,
    required String cachePath,
    int databaseVersion = 1,
    List<String> requiredTables = const [],
  }) async {
    if (_isInitialized) return;
    
    try {
      // 初始化数据库
      await _databaseService.initialize(
        databasePath: databasePath,
        version: databaseVersion,
      );
      
      // 创建必需的表
      for (final table in requiredTables) {
        await _createTableIfNotExists(table);
      }
      
      // 初始化文件存储
      await _fileStorageService.initialize(
        basePath: fileStoragePath,
      );
      
      // 初始化缓存管理器
      await _cacheManager.initialize(
        cacheDirectory: cachePath,
      );
      
      // 初始化数据同步服务
      await _dataSyncService.initialize();
      
      _isInitialized = true;
      _logger.info('DataPersistenceManager initialized successfully');
    } catch (e, stackTrace) {
      _logger.error('Failed to initialize DataPersistenceManager', e, stackTrace);
      rethrow;
    }
  }
  
  Future<void> dispose() async {
    if (!_isInitialized) return;
    
    try {
      await _dataSyncService.dispose();
      await _cacheManager.dispose();
      await _fileStorageService.dispose();
      await _databaseService.close();
      
      _isInitialized = false;
      _logger.info('DataPersistenceManager disposed');
    } catch (e, stackTrace) {
      _logger.error('Error disposing DataPersistenceManager', e, stackTrace);
    }
  }
  
  // 数据库操作
  DatabaseService get database {
    _ensureInitialized();
    return _databaseService;
  }
  
  // 文件存储操作
  FileStorageService get fileStorage {
    _ensureInitialized();
    return _fileStorageService;
  }
  
  // 缓存操作
  CacheManager get cache {
    _ensureInitialized();
    return _cacheManager;
  }
  
  // 数据同步操作
  DataSyncService get sync {
    _ensureInitialized();
    return _dataSyncService;
  }
  
  // 综合数据操作：先查缓存，再查数据库
  Future<T?> getData<T>(
    String key, {
    String? table,
    String? where,
    List<dynamic>? whereArgs,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    _ensureInitialized();
    
    // 先从缓存获取
    T? cachedData = await _cacheManager.getWithFallback<T>(key);
    if (cachedData != null) {
      return cachedData;
    }
    
    // 从数据库获取
    if (table != null && fromJson != null) {
      final result = await _databaseService.query(
        table,
        where: where,
        whereArgs: whereArgs,
      );
      
      if (result.data.isNotEmpty) {
        final data = fromJson(result.data.first);
        
        // 写入缓存
        await _cacheManager.setMultiLevel<T>(key, data);
        
        return data;
      }
    }
    
    return null;
  }
  
  // 综合数据保存：同时保存到数据库和缓存
  Future<void> saveData<T>(
    String key,
    T data, {
    String? table,
    Map<String, dynamic> Function(T)? toJson,
    Duration? cacheTtl,
  }) async {
    _ensureInitialized();
    
    // 保存到缓存
    await _cacheManager.setMultiLevel<T>(key, data, memoryTtl: cacheTtl);
    
    // 保存到数据库
    if (table != null && toJson != null) {
      await _databaseService.insert(table, toJson(data));
    }
  }
  
  // 删除数据：同时从数据库和缓存删除
  Future<void> deleteData(
    String key, {
    String? table,
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    _ensureInitialized();
    
    // 从缓存删除
    await _cacheManager.delete(key);
    
    // 从数据库删除
    if (table != null) {
      await _databaseService.delete(
        table,
        where: where,
        whereArgs: whereArgs,
      );
    }
  }
  
  // 获取存储统计信息
  Future<Map<String, dynamic>> getStorageStats() async {
    _ensureInitialized();
    
    final cacheStats = await _cacheManager.getAllStats();
    final fileStats = await _fileStorageService.getStorageStats();
    final syncStatus = await _dataSyncService.getSyncStatus();
    
    return {
      'cache': cacheStats,
      'file_storage': fileStats,
      'sync_status': syncStatus.toString(),
      'database_path': await _databaseService.getDatabasePath(),
    };
  }
  
  // 清理存储
  Future<void> cleanupStorage({
    bool clearCache = false,
    bool clearTempFiles = true,
    bool compactDatabase = false,
  }) async {
    _ensureInitialized();
    
    if (clearCache) {
      await _cacheManager.clear();
      _logger.info('Cache cleared');
    }
    
    if (clearTempFiles) {
      await _fileStorageService.clearTempFiles();
      _logger.info('Temporary files cleared');
    }
    
    if (compactDatabase) {
      await _databaseService.vacuum();
      _logger.info('Database compacted');
    }
  }
  
  Future<void> _createTableIfNotExists(String tableName) async {
    // 根据表名创建对应的表结构
    switch (tableName) {
      case 'users':
        await _databaseService.execute('''
          CREATE TABLE IF NOT EXISTS users (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            avatar_url TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            is_active INTEGER DEFAULT 1
          )
        ''');
        break;
      case 'sync_metadata':
        await _databaseService.execute('''
          CREATE TABLE IF NOT EXISTS sync_metadata (
            key TEXT PRIMARY KEY,
            value TEXT NOT NULL,
            updated_at TEXT NOT NULL
          )
        ''');
        break;
      case 'pending_uploads':
        await _databaseService.execute('''
          CREATE TABLE IF NOT EXISTS pending_uploads (
            id TEXT PRIMARY KEY,
            entity_type TEXT NOT NULL,
            entity_id TEXT NOT NULL,
            data TEXT NOT NULL,
            created_at TEXT NOT NULL,
            uploaded INTEGER DEFAULT 0,
            uploaded_at TEXT
          )
        ''');
        break;
      default:
        _logger.warning('Unknown table: $tableName');
    }
  }
  
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('DataPersistenceManager not initialized. Call initialize() first.');
    }
  }
}
```

### 5.2 依赖注入配置

```dart
// lib/core/di/persistence_module.dart
import 'package:get_it/get_it.dart';

class PersistenceModule {
  static Future<void> register(GetIt getIt) async {
    // 注册Logger
    if (!getIt.isRegistered<Logger>()) {
      getIt.registerLazySingleton<Logger>(() => Logger('DataPersistence'));
    }
    
    // 注册数据库服务
    getIt.registerLazySingleton<DatabaseService>(
      () => SqliteDatabaseService(
        logger: getIt<Logger>(),
        migrationManager: getIt<DatabaseMigrationManager>(),
      ),
    );
    
    // 注册数据库迁移管理器
    getIt.registerLazySingleton<DatabaseMigrationManager>(
      () => DatabaseMigrationManager(
        logger: getIt<Logger>(),
      ),
    );
    
    // 注册文件存储服务
    getIt.registerLazySingleton<FileStorageService>(
      () => FileStorageServiceImpl(
        logger: getIt<Logger>(),
      ),
    );
    
    // 注册缓存服务
    getIt.registerLazySingleton<CacheManager>(
      () => CacheManager(
        logger: getIt<Logger>(),
      ),
    );
    
    // 注册数据同步服务
    getIt.registerLazySingleton<DataSyncService>(
      () => DataSyncServiceImpl(
        databaseService: getIt<DatabaseService>(),
        apiService: getIt<ApiService>(),
        logger: getIt<Logger>(),
      ),
    );
    
    // 注册数据持久化管理器
    getIt.registerLazySingleton<DataPersistenceManager>(
      () => DataPersistenceManager(
        databaseService: getIt<DatabaseService>(),
        fileStorageService: getIt<FileStorageService>(),
        cacheManager: getIt<CacheManager>(),
        dataSyncService: getIt<DataSyncService>(),
        logger: getIt<Logger>(),
      ),
    );
  }
}
```

## 6. 使用示例

### 6.1 应用初始化

```dart
// lib/main.dart
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:get_it/get_it.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 注册依赖
  await PersistenceModule.register(GetIt.instance);
  
  // 初始化数据持久化
  await _initializeDataPersistence();
  
  runApp(MyApp());
}

Future<void> _initializeDataPersistence() async {
  final appDir = await getApplicationDocumentsDirectory();
  final persistenceManager = GetIt.instance<DataPersistenceManager>();
  
  await persistenceManager.initialize(
    databasePath: '${appDir.path}/app.db',
    fileStoragePath: '${appDir.path}/files',
    cachePath: '${appDir.path}/cache',
    databaseVersion: 1,
    requiredTables: ['users', 'sync_metadata', 'pending_uploads'],
  );
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Data Persistence Demo',
      home: DataPersistenceDemo(),
    );
  }
}
```

### 6.2 数据操作示例

```dart
// lib/pages/data_persistence_demo.dart
class DataPersistenceDemo extends StatefulWidget {
  @override
  _DataPersistenceDemoState createState() => _DataPersistenceDemoState();
}

class _DataPersistenceDemoState extends State<DataPersistenceDemo> {
  final DataPersistenceManager _persistenceManager = GetIt.instance<DataPersistenceManager>();
  
  String _status = 'Ready';
  Map<String, dynamic>? _storageStats;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Data Persistence Demo'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text('Status: $_status'),
            SizedBox(height: 16),
            
            // 数据库操作
            _buildSection(
              'Database Operations',
              [
                ElevatedButton(
                  onPressed: _testDatabaseOperations,
                  child: Text('Test Database'),
                ),
              ],
            ),
            
            // 文件存储操作
            _buildSection(
              'File Storage Operations',
              [
                ElevatedButton(
                  onPressed: _testFileOperations,
                  child: Text('Test File Storage'),
                ),
              ],
            ),
            
            // 缓存操作
            _buildSection(
              'Cache Operations',
              [
                ElevatedButton(
                  onPressed: _testCacheOperations,
                  child: Text('Test Cache'),
                ),
              ],
            ),
            
            // 数据同步操作
            _buildSection(
              'Data Sync Operations',
              [
                ElevatedButton(
                  onPressed: _testSyncOperations,
                  child: Text('Test Sync'),
                ),
              ],
            ),
            
            // 综合操作
            _buildSection(
              'Integrated Operations',
              [
                ElevatedButton(
                  onPressed: _testIntegratedOperations,
                  child: Text('Test Integrated'),
                ),
                ElevatedButton(
                  onPressed: _getStorageStats,
                  child: Text('Get Storage Stats'),
                ),
                ElevatedButton(
                  onPressed: _cleanupStorage,
                  child: Text('Cleanup Storage'),
                ),
              ],
            ),
            
            // 存储统计信息
            if (_storageStats != null) ...
              _buildStorageStats(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSection(String title, List<Widget> children) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            ...children,
          ],
        ),
      ),
    );
  }
  
  Widget _buildStorageStats() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Storage Statistics',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(jsonEncode(_storageStats)),
          ],
        ),
      ),
    );
  }
  
  Future<void> _testDatabaseOperations() async {
    setState(() => _status = 'Testing database operations...');
    
    try {
      // 插入用户数据
      final user = {
        'id': 'user_1',
        'name': 'John Doe',
        'email': '<EMAIL>',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };
      
      await _persistenceManager.database.insert('users', user);
      
      // 查询用户数据
      final result = await _persistenceManager.database.query(
        'users',
        where: 'id = ?',
        whereArgs: ['user_1'],
      );
      
      setState(() => _status = 'Database test completed. Found ${result.data.length} users.');
    } catch (e) {
      setState(() => _status = 'Database test failed: $e');
    }
  }
  
  Future<void> _testFileOperations() async {
    setState(() => _status = 'Testing file operations...');
    
    try {
      // 保存文件
      final content = 'Hello, World! ${DateTime.now()}';
      await _persistenceManager.fileStorage.saveFile(
        'test.txt',
        content.codeUnits,
        subDirectory: 'test',
      );
      
      // 读取文件
      final readContent = await _persistenceManager.fileStorage.readFile(
        'test.txt',
        subDirectory: 'test',
      );
      
      final readString = String.fromCharCodes(readContent);
      
      setState(() => _status = 'File test completed. Content: $readString');
    } catch (e) {
      setState(() => _status = 'File test failed: $e');
    }
  }
  
  Future<void> _testCacheOperations() async {
    setState(() => _status = 'Testing cache operations...');
    
    try {
      // 设置缓存
      await _persistenceManager.cache.setMultiLevel(
        'test_key',
        {'message': 'Hello Cache!', 'timestamp': DateTime.now().toIso8601String()},
      );
      
      // 获取缓存
      final cachedData = await _persistenceManager.cache.getWithFallback<Map<String, dynamic>>('test_key');
      
      setState(() => _status = 'Cache test completed. Data: $cachedData');
    } catch (e) {
      setState(() => _status = 'Cache test failed: $e');
    }
  }
  
  Future<void> _testSyncOperations() async {
    setState(() => _status = 'Testing sync operations...');
    
    try {
      // 获取同步状态
      final syncStatus = await _persistenceManager.sync.getSyncStatus();
      
      setState(() => _status = 'Sync test completed. Status: $syncStatus');
    } catch (e) {
      setState(() => _status = 'Sync test failed: $e');
    }
  }
  
  Future<void> _testIntegratedOperations() async {
    setState(() => _status = 'Testing integrated operations...');
    
    try {
      // 综合数据保存
      final userData = {
        'id': 'integrated_user',
        'name': 'Integrated User',
        'email': '<EMAIL>',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };
      
      await _persistenceManager.saveData<Map<String, dynamic>>(
        'integrated_user',
        userData,
        table: 'users',
        toJson: (data) => data,
        cacheTtl: Duration(minutes: 30),
      );
      
      // 综合数据获取
      final retrievedData = await _persistenceManager.getData<Map<String, dynamic>>(
        'integrated_user',
        table: 'users',
        where: 'id = ?',
        whereArgs: ['integrated_user'],
        fromJson: (json) => json,
      );
      
      setState(() => _status = 'Integrated test completed. Data: $retrievedData');
    } catch (e) {
      setState(() => _status = 'Integrated test failed: $e');
    }
  }
  
  Future<void> _getStorageStats() async {
    setState(() => _status = 'Getting storage statistics...');
    
    try {
      final stats = await _persistenceManager.getStorageStats();
      
      setState(() {
        _storageStats = stats;
        _status = 'Storage stats retrieved successfully';
      });
    } catch (e) {
      setState(() => _status = 'Failed to get storage stats: $e');
    }
  }
  
  Future<void> _cleanupStorage() async {
    setState(() => _status = 'Cleaning up storage...');
    
    try {
      await _persistenceManager.cleanupStorage(
        clearCache: true,
        clearTempFiles: true,
        compactDatabase: true,
      );
      
      setState(() => _status = 'Storage cleanup completed');
    } catch (e) {
      setState(() => _status = 'Storage cleanup failed: $e');
    }
  }
}
```

## 7. 最佳实践和建议

### 7.1 性能优化

1. **数据库优化**
   - 合理使用索引
   - 避免在主线程执行长时间的数据库操作
   - 使用事务批量操作
   - 定期执行 VACUUM 压缩数据库

2. **缓存策略**
   - 根据数据访问频率选择合适的缓存类型
   - 设置合理的 TTL 避免内存泄漏
   - 使用多级缓存提高命中率
   - 定期清理过期缓存

3. **文件存储优化**
   - 大文件使用流式读写
   - 定期清理临时文件
   - 使用文件压缩减少存储空间
   - 实现文件分片上传

### 7.2 数据安全

1. **数据加密**
   - 敏感数据使用加密存储
   - 使用安全的密钥管理
   - 定期轮换加密密钥

2. **访问控制**
   - 实现细粒度的权限控制
   - 记录数据访问日志
   - 定期审计数据访问

### 7.3 错误处理

1. **异常处理**
   - 捕获并处理所有可能的异常
   - 提供有意义的错误信息
   - 实现重试机制

2. **数据一致性**
   - 使用事务保证数据一致性
   - 实现数据校验机制
   - 提供数据恢复功能

### 7.4 监控和调试

1. **性能监控**
   - 监控数据库查询性能
   - 跟踪缓存命中率
   - 监控存储空间使用

2. **日志记录**
   - 记录关键操作日志
   - 实现结构化日志
   - 提供日志查询功能

## 8. 总结

本文档提供了一个完整的 Flutter 企业级应用数据持久化和缓存系统实现，包括：

1. **数据库抽象层**：提供统一的数据库操作接口，支持 SQLite 实现
2. **文件存储系统**：支持文件的保存、读取、管理和统计
3. **多级缓存系统**：包括内存缓存、持久化缓存和 LRU 缓存
4. **数据同步服务**：支持离线数据同步和冲突解决
5. **集成管理器**：统一管理所有持久化服务
6. **完整的使用示例**：展示如何在实际应用中使用这些服务

该实现遵循 Clean Architecture 原则，具有良好的可扩展性和可维护性，适合企业级应用的数据持久化需求。通过合理的缓存策略和数据同步机制，可以显著提升应用的性能和用户体验。