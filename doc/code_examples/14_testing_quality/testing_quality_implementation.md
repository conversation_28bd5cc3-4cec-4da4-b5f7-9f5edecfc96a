# Flutter 企业级应用测试和质量保证实现

本文档提供了 Flutter 企业级应用中测试和质量保证的完整实现示例，包括单元测试、集成测试、UI 测试、性能测试、代码质量检查和持续集成等功能。

## 1. 测试基础架构

### 1.1 测试配置和工具

```

## 4. 集成测试

### 4.1 API 集成测试

```dart
// test/integration/api_integration_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../helpers/test_helpers.dart';

class ApiTestServer {
  static const String baseUrl = 'http://localhost:3000';
  
  static Future<void> startServer() async {
    // 启动测试服务器的逻辑
    // 可以使用 Docker 或本地服务器
  }
  
  static Future<void> stopServer() async {
    // 停止测试服务器的逻辑
  }
  
  static Future<void> resetDatabase() async {
    // 重置测试数据库
    await http.post(
      Uri.parse('$baseUrl/test/reset'),
      headers: {'Content-Type': 'application/json'},
    );
  }
  
  static Future<void> seedTestData() async {
    // 插入测试数据
    final testUsers = TestDataGenerator.generateUsers(5);
    await http.post(
      Uri.parse('$baseUrl/test/seed'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({'users': testUsers}),
    );
  }
}

void main() {
  group('API Integration Tests', () {
    late ApiService apiService;
    
    setUpAll(() async {
      await ApiTestServer.startServer();
      await TestHelpers.setupTestEnvironment();
      
      apiService = ApiServiceImpl(
        baseUrl: ApiTestServer.baseUrl,
        httpClient: http.Client(),
      );
    });
    
    tearDownAll(() async {
      await ApiTestServer.stopServer();
      await TestHelpers.tearDownTestEnvironment();
    });
    
    setUp(() async {
      await ApiTestServer.resetDatabase();
      await ApiTestServer.seedTestData();
    });
    
    group('Authentication API', () {
      test('should login with valid credentials', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        
        // Act
        final response = await apiService.post(
          '/auth/login',
          data: {
            'email': email,
            'password': password,
          },
        );
        
        // Assert
        expect(response.isSuccess, true);
        expect(response.data['token'], isNotNull);
        expect(response.data['user']['email'], email);
      });
      
      test('should reject invalid credentials', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'wrong_password';
        
        // Act & Assert
        expect(
          () => apiService.post(
            '/auth/login',
            data: {
              'email': email,
              'password': password,
            },
          ),
          throwsA(isA<ApiException>()),
        );
      });
      
      test('should refresh token successfully', () async {
        // Arrange
        final loginResponse = await apiService.post(
          '/auth/login',
          data: {
            'email': '<EMAIL>',
            'password': 'password123',
          },
        );
        
        final refreshToken = loginResponse.data['refresh_token'];
        
        // Act
        final response = await apiService.post(
          '/auth/refresh',
          data: {'refresh_token': refreshToken},
        );
        
        // Assert
        expect(response.isSuccess, true);
        expect(response.data['token'], isNotNull);
        expect(response.data['token'], isNot(equals(loginResponse.data['token'])));
      });
    });
    
    group('Users API', () {
      test('should get users list', () async {
        // Act
        final response = await apiService.get('/users');
        
        // Assert
        expect(response.isSuccess, true);
        expect(response.data, isList);
        expect(response.data.length, greaterThan(0));
        
        final firstUser = response.data.first;
        expect(firstUser['id'], isNotNull);
        expect(firstUser['name'], isNotNull);
        expect(firstUser['email'], isNotNull);
      });
      
      test('should create new user', () async {
        // Arrange
        final userData = {
          'name': 'New User',
          'email': '<EMAIL>',
        };
        
        // Act
        final response = await apiService.post(
          '/users',
          data: userData,
        );
        
        // Assert
        expect(response.isSuccess, true);
        expect(response.data['id'], isNotNull);
        expect(response.data['name'], userData['name']);
        expect(response.data['email'], userData['email']);
        expect(response.data['created_at'], isNotNull);
      });
      
      test('should update existing user', () async {
        // Arrange
        final createResponse = await apiService.post(
          '/users',
          data: {
            'name': 'Original Name',
            'email': '<EMAIL>',
          },
        );
        
        final userId = createResponse.data['id'];
        final updateData = {
          'name': 'Updated Name',
          'email': '<EMAIL>',
        };
        
        // Act
        final response = await apiService.put(
          '/users/$userId',
          data: updateData,
        );
        
        // Assert
        expect(response.isSuccess, true);
        expect(response.data['id'], userId);
        expect(response.data['name'], updateData['name']);
        expect(response.data['email'], updateData['email']);
        expect(response.data['updated_at'], isNot(equals(response.data['created_at'])));
      });
      
      test('should delete user', () async {
        // Arrange
        final createResponse = await apiService.post(
          '/users',
          data: {
            'name': 'To Delete',
            'email': '<EMAIL>',
          },
        );
        
        final userId = createResponse.data['id'];
        
        // Act
        final response = await apiService.delete('/users/$userId');
        
        // Assert
        expect(response.isSuccess, true);
        
        // Verify user is deleted
        expect(
          () => apiService.get('/users/$userId'),
          throwsA(isA<ApiException>()),
        );
      });
    });
    
    group('Error Handling', () {
      test('should handle 404 errors', () async {
        // Act & Assert
        expect(
          () => apiService.get('/users/non_existent_id'),
          throwsA(predicate((e) => e is ApiException && e.statusCode == 404)),
        );
      });
      
      test('should handle validation errors', () async {
        // Act & Assert
        expect(
          () => apiService.post(
            '/users',
            data: {
              'name': '', // Invalid empty name
              'email': 'invalid-email', // Invalid email format
            },
          ),
          throwsA(predicate((e) => e is ApiException && e.statusCode == 422)),
        );
      });
      
      test('should handle rate limiting', () async {
        // Arrange - Make multiple rapid requests
        final futures = List.generate(
          20,
          (index) => apiService.get('/users'),
        );
        
        // Act & Assert
        expect(
          () => Future.wait(futures),
          throwsA(predicate((e) => e is ApiException && e.statusCode == 429)),
        );
      });
    });
  });
}
```

### 4.2 数据库集成测试

```dart
// test/integration/database_integration_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('Database Integration Tests', () {
    late DatabaseService databaseService;
    late String testDatabasePath;
    
    setUpAll(() {
      // 初始化 FFI
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    });
    
    setUp(() async {
      await TestHelpers.setupTestEnvironment();
      
      // 创建临时测试数据库
      testDatabasePath = 'test_database_${DateTime.now().millisecondsSinceEpoch}.db';
      databaseService = SqliteDatabaseService(
        databasePath: testDatabasePath,
      );
      
      await databaseService.initialize();
    });
    
    tearDown(() async {
      await databaseService.close();
      await TestHelpers.tearDownTestEnvironment();
      
      // 删除测试数据库文件
      try {
        await databaseFactory.deleteDatabase(testDatabasePath);
      } catch (e) {
        // 忽略删除错误
      }
    });
    
    group('Table Operations', () {
      test('should create table successfully', () async {
        // Act
        final result = await databaseService.createTable(
          'test_table',
          {
            'id': 'INTEGER PRIMARY KEY AUTOINCREMENT',
            'name': 'TEXT NOT NULL',
            'email': 'TEXT UNIQUE',
            'created_at': 'TEXT DEFAULT CURRENT_TIMESTAMP',
          },
        );
        
        // Assert
        expect(result.isSuccess, true);
        
        // Verify table exists
        final tables = await databaseService.query(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='test_table'",
        );
        expect(tables.data?.length, 1);
      });
      
      test('should drop table successfully', () async {
        // Arrange
        await databaseService.createTable(
          'temp_table',
          {'id': 'INTEGER PRIMARY KEY', 'data': 'TEXT'},
        );
        
        // Act
        final result = await databaseService.dropTable('temp_table');
        
        // Assert
        expect(result.isSuccess, true);
        
        // Verify table doesn't exist
        final tables = await databaseService.query(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='temp_table'",
        );
        expect(tables.data?.length, 0);
      });
    });
    
    group('CRUD Operations', () {
      setUp(() async {
        // 创建测试表
        await databaseService.createTable(
          'users',
          {
            'id': 'INTEGER PRIMARY KEY AUTOINCREMENT',
            'name': 'TEXT NOT NULL',
            'email': 'TEXT UNIQUE NOT NULL',
            'age': 'INTEGER',
            'created_at': 'TEXT DEFAULT CURRENT_TIMESTAMP',
          },
        );
      });
      
      test('should insert data successfully', () async {
        // Arrange
        final userData = {
          'name': 'Test User',
          'email': '<EMAIL>',
          'age': 25,
        };
        
        // Act
        final result = await databaseService.insert('users', userData);
        
        // Assert
        expect(result.isSuccess, true);
        expect(result.data, isA<int>());
        expect(result.data, greaterThan(0));
      });
      
      test('should query data successfully', () async {
        // Arrange
        final testUsers = TestDataGenerator.generateUsers(3);
        for (final user in testUsers) {
          await databaseService.insert('users', user);
        }
        
        // Act
        final result = await databaseService.query('users');
        
        // Assert
        expect(result.isSuccess, true);
        expect(result.data?.length, 3);
        
        final firstUser = result.data?.first;
        expect(firstUser?['id'], isNotNull);
        expect(firstUser?['name'], isNotNull);
        expect(firstUser?['email'], isNotNull);
      });
      
      test('should update data successfully', () async {
        // Arrange
        final userData = {
          'name': 'Original Name',
          'email': '<EMAIL>',
          'age': 25,
        };
        
        final insertResult = await databaseService.insert('users', userData);
        final userId = insertResult.data as int;
        
        // Act
        final updateResult = await databaseService.update(
          'users',
          {'name': 'Updated Name', 'age': 30},
          where: 'id = ?',
          whereArgs: [userId],
        );
        
        // Assert
        expect(updateResult.isSuccess, true);
        expect(updateResult.data, 1); // 1 row affected
        
        // Verify update
        final queryResult = await databaseService.query(
          'users',
          where: 'id = ?',
          whereArgs: [userId],
        );
        
        final updatedUser = queryResult.data?.first;
        expect(updatedUser?['name'], 'Updated Name');
        expect(updatedUser?['age'], 30);
        expect(updatedUser?['email'], '<EMAIL>'); // Unchanged
      });
      
      test('should delete data successfully', () async {
        // Arrange
        final userData = {
          'name': 'To Delete',
          'email': '<EMAIL>',
          'age': 25,
        };
        
        final insertResult = await databaseService.insert('users', userData);
        final userId = insertResult.data as int;
        
        // Act
        final deleteResult = await databaseService.delete(
          'users',
          where: 'id = ?',
          whereArgs: [userId],
        );
        
        // Assert
        expect(deleteResult.isSuccess, true);
        expect(deleteResult.data, 1); // 1 row affected
        
        // Verify deletion
        final queryResult = await databaseService.query(
          'users',
          where: 'id = ?',
          whereArgs: [userId],
        );
        
        expect(queryResult.data?.length, 0);
      });
    });
    
    group('Transaction Operations', () {
      setUp(() async {
        await databaseService.createTable(
          'accounts',
          {
            'id': 'INTEGER PRIMARY KEY AUTOINCREMENT',
            'name': 'TEXT NOT NULL',
            'balance': 'REAL NOT NULL DEFAULT 0',
          },
        );
      });
      
      test('should commit transaction successfully', () async {
        // Arrange
        await databaseService.insert('accounts', {'name': 'Account A', 'balance': 1000.0});
        await databaseService.insert('accounts', {'name': 'Account B', 'balance': 500.0});
        
        // Act
        final result = await databaseService.transaction((txn) async {
          // Transfer 200 from Account A to Account B
          await txn.update(
            'accounts',
            {'balance': 800.0},
            where: 'name = ?',
            whereArgs: ['Account A'],
          );
          
          await txn.update(
            'accounts',
            {'balance': 700.0},
            where: 'name = ?',
            whereArgs: ['Account B'],
          );
          
          return 'Transfer completed';
        });
        
        // Assert
        expect(result.isSuccess, true);
        expect(result.data, 'Transfer completed');
        
        // Verify balances
        final accountA = await databaseService.query(
          'accounts',
          where: 'name = ?',
          whereArgs: ['Account A'],
        );
        expect(accountA.data?.first['balance'], 800.0);
        
        final accountB = await databaseService.query(
          'accounts',
          where: 'name = ?',
          whereArgs: ['Account B'],
        );
        expect(accountB.data?.first['balance'], 700.0);
      });
      
      test('should rollback transaction on error', () async {
        // Arrange
        await databaseService.insert('accounts', {'name': 'Account A', 'balance': 1000.0});
        await databaseService.insert('accounts', {'name': 'Account B', 'balance': 500.0});
        
        // Act
        final result = await databaseService.transaction((txn) async {
          // Transfer 200 from Account A to Account B
          await txn.update(
            'accounts',
            {'balance': 800.0},
            where: 'name = ?',
            whereArgs: ['Account A'],
          );
          
          // Simulate error
          throw Exception('Transfer failed');
        });
        
        // Assert
        expect(result.isSuccess, false);
        expect(result.error, contains('Transfer failed'));
        
        // Verify balances are unchanged
        final accountA = await databaseService.query(
          'accounts',
          where: 'name = ?',
          whereArgs: ['Account A'],
        );
        expect(accountA.data?.first['balance'], 1000.0); // Original balance
        
        final accountB = await databaseService.query(
          'accounts',
          where: 'name = ?',
          whereArgs: ['Account B'],
        );
        expect(accountB.data?.first['balance'], 500.0); // Original balance
      });
    });
    
    group('Migration Tests', () {
      test('should run migrations successfully', () async {
        // Arrange
        final migrationManager = DatabaseMigrationManager(databaseService);
        
        final migrations = [
          MigrationScript(
            version: 1,
            description: 'Create users table',
            upScript: '''
              CREATE TABLE users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                email TEXT UNIQUE NOT NULL
              );
            ''',
            downScript: 'DROP TABLE users;',
          ),
          MigrationScript(
            version: 2,
            description: 'Add age column to users',
            upScript: 'ALTER TABLE users ADD COLUMN age INTEGER;',
            downScript: 'ALTER TABLE users DROP COLUMN age;',
          ),
        ];
        
        // Act
        final result = await migrationManager.runMigrations(migrations);
        
        // Assert
        expect(result.isSuccess, true);
        
        // Verify table structure
        final tableInfo = await databaseService.query(
          "PRAGMA table_info(users)",
        );
        
        final columns = tableInfo.data?.map((row) => row['name']).toList();
        expect(columns, contains('id'));
        expect(columns, contains('name'));
        expect(columns, contains('email'));
        expect(columns, contains('age'));
      });
    });
  });
}
```

## 5. UI 测试 (使用 Patrol)

### 5.1 端到端 UI 测试

```dart
// integration_test/app_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:patrol/patrol.dart';
import 'package:myapp/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('App E2E Tests', () {
    patrolTest(
      'complete user flow - login, create user, logout',
      ($) async {
        // 启动应用
        app.main();
        await $.pumpAndSettle();
        
        // 验证启动页面
        expect($(#loginPage), findsOneWidget);
        
        // 执行登录
        await $(#emailField).enterText('<EMAIL>');
        await $(#passwordField).enterText('password123');
        await $(#loginButton).tap();
        
        // 等待导航到主页
        await $.pumpAndSettle();
        expect($(#homePage), findsOneWidget);
        
        // 导航到用户列表
        await $(#userListTab).tap();
        await $.pumpAndSettle();
        
        // 验证用户列表页面
        expect($(#userListPage), findsOneWidget);
        expect($(UserCard), findsWidgets);
        
        // 创建新用户
        await $(FloatingActionButton).tap();
        await $.pumpAndSettle();
        
        expect($(#createUserPage), findsOneWidget);
        
        await $(#nameField).enterText('New Test User');
        await $(#emailField).enterText('<EMAIL>');
        await $(#submitButton).tap();
        
        // 等待创建完成并返回列表
        await $.pumpAndSettle();
        expect($(#userListPage), findsOneWidget);
        expect($('New Test User'), findsOneWidget);
        
        // 验证新用户出现在列表中
        await $.scrollUntilVisible(
          finder: $('New Test User'),
          view: $(ListView),
        );
        
        // 点击用户查看详情
        await $('New Test User').tap();
        await $.pumpAndSettle();
        
        expect($(#userDetailPage), findsOneWidget);
        expect($('New Test User'), findsOneWidget);
        expect($('<EMAIL>'), findsOneWidget);
        
        // 返回列表
        await $.native.pressBack();
        await $.pumpAndSettle();
        
        // 执行登出
        await $(#menuButton).tap();
        await $.pumpAndSettle();
        
        await $(#logoutButton).tap();
        await $.pumpAndSettle();
        
        // 验证返回登录页面
        expect($(#loginPage), findsOneWidget);
      },
    );
    
    patrolTest(
      'error handling - network error',
      ($) async {
        // 启动应用（模拟网络错误）
        app.main();
        await $.pumpAndSettle();
        
        // 尝试登录（应该失败）
        await $(#emailField).enterText('<EMAIL>');
        await $(#passwordField).enterText('password123');
        await $(#loginButton).tap();
        
        // 等待错误消息
        await $.pumpAndSettle();
        expect($('Network error'), findsOneWidget);
        expect($(#retryButton), findsOneWidget);
        
        // 点击重试
        await $(#retryButton).tap();
        await $.pumpAndSettle();
        
        // 验证重试逻辑
        expect($(CircularProgressIndicator), findsOneWidget);
      },
    );
    
    patrolTest(
      'form validation',
      ($) async {
        app.main();
        await $.pumpAndSettle();
        
        // 导航到创建用户页面（假设已登录）
        await _performLogin($);
        
        await $(#userListTab).tap();
        await $.pumpAndSettle();
        
        await $(FloatingActionButton).tap();
        await $.pumpAndSettle();
        
        // 测试空字段验证
        await $(#submitButton).tap();
        await $.pumpAndSettle();
        
        expect($('Name is required'), findsOneWidget);
        expect($('Email is required'), findsOneWidget);
        
        // 测试无效邮箱验证
        await $(#nameField).enterText('Test User');
        await $(#emailField).enterText('invalid-email');
        await $(#submitButton).tap();
        await $.pumpAndSettle();
        
        expect($('Please enter a valid email'), findsOneWidget);
        
        // 输入有效数据
        await $(#emailField).clear();
        await $(#emailField).enterText('<EMAIL>');
        await $(#submitButton).tap();
        
        // 验证成功创建
        await $.pumpAndSettle();
        expect($(#userListPage), findsOneWidget);
      },
    );
    
    patrolTest(
      'pull to refresh functionality',
      ($) async {
        app.main();
        await $.pumpAndSettle();
        
        await _performLogin($);
        
        await $(#userListTab).tap();
        await $.pumpAndSettle();
        
        // 执行下拉刷新
        await $.drag(
          from: $(ListView),
          to: const Offset(0, 300),
        );
        
        // 验证刷新指示器
        expect($(RefreshProgressIndicator), findsOneWidget);
        
        await $.pumpAndSettle();
        
        // 验证列表已刷新
        expect($(UserCard), findsWidgets);
      },
    );
    
    patrolTest(
      'dark mode toggle',
      ($) async {
        app.main();
        await $.pumpAndSettle();
        
        await _performLogin($);
        
        // 打开设置
        await $(#settingsTab).tap();
        await $.pumpAndSettle();
        
        // 切换暗色模式
        await $(#darkModeSwitch).tap();
        await $.pumpAndSettle();
        
        // 验证主题变化
        final appBar = $.tester.widget<AppBar>($(AppBar));
        expect(appBar.backgroundColor, isNot(equals(Colors.blue)));
        
        // 切换回亮色模式
        await $(#darkModeSwitch).tap();
        await $.pumpAndSettle();
        
        final lightAppBar = $.tester.widget<AppBar>($(AppBar));
        expect(lightAppBar.backgroundColor, equals(Colors.blue));
      },
    );
    
    patrolTest(
      'language switching',
      ($) async {
        app.main();
        await $.pumpAndSettle();
        
        await _performLogin($);
        
        // 打开设置
        await $(#settingsTab).tap();
        await $.pumpAndSettle();
        
        // 打开语言选择
        await $(#languageSelector).tap();
        await $.pumpAndSettle();
        
        // 选择中文
        await $('中文').tap();
        await $.pumpAndSettle();
        
        // 验证语言变化
        expect($('用户列表'), findsOneWidget);
        expect($('设置'), findsOneWidget);
        
        // 切换回英文
        await $(#languageSelector).tap();
        await $.pumpAndSettle();
        
        await $('English').tap();
        await $.pumpAndSettle();
        
        expect($('User List'), findsOneWidget);
        expect($('Settings'), findsOneWidget);
      },
    );
  });
}

// 辅助函数
Future<void> _performLogin(PatrolTester $) async {
  if ($(#loginPage).exists) {
    await $(#emailField).enterText('<EMAIL>');
    await $(#passwordField).enterText('password123');
    await $(#loginButton).tap();
    await $.pumpAndSettle();
  }
}
```

### 5.2 Golden 测试

```dart
// test/golden/widget_golden_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('Widget Golden Tests', () {
    testGoldens('UserCard golden test', (tester) async {
      final testUser = User.fromJson(TestHelpers.createTestUser());
      
      final builder = DeviceBuilder()
        ..overrideDevicesForAllScenarios(
          devices: [
            Device.phone,
            Device.iphone11,
            Device.tabletPortrait,
          ],
        )
        ..addScenario(
          widget: UserCard(user: testUser),
          name: 'default',
        )
        ..addScenario(
          widget: UserCard(
            user: testUser,
            elevation: 8.0,
            margin: EdgeInsets.all(16.0),
          ),
          name: 'elevated',
        )
        ..addScenario(
          widget: UserCard(user: null),
          name: 'loading',
        );
      
      await tester.pumpDeviceBuilder(builder);
      
      await screenMatchesGolden(tester, 'user_card');
    });
    
    testGoldens('CreateUserForm golden test', (tester) async {
      final builder = DeviceBuilder()
        ..overrideDevicesForAllScenarios(
          devices: [Device.phone, Device.tabletPortrait],
        )
        ..addScenario(
          widget: CreateUserForm(),
          name: 'empty',
        )
        ..addScenario(
          widget: CreateUserForm(isLoading: true),
          name: 'loading',
        )
        ..addScenario(
          widget: CreateUserForm(
            initialName: 'Test User',
            initialEmail: '<EMAIL>',
          ),
          name: 'filled',
        );
      
      await tester.pumpDeviceBuilder(builder);
      
      await screenMatchesGolden(tester, 'create_user_form');
    });
    
    testGoldens('UserListPage golden test', (tester) async {
      final testUsers = TestDataGenerator.generateUsers(5)
          .map((data) => User.fromJson(data))
          .toList();
      
      final builder = DeviceBuilder()
        ..overrideDevicesForAllScenarios(
          devices: [Device.phone, Device.tabletPortrait],
        )
        ..addScenario(
          widget: BlocProvider(
            create: (_) => MockUserBloc()..add(LoadUsers()),
            child: UserListPage(),
          ),
          name: 'loading',
        )
        ..addScenario(
          widget: BlocProvider(
            create: (_) => MockUserBloc()
              ..emit(UserLoaded(testUsers)),
            child: UserListPage(),
          ),
          name: 'loaded',
        )
        ..addScenario(
          widget: BlocProvider(
            create: (_) => MockUserBloc()
              ..emit(UserError('Failed to load users')),
            child: UserListPage(),
          ),
          name: 'error',
        );
      
      await tester.pumpDeviceBuilder(builder);
      
      await screenMatchesGolden(tester, 'user_list_page');
    });
    
    testGoldens('Theme variations', (tester) async {
      final testUser = User.fromJson(TestHelpers.createTestUser());
      
      final builder = DeviceBuilder()
        ..overrideDevicesForAllScenarios(
          devices: [Device.phone],
        )
        ..addScenario(
          widget: MaterialApp(
            theme: ThemeData.light(),
            home: Scaffold(
              body: UserCard(user: testUser),
            ),
          ),
          name: 'light_theme',
        )
        ..addScenario(
          widget: MaterialApp(
            theme: ThemeData.dark(),
            home: Scaffold(
              body: UserCard(user: testUser),
            ),
          ),
          name: 'dark_theme',
        );
      
      await tester.pumpDeviceBuilder(builder);
      
      await screenMatchesGolden(tester, 'theme_variations');
    });
  });
}
```

## 6. 性能测试

### 6.1 Widget 性能测试

```dart
// test/performance/widget_performance_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../helpers/test_helpers.dart';

class PerformanceTestHelper {
  static Future<Duration> measureWidgetBuildTime(
    WidgetTester tester,
    Widget widget,
  ) async {
    final stopwatch = Stopwatch()..start();
    
    await tester.pumpWidget(widget);
    await tester.pumpAndSettle();
    
    stopwatch.stop();
    return stopwatch.elapsed;
  }
  
  static Future<void> measureScrollPerformance(
    WidgetTester tester,
    Widget widget,
    {int scrollCount = 10}
  ) async {
    await tester.pumpWidget(widget);
    await tester.pumpAndSettle();
    
    final listFinder = find.byType(ListView);
    expect(listFinder, findsOneWidget);
    
    final stopwatch = Stopwatch()..start();
    
    for (int i = 0; i < scrollCount; i++) {
      await tester.drag(listFinder, const Offset(0, -300));
      await tester.pumpAndSettle();
    }
    
    stopwatch.stop();
    
    final averageTime = stopwatch.elapsedMilliseconds / scrollCount;
    expect(averageTime, lessThan(100)); // 每次滚动应少于100ms
  }
  
  static Future<void> measureMemoryUsage(
    WidgetTester tester,
    Widget widget,
  ) async {
    // 测试内存使用情况
    await tester.pumpWidget(widget);
    await tester.pumpAndSettle();
    
    // 强制垃圾回收
    await tester.binding.reassembleApplication();
    await tester.pumpAndSettle();
    
    // 这里可以集成内存监控工具
    // 例如使用 vm_service 来监控内存使用
  }
}

void main() {
  group('Widget Performance Tests', () {
    testWidgets('UserCard build performance', (tester) async {
      final testUser = User.fromJson(TestHelpers.createTestUser());
      
      final buildTime = await PerformanceTestHelper.measureWidgetBuildTime(
        tester,
        MaterialApp(
          home: Scaffold(
            body: UserCard(user: testUser),
          ),
        ),
      );
      
      // Widget 构建时间应少于 50ms
      expect(buildTime.inMilliseconds, lessThan(50));
    });
    
    testWidgets('UserList scroll performance', (tester) async {
      final testUsers = TestDataGenerator.generateUsers(100)
          .map((data) => User.fromJson(data))
          .toList();
      
      await PerformanceTestHelper.measureScrollPerformance(
        tester,
        MaterialApp(
          home: Scaffold(
            body: ListView.builder(
              itemCount: testUsers.length,
              itemBuilder: (context, index) {
                return UserCard(user: testUsers[index]);
              },
            ),
          ),
        ),
      );
    });
    
    testWidgets('Large list memory usage', (tester) async {
      final testUsers = TestDataGenerator.generateUsers(1000)
          .map((data) => User.fromJson(data))
          .toList();
      
      await PerformanceTestHelper.measureMemoryUsage(
        tester,
        MaterialApp(
          home: Scaffold(
            body: ListView.builder(
              itemCount: testUsers.length,
              itemBuilder: (context, index) {
                return UserCard(user: testUsers[index]);
              },
            ),
          ),
        ),
      );
    });
    
    testWidgets('Animation performance', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              width: 100,
              height: 100,
              color: Colors.blue,
            ),
          ),
        ),
      );
      
      final stopwatch = Stopwatch()..start();
      
      // 触发动画
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              width: 200,
              height: 200,
              color: Colors.red,
            ),
          ),
        ),
      );
      
      // 等待动画完成
      await tester.pumpAndSettle();
      
      stopwatch.stop();
      
      // 动画应在合理时间内完成
      expect(stopwatch.elapsedMilliseconds, lessThan(500));
    });
  });
}
```

### 6.2 API 性能测试

```dart
// test/performance/api_performance_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import '../helpers/test_helpers.dart';

class ApiPerformanceHelper {
  static Future<Duration> measureApiResponseTime(
    Future<http.Response> Function() apiCall,
  ) async {
    final stopwatch = Stopwatch()..start();
    
    await apiCall();
    
    stopwatch.stop();
    return stopwatch.elapsed;
  }
  
  static Future<List<Duration>> measureConcurrentRequests(
    List<Future<http.Response> Function()> apiCalls,
  ) async {
    final stopwatch = Stopwatch()..start();
    final results = <Duration>[];
    
    final futures = apiCalls.map((call) async {
      final callStopwatch = Stopwatch()..start();
      await call();
      callStopwatch.stop();
      return callStopwatch.elapsed;
    });
    
    final durations = await Future.wait(futures);
    stopwatch.stop();
    
    return durations;
  }
  
  static Future<void> measureThroughput(
    Future<http.Response> Function() apiCall,
    {int requestCount = 100, Duration timeWindow = const Duration(seconds: 10)}
  ) async {
    final stopwatch = Stopwatch()..start();
    int completedRequests = 0;
    
    final futures = <Future>[];
    
    while (stopwatch.elapsed < timeWindow) {
      if (futures.length < requestCount) {
        futures.add(
          apiCall().then((_) => completedRequests++),
        );
      }
      
      // 清理已完成的请求
      futures.removeWhere((future) => future.isCompleted);
      
      await Future.delayed(const Duration(milliseconds: 10));
    }
    
    // 等待剩余请求完成
    await Future.wait(futures);
    
    final throughput = completedRequests / timeWindow.inSeconds;
    print('Throughput: $throughput requests/second');
    
    // 验证吞吐量满足要求
    expect(throughput, greaterThan(10)); // 至少10 req/s
  }
}

void main() {
  group('API Performance Tests', () {
    late ApiService apiService;
    
    setUpAll(() async {
      await TestHelpers.setupTestEnvironment();
      apiService = ApiServiceImpl(
        baseUrl: 'http://localhost:3000',
        httpClient: http.Client(),
      );
    });
    
    tearDownAll(() async {
      await TestHelpers.tearDownTestEnvironment();
    });
    
    test('login API response time', () async {
      final responseTime = await ApiPerformanceHelper.measureApiResponseTime(
        () => apiService.post(
          '/auth/login',
          data: {
            'email': '<EMAIL>',
            'password': 'password123',
          },
        ).then((result) => http.Response('{}', 200)),
      );
      
      // 登录API应在2秒内响应
      expect(responseTime.inSeconds, lessThan(2));
    });
    
    test('users list API response time', () async {
      final responseTime = await ApiPerformanceHelper.measureApiResponseTime(
        () => apiService.get('/users')
            .then((result) => http.Response('[]', 200)),
      );
      
      // 用户列表API应在1秒内响应
      expect(responseTime.inMilliseconds, lessThan(1000));
    });
    
    test('concurrent API requests', () async {
      final apiCalls = List.generate(
        10,
        (index) => () => apiService.get('/users')
            .then((result) => http.Response('[]', 200)),
      );
      
      final durations = await ApiPerformanceHelper.measureConcurrentRequests(
        apiCalls,
      );
      
      // 所有并发请求都应在合理时间内完成
      for (final duration in durations) {
        expect(duration.inSeconds, lessThan(5));
      }
      
      // 平均响应时间应合理
      final averageTime = durations
          .map((d) => d.inMilliseconds)
          .reduce((a, b) => a + b) / durations.length;
      
      expect(averageTime, lessThan(2000));
    });
    
    test('API throughput', () async {
      await ApiPerformanceHelper.measureThroughput(
        () => apiService.get('/users')
            .then((result) => http.Response('[]', 200)),
        requestCount: 50,
        timeWindow: const Duration(seconds: 5),
      );
    });
  });
}
```

## 7. 代码覆盖率

### 7.1 覆盖率配置

```yaml
# pubspec.yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  coverage: ^1.6.0
  test_coverage: ^0.5.0
  lcov: ^6.0.0
```

### 7.2 覆盖率脚本

```bash
#!/bin/bash
# scripts/test_coverage.sh

set -e

echo "Running tests with coverage..."

# 清理之前的覆盖率数据
rm -rf coverage

# 运行测试并生成覆盖率数据
flutter test --coverage

# 过滤掉生成的文件和测试文件
lcov --remove coverage/lcov.info \
  'lib/generated/*' \
  'lib/**/*.g.dart' \
  'lib/**/*.freezed.dart' \
  'lib/**/*.mocks.dart' \
  'test/*' \
  -o coverage/lcov_filtered.info

# 生成HTML报告
genhtml coverage/lcov_filtered.info -o coverage/html

# 显示覆盖率摘要
lcov --summary coverage/lcov_filtered.info

echo "Coverage report generated in coverage/html/index.html"

# 检查覆盖率阈值
COVERAGE_THRESHOLD=80
COVERAGE_PERCENT=$(lcov --summary coverage/lcov_filtered.info | grep "lines" | grep -o '[0-9]\+\.[0-9]\+%' | sed 's/%//')

if (( $(echo "$COVERAGE_PERCENT < $COVERAGE_THRESHOLD" | bc -l) )); then
  echo "❌ Coverage $COVERAGE_PERCENT% is below threshold $COVERAGE_THRESHOLD%"
  exit 1
else
  echo "✅ Coverage $COVERAGE_PERCENT% meets threshold $COVERAGE_THRESHOLD%"
fi
```

### 7.3 覆盖率分析工具

```dart
// tools/coverage_analyzer.dart
import 'dart:io';
import 'dart:convert';

class CoverageAnalyzer {
  static Future<void> analyzeCoverage() async {
    final lcovFile = File('coverage/lcov.info');
    
    if (!lcovFile.existsSync()) {
      print('Coverage file not found. Run tests with --coverage first.');
      return;
    }
    
    final content = await lcovFile.readAsString();
    final coverage = _parseLcovFile(content);
    
    _printCoverageSummary(coverage);
    _identifyUncoveredFiles(coverage);
    _generateCoverageReport(coverage);
  }
  
  static Map<String, FileCoverage> _parseLcovFile(String content) {
    final coverage = <String, FileCoverage>{};
    final lines = content.split('\n');
    
    String? currentFile;
    int totalLines = 0;
    int coveredLines = 0;
    
    for (final line in lines) {
      if (line.startsWith('SF:')) {
        currentFile = line.substring(3);
      } else if (line.startsWith('LH:')) {
        coveredLines = int.parse(line.substring(3));
      } else if (line.startsWith('LF:')) {
        totalLines = int.parse(line.substring(3));
      } else if (line == 'end_of_record' && currentFile != null) {
        coverage[currentFile] = FileCoverage(
          file: currentFile,
          totalLines: totalLines,
          coveredLines: coveredLines,
        );
        currentFile = null;
        totalLines = 0;
        coveredLines = 0;
      }
    }
    
    return coverage;
  }
  
  static void _printCoverageSummary(Map<String, FileCoverage> coverage) {
    final totalLines = coverage.values
        .map((c) => c.totalLines)
        .fold(0, (a, b) => a + b);
    
    final totalCovered = coverage.values
        .map((c) => c.coveredLines)
        .fold(0, (a, b) => a + b);
    
    final percentage = (totalCovered / totalLines * 100).toStringAsFixed(2);
    
    print('\n📊 Coverage Summary:');
    print('Total lines: $totalLines');
    print('Covered lines: $totalCovered');
    print('Coverage: $percentage%');
    print('=' * 50);
  }
  
  static void _identifyUncoveredFiles(Map<String, FileCoverage> coverage) {
    final uncoveredFiles = coverage.values
        .where((c) => c.coveragePercentage < 80)
        .toList()
      ..sort((a, b) => a.coveragePercentage.compareTo(b.coveragePercentage));
    
    if (uncoveredFiles.isNotEmpty) {
      print('\n⚠️  Files with low coverage (<80%):');
      for (final file in uncoveredFiles) {
        print('${file.file}: ${file.coveragePercentage.toStringAsFixed(1)}%');
      }
    }
  }
  
  static void _generateCoverageReport(Map<String, FileCoverage> coverage) {
    final report = {
      'timestamp': DateTime.now().toIso8601String(),
      'summary': {
        'totalFiles': coverage.length,
        'totalLines': coverage.values.map((c) => c.totalLines).fold(0, (a, b) => a + b),
        'coveredLines': coverage.values.map((c) => c.coveredLines).fold(0, (a, b) => a + b),
      },
      'files': coverage.values.map((c) => c.toJson()).toList(),
    };
    
    final reportFile = File('coverage/coverage_report.json');
    reportFile.writeAsStringSync(json.encode(report));
    
    print('\n📄 Detailed report saved to: ${reportFile.path}');
  }
}

class FileCoverage {
  final String file;
  final int totalLines;
  final int coveredLines;
  
  FileCoverage({
    required this.file,
    required this.totalLines,
    required this.coveredLines,
  });
  
  double get coveragePercentage => 
      totalLines > 0 ? (coveredLines / totalLines * 100) : 0;
  
  Map<String, dynamic> toJson() => {
    'file': file,
    'totalLines': totalLines,
    'coveredLines': coveredLines,
    'coveragePercentage': coveragePercentage,
  };
}

void main() async {
  await CoverageAnalyzer.analyzeCoverage();
}
```

## 8. 测试自动化和 CI/CD

### 8.1 GitHub Actions 配置

```yaml
# .github/workflows/test.yml
name: Test and Coverage

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
    
    - name: Install dependencies
      run: flutter pub get
    
    - name: Verify formatting
      run: dart format --output=none --set-exit-if-changed .
    
    - name: Analyze project source
      run: dart analyze --fatal-infos
    
    - name: Run unit tests
      run: flutter test --coverage
    
    - name: Run integration tests
      run: flutter test integration_test/
    
    - name: Generate coverage report
      run: |
        sudo apt-get update
        sudo apt-get install -y lcov
        lcov --remove coverage/lcov.info \
          'lib/generated/*' \
          'lib/**/*.g.dart' \
          'lib/**/*.freezed.dart' \
          'lib/**/*.mocks.dart' \
          'test/*' \
          -o coverage/lcov_filtered.info
        genhtml coverage/lcov_filtered.info -o coverage/html
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: coverage/lcov_filtered.info
        flags: unittests
        name: codecov-umbrella
    
    - name: Upload coverage reports
      uses: actions/upload-artifact@v3
      with:
        name: coverage-report
        path: coverage/html/
    
    - name: Check coverage threshold
      run: |
        COVERAGE_PERCENT=$(lcov --summary coverage/lcov_filtered.info | grep "lines" | grep -o '[0-9]\+\.[0-9]\+%' | sed 's/%//')
        echo "Coverage: $COVERAGE_PERCENT%"
        if (( $(echo "$COVERAGE_PERCENT < 80" | bc -l) )); then
          echo "❌ Coverage $COVERAGE_PERCENT% is below threshold 80%"
          exit 1
        else
          echo "✅ Coverage $COVERAGE_PERCENT% meets threshold 80%"
        fi

  e2e-test:
    runs-on: macos-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
    
    - name: Install dependencies
      run: flutter pub get
    
    - name: Start iOS Simulator
      run: |
        xcrun simctl boot "iPhone 14" || true
        xcrun simctl list devices
    
    - name: Run E2E tests
      run: |
        flutter drive \
          --driver=test_driver/integration_test.dart \
          --target=integration_test/app_test.dart
    
    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-test-results
        path: test_results/
```

### 8.2 测试报告生成

```dart
// tools/test_reporter.dart
import 'dart:io';
import 'dart:convert';

class TestReporter {
  static Future<void> generateReport() async {
    final testResults = await _collectTestResults();
    final coverageData = await _collectCoverageData();
    
    final report = TestReport(
      timestamp: DateTime.now(),
      testResults: testResults,
      coverage: coverageData,
    );
    
    await _generateHtmlReport(report);
    await _generateJsonReport(report);
    await _generateMarkdownReport(report);
    
    print('📊 Test reports generated successfully!');
  }
  
  static Future<TestResults> _collectTestResults() async {
    // 解析测试结果文件
    final unitTestFile = File('test_results/unit_tests.json');
    final integrationTestFile = File('test_results/integration_tests.json');
    
    final unitTests = unitTestFile.existsSync()
        ? json.decode(await unitTestFile.readAsString())
        : {};
    
    final integrationTests = integrationTestFile.existsSync()
        ? json.decode(await integrationTestFile.readAsString())
        : {};
    
    return TestResults(
      unitTests: unitTests,
      integrationTests: integrationTests,
    );
  }
  
  static Future<CoverageData> _collectCoverageData() async {
    final coverageFile = File('coverage/coverage_report.json');
    
    if (!coverageFile.existsSync()) {
      return CoverageData.empty();
    }
    
    final data = json.decode(await coverageFile.readAsString());
    return CoverageData.fromJson(data);
  }
  
  static Future<void> _generateHtmlReport(TestReport report) async {
    final html = '''
<!DOCTYPE html>
<html>
<head>
    <title>Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .success { color: green; }
        .failure { color: red; }
        .coverage-bar { 
            width: 100%; 
            height: 20px; 
            background: #f0f0f0; 
            border-radius: 10px;
            overflow: hidden;
        }
        .coverage-fill { 
            height: 100%; 
            background: linear-gradient(90deg, #ff4444, #ffaa00, #44ff44);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Test Report</h1>
        <p>Generated: ${report.timestamp}</p>
    </div>
    
    <div class="section">
        <h2>Test Summary</h2>
        <p>Unit Tests: ${report.testResults.unitTestsPassed}/${report.testResults.totalUnitTests}</p>
        <p>Integration Tests: ${report.testResults.integrationTestsPassed}/${report.testResults.totalIntegrationTests}</p>
    </div>
    
    <div class="section">
        <h2>Coverage</h2>
        <div class="coverage-bar">
            <div class="coverage-fill" style="width: ${report.coverage.percentage}%"></div>
        </div>
        <p>${report.coverage.percentage.toStringAsFixed(1)}% (${report.coverage.coveredLines}/${report.coverage.totalLines} lines)</p>
    </div>
</body>
</html>
    ''';
    
    final reportFile = File('test_results/report.html');
    await reportFile.create(recursive: true);
    await reportFile.writeAsString(html);
  }
  
  static Future<void> _generateJsonReport(TestReport report) async {
    final reportFile = File('test_results/report.json');
    await reportFile.create(recursive: true);
    await reportFile.writeAsString(json.encode(report.toJson()));
  }
  
  static Future<void> _generateMarkdownReport(TestReport report) async {
    final markdown = '''
# Test Report

**Generated:** ${report.timestamp}

## Test Summary

| Type | Passed | Total | Success Rate |
|------|--------|-------|-------------|
| Unit Tests | ${report.testResults.unitTestsPassed} | ${report.testResults.totalUnitTests} | ${(report.testResults.unitTestsPassed / report.testResults.totalUnitTests * 100).toStringAsFixed(1)}% |
| Integration Tests | ${report.testResults.integrationTestsPassed} | ${report.testResults.totalIntegrationTests} | ${(report.testResults.integrationTestsPassed / report.testResults.totalIntegrationTests * 100).toStringAsFixed(1)}% |

## Coverage

**Overall Coverage:** ${report.coverage.percentage.toStringAsFixed(1)}%

- **Total Lines:** ${report.coverage.totalLines}
- **Covered Lines:** ${report.coverage.coveredLines}
- **Uncovered Lines:** ${report.coverage.totalLines - report.coverage.coveredLines}

## Files with Low Coverage

${report.coverage.lowCoverageFiles.map((file) => '- `${file.name}`: ${file.coverage.toStringAsFixed(1)}%').join('\n')}
    ''';
    
    final reportFile = File('test_results/report.md');
    await reportFile.create(recursive: true);
    await reportFile.writeAsString(markdown);
  }
}

class TestReport {
  final DateTime timestamp;
  final TestResults testResults;
  final CoverageData coverage;
  
  TestReport({
    required this.timestamp,
    required this.testResults,
    required this.coverage,
  });
  
  Map<String, dynamic> toJson() => {
    'timestamp': timestamp.toIso8601String(),
    'testResults': testResults.toJson(),
    'coverage': coverage.toJson(),
  };
}

class TestResults {
  final Map<String, dynamic> unitTests;
  final Map<String, dynamic> integrationTests;
  
  TestResults({
    required this.unitTests,
    required this.integrationTests,
  });
  
  int get totalUnitTests => unitTests['total'] ?? 0;
  int get unitTestsPassed => unitTests['passed'] ?? 0;
  int get totalIntegrationTests => integrationTests['total'] ?? 0;
  int get integrationTestsPassed => integrationTests['passed'] ?? 0;
  
  Map<String, dynamic> toJson() => {
    'unitTests': unitTests,
    'integrationTests': integrationTests,
  };
}

class CoverageData {
  final int totalLines;
  final int coveredLines;
  final List<FileInfo> lowCoverageFiles;
  
  CoverageData({
    required this.totalLines,
    required this.coveredLines,
    required this.lowCoverageFiles,
  });
  
  factory CoverageData.empty() => CoverageData(
    totalLines: 0,
    coveredLines: 0,
    lowCoverageFiles: [],
  );
  
  factory CoverageData.fromJson(Map<String, dynamic> json) {
    final summary = json['summary'] as Map<String, dynamic>;
    final files = (json['files'] as List)
        .map((f) => FileInfo.fromJson(f))
        .where((f) => f.coverage < 80)
        .toList();
    
    return CoverageData(
      totalLines: summary['totalLines'],
      coveredLines: summary['coveredLines'],
      lowCoverageFiles: files,
    );
  }
  
  double get percentage => totalLines > 0 ? (coveredLines / totalLines * 100) : 0;
  
  Map<String, dynamic> toJson() => {
    'totalLines': totalLines,
    'coveredLines': coveredLines,
    'percentage': percentage,
    'lowCoverageFiles': lowCoverageFiles.map((f) => f.toJson()).toList(),
  };
}

class FileInfo {
  final String name;
  final double coverage;
  
  FileInfo({required this.name, required this.coverage});
  
  factory FileInfo.fromJson(Map<String, dynamic> json) => FileInfo(
    name: json['file'],
    coverage: json['coveragePercentage'],
  );
  
  Map<String, dynamic> toJson() => {
    'name': name,
    'coverage': coverage,
  };
}

void main() async {
  await TestReporter.generateReport();
}
```

## 9. 质量保证工具和最佳实践

### 9.1 代码质量检查

```yaml
# analysis_options.yaml
include: package:flutter_lints/flutter.yaml

linter:
  rules:
    # 启用额外的规则
    always_declare_return_types: true
    always_put_control_body_on_new_line: true
    always_put_required_named_parameters_first: true
    always_specify_types: true
    annotate_overrides: true
    avoid_bool_literals_in_conditional_expressions: true
    avoid_catches_without_on_clauses: true
    avoid_catching_errors: true
    avoid_double_and_int_checks: true
    avoid_empty_else: true
    avoid_field_initializers_in_const_classes: true
    avoid_function_literals_in_foreach_calls: true
    avoid_init_to_null: true
    avoid_null_checks_in_equality_operators: true
    avoid_positional_boolean_parameters: true
    avoid_print: true
    avoid_private_typedef_functions: true
    avoid_redundant_argument_values: true
    avoid_relative_lib_imports: true
    avoid_renaming_method_parameters: true
    avoid_return_types_on_setters: true
    avoid_returning_null_for_void: true
    avoid_setters_without_getters: true
    avoid_shadowing_type_parameters: true
    avoid_single_cascade_in_expression_statements: true
    avoid_slow_async_io: true
    avoid_types_as_parameter_names: true
    avoid_unnecessary_containers: true
    avoid_unused_constructor_parameters: true
    avoid_void_async: true
    await_only_futures: true
    camel_case_extensions: true
    camel_case_types: true
    cancel_subscriptions: true
    cascade_invocations: true
    close_sinks: true
    comment_references: true
    constant_identifier_names: true
    control_flow_in_finally: true
    curly_braces_in_flow_control_structures: true
    directives_ordering: true
    empty_catches: true
    empty_constructor_bodies: true
    empty_statements: true
    file_names: true
    hash_and_equals: true
    implementation_imports: true
    invariant_booleans: true
    iterable_contains_unrelated_type: true
    join_return_with_assignment: true
    library_names: true
    library_prefixes: true
    lines_longer_than_80_chars: true
    list_remove_unrelated_type: true
    literal_only_boolean_expressions: true
    missing_whitespace_between_adjacent_strings: true
    no_adjacent_strings_in_list: true
    no_duplicate_case_values: true
    no_logic_in_create_state: true
    no_runtimeType_toString: true
    non_constant_identifier_names: true
    null_closures: true
    omit_local_variable_types: true
    one_member_abstracts: true
    only_throw_errors: true
    overridden_fields: true
    package_api_docs: true
    package_names: true
    package_prefixed_library_names: true
    parameter_assignments: true
    prefer_adjacent_string_concatenation: true
    prefer_asserts_in_initializer_lists: true
    prefer_asserts_with_message: true
    prefer_collection_literals: true
    prefer_conditional_assignment: true
    prefer_const_constructors: true
    prefer_const_constructors_in_immutables: true
    prefer_const_declarations: true
    prefer_const_literals_to_create_immutables: true
    prefer_constructors_over_static_methods: true
    prefer_contains: true
    prefer_equal_for_default_values: true
    prefer_expression_function_bodies: true
    prefer_final_fields: true
    prefer_final_in_for_each: true
    prefer_final_locals: true
    prefer_for_elements_to_map_fromIterable: true
    prefer_foreach: true
    prefer_function_declarations_over_variables: true
    prefer_generic_function_type_aliases: true
    prefer_if_elements_to_conditional_expressions: true
    prefer_if_null_operators: true
    prefer_initializing_formals: true
    prefer_inlined_adds: true
    prefer_int_literals: true
    prefer_interpolation_to_compose_strings: true
    prefer_is_empty: true
    prefer_is_not_empty: true
    prefer_is_not_operator: true
    prefer_iterable_whereType: true
    prefer_mixin: true
    prefer_null_aware_operators: true
    prefer_relative_imports: true
    prefer_single_quotes: true
    prefer_spread_collections: true
    prefer_typing_uninitialized_variables: true
    prefer_void_to_null: true
    provide_deprecation_message: true
    public_member_api_docs: true
    recursive_getters: true
    slash_for_doc_comments: true
    sort_child_properties_last: true
    sort_constructors_first: true
    sort_pub_dependencies: true
    sort_unnamed_constructors_first: true
    test_types_in_equals: true
    throw_in_finally: true
    type_annotate_public_apis: true
    type_init_formals: true
    unawaited_futures: true
    unnecessary_await_in_return: true
    unnecessary_brace_in_string_interps: true
    unnecessary_const: true
    unnecessary_getters_setters: true
    unnecessary_lambdas: true
    unnecessary_new: true
    unnecessary_null_aware_assignments: true
    unnecessary_null_in_if_null_operators: true
    unnecessary_overrides: true
    unnecessary_parenthesis: true
    unnecessary_statements: true
    unnecessary_string_escapes: true
    unnecessary_string_interpolations: true
    unnecessary_this: true
    unrelated_type_equality_checks: true
    unsafe_html: true
    use_full_hex_values_for_flutter_colors: true
    use_function_type_syntax_for_parameters: true
    use_key_in_widget_constructors: true
    use_raw_strings: true
    use_rethrow_when_possible: true
    use_setters_to_change_properties: true
    use_string_buffers: true
    use_to_and_as_if_applicable: true
    valid_regexps: true
    void_checks: true

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.mocks.dart"
    - "build/**"
  errors:
    invalid_annotation_target: ignore
  language:
    strict-casts: true
    strict-inference: true
    strict-raw-types: true
```

### 9.2 测试最佳实践

```dart
// test/best_practices/testing_guidelines.dart

/// 测试最佳实践指南
/// 
/// 1. 测试命名规范
/// - 使用描述性的测试名称
/// - 遵循 "should_ExpectedBehavior_When_StateUnderTest" 模式
/// 
/// 2. 测试结构
/// - 使用 Arrange-Act-Assert (AAA) 模式
/// - 每个测试只验证一个行为
/// - 保持测试简单和专注
/// 
/// 3. Mock 使用
/// - 只 Mock 外部依赖
/// - 验证重要的交互
/// - 避免过度 Mock
/// 
/// 4. 测试数据
/// - 使用工厂方法创建测试数据
/// - 避免硬编码值
/// - 使用有意义的测试数据
/// 
/// 5. 异步测试
/// - 正确处理 Future 和 Stream
/// - 使用 expectLater 进行异步断言
/// - 避免不必要的延迟

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

// ✅ 好的测试示例
class GoodTestExample {
  void demonstrateGoodPractices() {
    group('UserService', () {
      late UserService userService;
      late MockUserRepository mockRepository;
      late MockNetworkService mockNetworkService;
      
      setUp(() {
        mockRepository = MockUserRepository();
        mockNetworkService = MockNetworkService();
        userService = UserService(
          repository: mockRepository,
          networkService: mockNetworkService,
        );
      });
      
      group('getUserById', () {
        test('should_ReturnUser_When_UserExists', () async {
          // Arrange
          const userId = 'user123';
          final expectedUser = TestDataFactory.createUser(id: userId);
          
          when(mockRepository.findById(userId))
              .thenAnswer((_) async => expectedUser);
          
          // Act
          final result = await userService.getUserById(userId);
          
          // Assert
          expect(result.isSuccess, true);
          expect(result.data, equals(expectedUser));
          verify(mockRepository.findById(userId)).called(1);
        });
        
        test('should_ReturnError_When_UserNotFound', () async {
          // Arrange
          const userId = 'nonexistent';
          
          when(mockRepository.findById(userId))
              .thenThrow(UserNotFoundException(userId));
          
          // Act
          final result = await userService.getUserById(userId);
          
          // Assert
          expect(result.isSuccess, false);
          expect(result.error, contains('User not found'));
          verify(mockRepository.findById(userId)).called(1);
        });
        
        test('should_ReturnError_When_NetworkUnavailable', () async {
          // Arrange
          const userId = 'user123';
          
          when(mockNetworkService.isConnected)
              .thenReturn(false);
          
          // Act
          final result = await userService.getUserById(userId);
          
          // Assert
          expect(result.isSuccess, false);
          expect(result.error, contains('Network unavailable'));
          verifyNever(mockRepository.findById(any));
        });
      });
      
      group('createUser', () {
        test('should_CreateUser_When_ValidDataProvided', () async {
          // Arrange
          final userData = TestDataFactory.createUserData(
            name: 'John Doe',
            email: '<EMAIL>',
          );
          final expectedUser = TestDataFactory.createUser(
            name: userData['name'],
            email: userData['email'],
          );
          
          when(mockRepository.create(any))
              .thenAnswer((_) async => expectedUser);
          when(mockNetworkService.isConnected)
              .thenReturn(true);
          
          // Act
          final result = await userService.createUser(userData);
          
          // Assert
          expect(result.isSuccess, true);
          expect(result.data?.name, userData['name']);
          expect(result.data?.email, userData['email']);
          
          final captured = verify(mockRepository.create(captureAny))
              .captured.single as Map<String, dynamic>;
          expect(captured['name'], userData['name']);
          expect(captured['email'], userData['email']);
        });
        
        test('should_ReturnValidationError_When_InvalidEmailProvided', () async {
          // Arrange
          final invalidUserData = TestDataFactory.createUserData(
            name: 'John Doe',
            email: 'invalid-email',
          );
          
          // Act
          final result = await userService.createUser(invalidUserData);
          
          // Assert
          expect(result.isSuccess, false);
          expect(result.error, contains('Invalid email'));
          verifyNever(mockRepository.create(any));
        });
      });
    });
  }
}

// ❌ 避免的测试反模式
class BadTestExample {
  void demonstrateBadPractices() {
    // ❌ 不好的测试名称
    test('test1', () {
      // 测试内容不清楚
    });
    
    // ❌ 测试多个行为
    test('user operations', () async {
      // 创建用户
      // 更新用户
      // 删除用户
      // 这应该分成三个独立的测试
    });
    
    // ❌ 硬编码值
    test('should create user', () async {
      final user = User(
        id: '12345', // 硬编码
        name: 'John Doe', // 硬编码
        email: '<EMAIL>', // 硬编码
      );
      // ...
    });
    
    // ❌ 过度 Mock
    test('should format user name', () {
      final mockString = MockString();
      when(mockString.toUpperCase()).thenReturn('JOHN DOE');
      // 不需要 Mock 基本类型
    });
    
    // ❌ 不处理异步
    test('should load users', () {
      userService.loadUsers(); // 忘记 await
      expect(userService.users.length, greaterThan(0)); // 可能失败
    });
  }
}

/// 测试数据工厂
class TestDataFactory {
  static User createUser({
    String? id,
    String? name,
    String? email,
    int? age,
  }) {
    return User(
      id: id ?? 'user_${DateTime.now().millisecondsSinceEpoch}',
      name: name ?? 'Test User',
      email: email ?? '<EMAIL>',
      age: age ?? 25,
    );
  }
  
  static Map<String, dynamic> createUserData({
    String? name,
    String? email,
    int? age,
  }) {
    return {
      'name': name ?? 'Test User',
      'email': email ?? '<EMAIL>',
      'age': age ?? 25,
    };
  }
  
  static List<User> createUsers(int count) {
    return List.generate(count, (index) => createUser(
      id: 'user_$index',
      name: 'User $index',
      email: 'user$<EMAIL>',
    ));
  }
}
```

## 10. 总结

本文档提供了 Flutter 企业级应用测试和质量保证的完整实现方案，包括：

### 测试类型覆盖
- **单元测试**：服务层、Repository 层、BLoC/Cubit 层
- **Widget 测试**：UI 组件、页面、表单验证
- **集成测试**：API 集成、数据库集成
- **UI 测试**：端到端测试、用户流程测试
- **性能测试**：Widget 性能、API 性能、内存使用

### 质量保证工具
- **代码覆盖率**：LCOV 报告、覆盖率分析
- **静态分析**：Dart Analyzer、Lint 规则
- **Golden 测试**：UI 回归测试
- **自动化测试**：CI/CD 集成、测试报告

### 最佳实践
- **测试结构**：AAA 模式、清晰命名
- **Mock 策略**：合理使用、避免过度
- **测试数据**：工厂模式、避免硬编码
- **异步处理**：正确的 Future/Stream 测试

### 工具和框架
- **测试框架**：flutter_test、integration_test
- **Mock 工具**：mockito、mocktail
- **UI 测试**：patrol、golden_toolkit
- **覆盖率工具**：coverage、lcov
- **CI/CD**：GitHub Actions、测试自动化

这套测试体系确保了代码质量、功能正确性和性能表现，为企业级 Flutter 应用提供了可靠的质量保证基础。

```yaml
# pubspec.yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  mockito: ^5.4.2
  build_runner: ^2.4.7
  test: ^1.24.3
  golden_toolkit: ^0.15.0
  patrol: ^2.6.0
  flutter_driver:
    sdk: flutter
  coverage: ^1.6.3
  very_good_analysis: ^5.1.0
  dart_code_metrics: ^5.7.6
  import_sorter: ^4.6.0
```

### 1.2 测试基础类和工具

```dart
// test/helpers/test_helpers.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

// 生成 Mock 类
@GenerateMocks([
  DatabaseService,
  ApiService,
  AuthService,
  CacheManager,
  Logger,
  SharedPreferences,
])
import 'test_helpers.mocks.dart';

class TestHelpers {
  static late GetIt testGetIt;
  
  /// 初始化测试环境
  static Future<void> setupTestEnvironment() async {
    testGetIt = GetIt.instance;
    
    // 重置 GetIt 实例
    await testGetIt.reset();
    
    // 注册测试用的 Mock 服务
    _registerMockServices();
  }
  
  /// 清理测试环境
  static Future<void> tearDownTestEnvironment() async {
    await testGetIt.reset();
  }
  
  /// 注册 Mock 服务
  static void _registerMockServices() {
    testGetIt.registerLazySingleton<DatabaseService>(() => MockDatabaseService());
    testGetIt.registerLazySingleton<ApiService>(() => MockApiService());
    testGetIt.registerLazySingleton<AuthService>(() => MockAuthService());
    testGetIt.registerLazySingleton<CacheManager>(() => MockCacheManager());
    testGetIt.registerLazySingleton<Logger>(() => MockLogger());
  }
  
  /// 创建测试用的 Widget
  static Widget createTestWidget(Widget child) {
    return MaterialApp(
      home: child,
      localizationsDelegates: [
        DefaultMaterialLocalizations.delegate,
        DefaultWidgetsLocalizations.delegate,
      ],
    );
  }
  
  /// 等待动画完成
  static Future<void> waitForAnimations(WidgetTester tester) async {
    await tester.pumpAndSettle();
  }
  
  /// 查找 Widget 并验证存在
  static Finder findWidgetAndVerify(Type widgetType) {
    final finder = find.byType(widgetType);
    expect(finder, findsOneWidget);
    return finder;
  }
  
  /// 模拟网络延迟
  static Future<void> simulateNetworkDelay([Duration? delay]) async {
    await Future.delayed(delay ?? const Duration(milliseconds: 100));
  }
  
  /// 创建测试用的用户数据
  static Map<String, dynamic> createTestUser({
    String? id,
    String? name,
    String? email,
  }) {
    return {
      'id': id ?? 'test_user_id',
      'name': name ?? 'Test User',
      'email': email ?? '<EMAIL>',
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };
  }
  
  /// 验证错误状态
  static void verifyErrorState(WidgetTester tester, String expectedError) {
    expect(find.text(expectedError), findsOneWidget);
  }
  
  /// 验证加载状态
  static void verifyLoadingState(WidgetTester tester) {
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
  }
}

/// 测试数据生成器
class TestDataGenerator {
  static List<Map<String, dynamic>> generateUsers(int count) {
    return List.generate(count, (index) => {
      'id': 'user_$index',
      'name': 'User $index',
      'email': 'user$<EMAIL>',
      'created_at': DateTime.now().subtract(Duration(days: index)).toIso8601String(),
    });
  }
  
  static Map<String, dynamic> generateApiResponse({
    bool success = true,
    dynamic data,
    String? error,
  }) {
    return {
      'success': success,
      'data': data,
      'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}

/// 自定义匹配器
class CustomMatchers {
  /// 验证 Widget 是否可见
  static Matcher isVisible() {
    return _IsVisibleMatcher();
  }
  
  /// 验证文本是否包含特定内容
  static Matcher containsText(String text) {
    return _ContainsTextMatcher(text);
  }
}

class _IsVisibleMatcher extends Matcher {
  @override
  bool matches(dynamic item, Map matchState) {
    if (item is Finder) {
      return item.evaluate().isNotEmpty;
    }
    return false;
  }
  
  @override
  Description describe(Description description) {
    return description.add('is visible');
  }
}

class _ContainsTextMatcher extends Matcher {
  final String expectedText;
  
  _ContainsTextMatcher(this.expectedText);
  
  @override
  bool matches(dynamic item, Map matchState) {
    if (item is String) {
      return item.contains(expectedText);
    }
    return false;
  }
  
  @override
  Description describe(Description description) {
    return description.add('contains text "$expectedText"');
  }
}
```

## 2. 单元测试

### 2.1 服务层单元测试

```dart
// test/unit/services/auth_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import '../../helpers/test_helpers.dart';

void main() {
  group('AuthService Tests', () {
    late AuthService authService;
    late MockApiService mockApiService;
    late MockCacheManager mockCacheManager;
    late MockLogger mockLogger;
    
    setUp(() async {
      await TestHelpers.setupTestEnvironment();
      
      mockApiService = TestHelpers.testGetIt<ApiService>() as MockApiService;
      mockCacheManager = TestHelpers.testGetIt<CacheManager>() as MockCacheManager;
      mockLogger = TestHelpers.testGetIt<Logger>() as MockLogger;
      
      authService = AuthServiceImpl(
        apiService: mockApiService,
        cacheManager: mockCacheManager,
        logger: mockLogger,
      );
    });
    
    tearDown(() async {
      await TestHelpers.tearDownTestEnvironment();
    });
    
    group('login', () {
      test('should return success when credentials are valid', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        final expectedResponse = TestDataGenerator.generateApiResponse(
          data: {
            'token': 'test_token',
            'user': TestHelpers.createTestUser(email: email),
          },
        );
        
        when(mockApiService.post(
          '/auth/login',
          data: anyNamed('data'),
        )).thenAnswer((_) async => ApiResponse.fromJson(expectedResponse));
        
        when(mockCacheManager.set(
          any,
          any,
          type: anyNamed('type'),
        )).thenAnswer((_) async {});
        
        // Act
        final result = await authService.login(email, password);
        
        // Assert
        expect(result.isSuccess, true);
        expect(result.data?.token, 'test_token');
        expect(result.data?.user.email, email);
        
        verify(mockApiService.post(
          '/auth/login',
          data: {
            'email': email,
            'password': password,
          },
        )).called(1);
        
        verify(mockCacheManager.set(
          'auth_token',
          'test_token',
          type: CacheType.persistent,
        )).called(1);
      });
      
      test('should return failure when credentials are invalid', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'wrong_password';
        
        when(mockApiService.post(
          '/auth/login',
          data: anyNamed('data'),
        )).thenThrow(ApiException('Invalid credentials', 401));
        
        // Act
        final result = await authService.login(email, password);
        
        // Assert
        expect(result.isSuccess, false);
        expect(result.error, 'Invalid credentials');
        
        verifyNever(mockCacheManager.set(
          any,
          any,
          type: anyNamed('type'),
        ));
      });
      
      test('should handle network errors gracefully', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        
        when(mockApiService.post(
          '/auth/login',
          data: anyNamed('data'),
        )).thenThrow(NetworkException('No internet connection'));
        
        // Act
        final result = await authService.login(email, password);
        
        // Assert
        expect(result.isSuccess, false);
        expect(result.error, contains('network'));
        
        verify(mockLogger.error(
          'Login failed',
          any,
          any,
        )).called(1);
      });
    });
    
    group('logout', () {
      test('should clear cached data on logout', () async {
        // Arrange
        when(mockCacheManager.delete(any)).thenAnswer((_) async {});
        when(mockApiService.post('/auth/logout')).thenAnswer(
          (_) async => ApiResponse.fromJson(
            TestDataGenerator.generateApiResponse(),
          ),
        );
        
        // Act
        await authService.logout();
        
        // Assert
        verify(mockCacheManager.delete('auth_token')).called(1);
        verify(mockCacheManager.delete('current_user')).called(1);
        verify(mockApiService.post('/auth/logout')).called(1);
      });
    });
    
    group('getCurrentUser', () {
      test('should return cached user when available', () async {
        // Arrange
        final testUser = TestHelpers.createTestUser();
        when(mockCacheManager.get<Map<String, dynamic>>('current_user'))
            .thenAnswer((_) async => testUser);
        
        // Act
        final result = await authService.getCurrentUser();
        
        // Assert
        expect(result, isNotNull);
        expect(result?.id, testUser['id']);
        expect(result?.email, testUser['email']);
        
        verifyNever(mockApiService.get(any));
      });
      
      test('should fetch user from API when not cached', () async {
        // Arrange
        final testUser = TestHelpers.createTestUser();
        when(mockCacheManager.get<Map<String, dynamic>>('current_user'))
            .thenAnswer((_) async => null);
        
        when(mockApiService.get('/auth/me')).thenAnswer(
          (_) async => ApiResponse.fromJson(
            TestDataGenerator.generateApiResponse(data: testUser),
          ),
        );
        
        when(mockCacheManager.set(
          any,
          any,
          type: anyNamed('type'),
        )).thenAnswer((_) async {});
        
        // Act
        final result = await authService.getCurrentUser();
        
        // Assert
        expect(result, isNotNull);
        expect(result?.id, testUser['id']);
        
        verify(mockApiService.get('/auth/me')).called(1);
        verify(mockCacheManager.set(
          'current_user',
          testUser,
          type: CacheType.memory,
        )).called(1);
      });
    });
  });
}
```

### 2.2 Repository 层单元测试

```dart
// test/unit/repositories/user_repository_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import '../../helpers/test_helpers.dart';

void main() {
  group('UserRepository Tests', () {
    late UserRepository userRepository;
    late MockDatabaseService mockDatabaseService;
    late MockApiService mockApiService;
    late MockCacheManager mockCacheManager;
    
    setUp(() async {
      await TestHelpers.setupTestEnvironment();
      
      mockDatabaseService = TestHelpers.testGetIt<DatabaseService>() as MockDatabaseService;
      mockApiService = TestHelpers.testGetIt<ApiService>() as MockApiService;
      mockCacheManager = TestHelpers.testGetIt<CacheManager>() as MockCacheManager;
      
      userRepository = UserRepositoryImpl(
        databaseService: mockDatabaseService,
        apiService: mockApiService,
        cacheManager: mockCacheManager,
      );
    });
    
    tearDown(() async {
      await TestHelpers.tearDownTestEnvironment();
    });
    
    group('getUsers', () {
      test('should return cached users when available', () async {
        // Arrange
        final testUsers = TestDataGenerator.generateUsers(3);
        when(mockCacheManager.get<List<Map<String, dynamic>>>('users'))
            .thenAnswer((_) async => testUsers);
        
        // Act
        final result = await userRepository.getUsers();
        
        // Assert
        expect(result.isSuccess, true);
        expect(result.data?.length, 3);
        expect(result.data?.first.id, testUsers.first['id']);
        
        verifyNever(mockApiService.get(any));
        verifyNever(mockDatabaseService.query(any));
      });
      
      test('should fetch from API when cache is empty', () async {
        // Arrange
        final testUsers = TestDataGenerator.generateUsers(2);
        when(mockCacheManager.get<List<Map<String, dynamic>>>('users'))
            .thenAnswer((_) async => null);
        
        when(mockApiService.get('/users')).thenAnswer(
          (_) async => ApiResponse.fromJson(
            TestDataGenerator.generateApiResponse(data: testUsers),
          ),
        );
        
        when(mockCacheManager.set(
          any,
          any,
          type: anyNamed('type'),
          ttl: anyNamed('ttl'),
        )).thenAnswer((_) async {});
        
        when(mockDatabaseService.insertBatch(
          any,
          any,
        )).thenAnswer((_) async => DatabaseResult.success());
        
        // Act
        final result = await userRepository.getUsers();
        
        // Assert
        expect(result.isSuccess, true);
        expect(result.data?.length, 2);
        
        verify(mockApiService.get('/users')).called(1);
        verify(mockCacheManager.set(
          'users',
          testUsers,
          type: CacheType.memory,
          ttl: any,
        )).called(1);
        verify(mockDatabaseService.insertBatch('users', testUsers)).called(1);
      });
      
      test('should fallback to database when API fails', () async {
        // Arrange
        final testUsers = TestDataGenerator.generateUsers(1);
        when(mockCacheManager.get<List<Map<String, dynamic>>>('users'))
            .thenAnswer((_) async => null);
        
        when(mockApiService.get('/users'))
            .thenThrow(NetworkException('No internet connection'));
        
        when(mockDatabaseService.query('users')).thenAnswer(
          (_) async => DatabaseResult.success(data: testUsers),
        );
        
        when(mockCacheManager.set(
          any,
          any,
          type: anyNamed('type'),
        )).thenAnswer((_) async {});
        
        // Act
        final result = await userRepository.getUsers();
        
        // Assert
        expect(result.isSuccess, true);
        expect(result.data?.length, 1);
        
        verify(mockDatabaseService.query('users')).called(1);
        verify(mockCacheManager.set(
          'users',
          testUsers,
          type: CacheType.memory,
        )).called(1);
      });
    });
    
    group('createUser', () {
      test('should create user successfully', () async {
        // Arrange
        final userData = TestHelpers.createTestUser();
        when(mockApiService.post(
          '/users',
          data: anyNamed('data'),
        )).thenAnswer(
          (_) async => ApiResponse.fromJson(
            TestDataGenerator.generateApiResponse(data: userData),
          ),
        );
        
        when(mockDatabaseService.insert(
          any,
          any,
        )).thenAnswer((_) async => DatabaseResult.success());
        
        when(mockCacheManager.delete(any)).thenAnswer((_) async {});
        
        // Act
        final result = await userRepository.createUser(
          name: userData['name'],
          email: userData['email'],
        );
        
        // Assert
        expect(result.isSuccess, true);
        expect(result.data?.name, userData['name']);
        expect(result.data?.email, userData['email']);
        
        verify(mockApiService.post(
          '/users',
          data: {
            'name': userData['name'],
            'email': userData['email'],
          },
        )).called(1);
        
        verify(mockDatabaseService.insert('users', userData)).called(1);
        verify(mockCacheManager.delete('users')).called(1);
      });
      
      test('should handle validation errors', () async {
        // Arrange
        when(mockApiService.post(
          '/users',
          data: anyNamed('data'),
        )).thenThrow(ApiException('Email already exists', 422));
        
        // Act
        final result = await userRepository.createUser(
          name: 'Test User',
          email: '<EMAIL>',
        );
        
        // Assert
        expect(result.isSuccess, false);
        expect(result.error, 'Email already exists');
        
        verifyNever(mockDatabaseService.insert(any, any));
        verifyNever(mockCacheManager.delete(any));
      });
    });
  });
}
```

### 2.3 BLoC/Cubit 单元测试

```dart
// test/unit/blocs/user_bloc_test.dart
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import '../../helpers/test_helpers.dart';

void main() {
  group('UserBloc Tests', () {
    late UserBloc userBloc;
    late MockUserRepository mockUserRepository;
    
    setUp(() async {
      await TestHelpers.setupTestEnvironment();
      mockUserRepository = MockUserRepository();
      userBloc = UserBloc(userRepository: mockUserRepository);
    });
    
    tearDown(() {
      userBloc.close();
    });
    
    test('initial state should be UserInitial', () {
      expect(userBloc.state, equals(UserInitial()));
    });
    
    group('LoadUsers', () {
      blocTest<UserBloc, UserState>(
        'emits [UserLoading, UserLoaded] when LoadUsers is successful',
        build: () {
          final testUsers = TestDataGenerator.generateUsers(3)
              .map((data) => User.fromJson(data))
              .toList();
          
          when(mockUserRepository.getUsers())
              .thenAnswer((_) async => Result.success(testUsers));
          
          return userBloc;
        },
        act: (bloc) => bloc.add(LoadUsers()),
        expect: () => [
          UserLoading(),
          UserLoaded(TestDataGenerator.generateUsers(3)
              .map((data) => User.fromJson(data))
              .toList()),
        ],
        verify: (_) {
          verify(mockUserRepository.getUsers()).called(1);
        },
      );
      
      blocTest<UserBloc, UserState>(
        'emits [UserLoading, UserError] when LoadUsers fails',
        build: () {
          when(mockUserRepository.getUsers())
              .thenAnswer((_) async => Result.failure('Network error'));
          
          return userBloc;
        },
        act: (bloc) => bloc.add(LoadUsers()),
        expect: () => [
          UserLoading(),
          UserError('Network error'),
        ],
      );
    });
    
    group('CreateUser', () {
      blocTest<UserBloc, UserState>(
        'emits [UserCreating, UserCreated] when CreateUser is successful',
        build: () {
          final testUser = User.fromJson(TestHelpers.createTestUser());
          
          when(mockUserRepository.createUser(
            name: anyNamed('name'),
            email: anyNamed('email'),
          )).thenAnswer((_) async => Result.success(testUser));
          
          return userBloc;
        },
        act: (bloc) => bloc.add(CreateUser(
          name: 'Test User',
          email: '<EMAIL>',
        )),
        expect: () => [
          UserCreating(),
          UserCreated(User.fromJson(TestHelpers.createTestUser())),
        ],
        verify: (_) {
          verify(mockUserRepository.createUser(
            name: 'Test User',
            email: '<EMAIL>',
          )).called(1);
        },
      );
      
      blocTest<UserBloc, UserState>(
        'emits [UserCreating, UserError] when CreateUser fails',
        build: () {
          when(mockUserRepository.createUser(
            name: anyNamed('name'),
            email: anyNamed('email'),
          )).thenAnswer((_) async => Result.failure('Validation error'));
          
          return userBloc;
        },
        act: (bloc) => bloc.add(CreateUser(
          name: 'Test User',
          email: 'invalid-email',
        )),
        expect: () => [
          UserCreating(),
          UserError('Validation error'),
        ],
      );
    });
    
    group('RefreshUsers', () {
      blocTest<UserBloc, UserState>(
        'emits [UserRefreshing, UserLoaded] when RefreshUsers is successful',
        build: () {
          final testUsers = TestDataGenerator.generateUsers(2)
              .map((data) => User.fromJson(data))
              .toList();
          
          when(mockUserRepository.getUsers(forceRefresh: true))
              .thenAnswer((_) async => Result.success(testUsers));
          
          return userBloc;
        },
        seed: () => UserLoaded([
          User.fromJson(TestHelpers.createTestUser()),
        ]),
        act: (bloc) => bloc.add(RefreshUsers()),
        expect: () => [
          UserRefreshing([
            User.fromJson(TestHelpers.createTestUser()),
          ]),
          UserLoaded(TestDataGenerator.generateUsers(2)
              .map((data) => User.fromJson(data))
              .toList()),
        ],
        verify: (_) {
          verify(mockUserRepository.getUsers(forceRefresh: true)).called(1);
        },
      );
    });
  });
}
```

## 3. Widget 测试

### 3.1 基础 Widget 测试

```dart
// test/widget/widgets/user_card_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../../helpers/test_helpers.dart';

void main() {
  group('UserCard Widget Tests', () {
    late User testUser;
    
    setUp(() {
      testUser = User.fromJson(TestHelpers.createTestUser());
    });
    
    testWidgets('should display user information correctly', (tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          UserCard(user: testUser),
        ),
      );
      
      // Assert
      expect(find.text(testUser.name), findsOneWidget);
      expect(find.text(testUser.email), findsOneWidget);
      expect(find.byType(CircleAvatar), findsOneWidget);
    });
    
    testWidgets('should show placeholder when avatar URL is null', (tester) async {
      // Arrange
      final userWithoutAvatar = User.fromJson(
        TestHelpers.createTestUser()..remove('avatar_url'),
      );
      
      // Act
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          UserCard(user: userWithoutAvatar),
        ),
      );
      
      // Assert
      final circleAvatar = tester.widget<CircleAvatar>(
        find.byType(CircleAvatar),
      );
      expect(circleAvatar.backgroundImage, isNull);
      expect(circleAvatar.child, isA<Icon>());
    });
    
    testWidgets('should call onTap when card is tapped', (tester) async {
      // Arrange
      bool wasTapped = false;
      
      // Act
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          UserCard(
            user: testUser,
            onTap: () => wasTapped = true,
          ),
        ),
      );
      
      await tester.tap(find.byType(UserCard));
      await tester.pump();
      
      // Assert
      expect(wasTapped, true);
    });
    
    testWidgets('should show loading indicator when user is null', (tester) async {
      // Act
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          UserCard(user: null),
        ),
      );
      
      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });
    
    testWidgets('should apply custom styling when provided', (tester) async {
      // Arrange
      const customElevation = 8.0;
      const customMargin = EdgeInsets.all(16.0);
      
      // Act
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          UserCard(
            user: testUser,
            elevation: customElevation,
            margin: customMargin,
          ),
        ),
      );
      
      // Assert
      final card = tester.widget<Card>(find.byType(Card));
      expect(card.elevation, customElevation);
      expect(card.margin, customMargin);
    });
  });
}
```

### 3.2 页面 Widget 测试

```dart
// test/widget/pages/user_list_page_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';
import '../../helpers/test_helpers.dart';

void main() {
  group('UserListPage Widget Tests', () {
    late MockUserBloc mockUserBloc;
    
    setUp(() async {
      await TestHelpers.setupTestEnvironment();
      mockUserBloc = MockUserBloc();
    });
    
    tearDown(() async {
      await TestHelpers.tearDownTestEnvironment();
    });
    
    Widget createTestWidget() {
      return TestHelpers.createTestWidget(
        BlocProvider<UserBloc>(
          create: (_) => mockUserBloc,
          child: UserListPage(),
        ),
      );
    }
    
    testWidgets('should display loading indicator when state is UserLoading', (tester) async {
      // Arrange
      when(mockUserBloc.state).thenReturn(UserLoading());
      when(mockUserBloc.stream).thenAnswer((_) => Stream.value(UserLoading()));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();
      
      // Assert
      TestHelpers.verifyLoadingState(tester);
    });
    
    testWidgets('should display user list when state is UserLoaded', (tester) async {
      // Arrange
      final testUsers = TestDataGenerator.generateUsers(3)
          .map((data) => User.fromJson(data))
          .toList();
      
      when(mockUserBloc.state).thenReturn(UserLoaded(testUsers));
      when(mockUserBloc.stream).thenAnswer((_) => Stream.value(UserLoaded(testUsers)));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();
      
      // Assert
      expect(find.byType(ListView), findsOneWidget);
      expect(find.byType(UserCard), findsNWidgets(3));
      
      for (final user in testUsers) {
        expect(find.text(user.name), findsOneWidget);
        expect(find.text(user.email), findsOneWidget);
      }
    });
    
    testWidgets('should display error message when state is UserError', (tester) async {
      // Arrange
      const errorMessage = 'Failed to load users';
      when(mockUserBloc.state).thenReturn(UserError(errorMessage));
      when(mockUserBloc.stream).thenAnswer((_) => Stream.value(UserError(errorMessage)));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();
      
      // Assert
      TestHelpers.verifyErrorState(tester, errorMessage);
      expect(find.byType(ElevatedButton), findsOneWidget); // Retry button
    });
    
    testWidgets('should trigger LoadUsers event when retry button is tapped', (tester) async {
      // Arrange
      const errorMessage = 'Failed to load users';
      when(mockUserBloc.state).thenReturn(UserError(errorMessage));
      when(mockUserBloc.stream).thenAnswer((_) => Stream.value(UserError(errorMessage)));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();
      
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();
      
      // Assert
      verify(mockUserBloc.add(any)).called(1);
    });
    
    testWidgets('should show floating action button', (tester) async {
      // Arrange
      when(mockUserBloc.state).thenReturn(UserInitial());
      when(mockUserBloc.stream).thenAnswer((_) => Stream.value(UserInitial()));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();
      
      // Assert
      expect(find.byType(FloatingActionButton), findsOneWidget);
    });
    
    testWidgets('should navigate to create user page when FAB is tapped', (tester) async {
      // Arrange
      when(mockUserBloc.state).thenReturn(UserInitial());
      when(mockUserBloc.stream).thenAnswer((_) => Stream.value(UserInitial()));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();
      
      await tester.tap(find.byType(FloatingActionButton));
      await tester.pumpAndSettle();
      
      // Assert
      expect(find.byType(CreateUserPage), findsOneWidget);
    });
    
    testWidgets('should support pull-to-refresh', (tester) async {
      // Arrange
      final testUsers = TestDataGenerator.generateUsers(2)
          .map((data) => User.fromJson(data))
          .toList();
      
      when(mockUserBloc.state).thenReturn(UserLoaded(testUsers));
      when(mockUserBloc.stream).thenAnswer((_) => Stream.value(UserLoaded(testUsers)));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();
      
      await tester.fling(
        find.byType(ListView),
        const Offset(0, 300),
        1000,
      );
      await tester.pump();
      
      // Assert
      verify(mockUserBloc.add(any)).called(1);
    });
  });
}
```

### 3.3 表单 Widget 测试

```dart
// test/widget/forms/create_user_form_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../../helpers/test_helpers.dart';

void main() {
  group('CreateUserForm Widget Tests', () {
    testWidgets('should display all form fields', (tester) async {
      // Act
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          CreateUserForm(),
        ),
      );
      
      // Assert
      expect(find.byKey(Key('name_field')), findsOneWidget);
      expect(find.byKey(Key('email_field')), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });
    
    testWidgets('should validate required fields', (tester) async {
      // Act
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          CreateUserForm(),
        ),
      );
      
      // Tap submit button without entering data
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();
      
      // Assert
      expect(find.text('Name is required'), findsOneWidget);
      expect(find.text('Email is required'), findsOneWidget);
    });
    
    testWidgets('should validate email format', (tester) async {
      // Act
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          CreateUserForm(),
        ),
      );
      
      // Enter invalid email
      await tester.enterText(find.byKey(Key('name_field')), 'Test User');
      await tester.enterText(find.byKey(Key('email_field')), 'invalid-email');
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();
      
      // Assert
      expect(find.text('Please enter a valid email'), findsOneWidget);
    });
    
    testWidgets('should call onSubmit when form is valid', (tester) async {
      // Arrange
      String? submittedName;
      String? submittedEmail;
      
      // Act
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          CreateUserForm(
            onSubmit: (name, email) {
              submittedName = name;
              submittedEmail = email;
            },
          ),
        ),
      );
      
      await tester.enterText(find.byKey(Key('name_field')), 'Test User');
      await tester.enterText(find.byKey(Key('email_field')), '<EMAIL>');
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();
      
      // Assert
      expect(submittedName, 'Test User');
      expect(submittedEmail, '<EMAIL>');
    });
    
    testWidgets('should disable submit button when loading', (tester) async {
      // Act
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          CreateUserForm(isLoading: true),
        ),
      );
      
      // Assert
      final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(button.onPressed, isNull);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });
    
    testWidgets('should clear form when reset is called', (tester) async {
      // Act
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          CreateUserForm(),
        ),
      );
      
      // Enter data
      await tester.enterText(find.byKey(Key('name_field')), 'Test User');
      await tester.enterText(find.byKey(Key('email_field')), '<EMAIL>');
      
      // Find and tap reset button (if exists)
      final resetButton = find.byKey(Key('reset_button'));
      if (tester.any(resetButton)) {
        await tester.tap(resetButton);
        await tester.pump();
        
        // Assert
        expect(find.text('Test User'), findsNothing);
        expect(find.text('<EMAIL>'), findsNothing);
      }
    });
  });
}
```