# 网络层实现示例

## 1. 核心网络配置

### HTTP客户端配置
```dart
// packages/core/core_network/lib/src/http_client.dart
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:core_config/core_config.dart';
import 'interceptors/auth_interceptor.dart';
import 'interceptors/retry_interceptor.dart';
import 'interceptors/error_interceptor.dart';
import 'interceptors/cache_interceptor.dart';

/// HTTP客户端工厂
@module
abstract class HttpClientModule {
  @singleton
  Dio provideDio(
    AppConfig config,
    AuthInterceptor authInterceptor,
    RetryInterceptor retryInterceptor,
    ErrorInterceptor errorInterceptor,
    CacheInterceptor cacheInterceptor,
  ) {
    final dio = Dio(BaseOptions(
      baseUrl: config.apiBaseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'FlutterApp/${config.appVersion}',
      },
      validateStatus: (status) {
        // 允许所有状态码，让拦截器处理错误
        return status != null && status < 500;
      },
    ));

    // 添加拦截器（顺序很重要）
    dio.interceptors.addAll([
      // 缓存拦截器（最先执行）
      cacheInterceptor,
      
      // 认证拦截器
      authInterceptor,
      
      // 重试拦截器
      retryInterceptor,
      
      // 错误处理拦截器
      errorInterceptor,
      
      // 日志拦截器（最后执行）
      if (config.enableNetworkLogs)
        PrettyDioLogger(
          requestHeader: true,
          requestBody: true,
          responseBody: true,
          responseHeader: false,
          error: true,
          compact: true,
          maxWidth: 90,
        ),
    ]);

    return dio;
  }

  @singleton
  @Named('upload')
  Dio provideUploadDio(
    AppConfig config,
    AuthInterceptor authInterceptor,
  ) {
    final dio = Dio(BaseOptions(
      baseUrl: config.apiBaseUrl,
      connectTimeout: const Duration(minutes: 5),
      receiveTimeout: const Duration(minutes: 5),
      sendTimeout: const Duration(minutes: 5),
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'FlutterApp/${config.appVersion}',
      },
    ));

    dio.interceptors.addAll([
      authInterceptor,
      if (config.enableNetworkLogs)
        PrettyDioLogger(
          requestHeader: true,
          requestBody: false, // 不记录上传文件内容
          responseBody: true,
          responseHeader: false,
          error: true,
          compact: true,
        ),
    ]);

    return dio;
  }
}
```

### 网络拦截器实现

#### 认证拦截器
```dart
// packages/core/core_network/lib/src/interceptors/auth_interceptor.dart
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:core_storage/core_storage.dart';
import '../models/token_model.dart';

@injectable
class AuthInterceptor extends Interceptor {
  AuthInterceptor(this._secureStorage);

  final SecureStorageService _secureStorage;
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    try {
      // 跳过不需要认证的请求
      if (_shouldSkipAuth(options)) {
        handler.next(options);
        return;
      }

      final token = await _secureStorage.read(_tokenKey);
      if (token != null) {
        options.headers['Authorization'] = 'Bearer $token';
      }

      handler.next(options);
    } catch (e) {
      handler.reject(
        DioException(
          requestOptions: options,
          error: 'Failed to add auth token: $e',
          type: DioExceptionType.unknown,
        ),
      );
    }
  }

  @override
  void onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    // 处理401未授权错误
    if (err.response?.statusCode == 401) {
      try {
        final refreshed = await _refreshToken();
        if (refreshed) {
          // 重试原始请求
          final retryResponse = await _retryRequest(err.requestOptions);
          handler.resolve(retryResponse);
          return;
        }
      } catch (e) {
        // 刷新token失败，清除所有认证信息
        await _clearAuthData();
      }
    }

    handler.next(err);
  }

  /// 检查是否应该跳过认证
  bool _shouldSkipAuth(RequestOptions options) {
    final skipAuthPaths = [
      '/auth/login',
      '/auth/register',
      '/auth/refresh',
      '/auth/forgot-password',
    ];

    return skipAuthPaths.any((path) => options.path.contains(path));
  }

  /// 刷新访问令牌
  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await _secureStorage.read(_refreshTokenKey);
      if (refreshToken == null) return false;

      final dio = Dio();
      final response = await dio.post(
        '/auth/refresh',
        data: {'refresh_token': refreshToken},
      );

      if (response.statusCode == 200) {
        final tokenModel = TokenModel.fromJson(response.data);
        await _saveTokens(tokenModel);
        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// 重试原始请求
  Future<Response> _retryRequest(RequestOptions options) async {
    final token = await _secureStorage.read(_tokenKey);
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }

    final dio = Dio();
    return dio.fetch(options);
  }

  /// 保存令牌
  Future<void> _saveTokens(TokenModel tokenModel) async {
    await Future.wait([
      _secureStorage.write(_tokenKey, tokenModel.accessToken),
      _secureStorage.write(_refreshTokenKey, tokenModel.refreshToken),
    ]);
  }

  /// 清除认证数据
  Future<void> _clearAuthData() async {
    await Future.wait([
      _secureStorage.delete(_tokenKey),
      _secureStorage.delete(_refreshTokenKey),
    ]);
  }
}
```

#### 重试拦截器
```dart
// packages/core/core_network/lib/src/interceptors/retry_interceptor.dart
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'dart:math';

@injectable
class RetryInterceptor extends Interceptor {
  RetryInterceptor({
    this.maxRetries = 3,
    this.retryDelay = const Duration(seconds: 1),
    this.retryDelayFactor = 2.0,
  });

  final int maxRetries;
  final Duration retryDelay;
  final double retryDelayFactor;

  @override
  void onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    if (_shouldRetry(err)) {
      final retryCount = err.requestOptions.extra['retry_count'] ?? 0;
      
      if (retryCount < maxRetries) {
        // 计算延迟时间（指数退避）
        final delay = Duration(
          milliseconds: (retryDelay.inMilliseconds * 
              pow(retryDelayFactor, retryCount)).round(),
        );

        await Future.delayed(delay);

        // 更新重试次数
        err.requestOptions.extra['retry_count'] = retryCount + 1;

        try {
          // 重试请求
          final response = await Dio().fetch(err.requestOptions);
          handler.resolve(response);
          return;
        } catch (e) {
          // 重试失败，继续处理错误
        }
      }
    }

    handler.next(err);
  }

  /// 判断是否应该重试
  bool _shouldRetry(DioException err) {
    // 不重试的情况
    if (err.type == DioExceptionType.cancel) return false;
    if (err.requestOptions.method.toUpperCase() != 'GET' && 
        err.requestOptions.method.toUpperCase() != 'HEAD') {
      return false;
    }

    // 可重试的状态码
    final retryStatusCodes = [408, 429, 500, 502, 503, 504];
    if (err.response?.statusCode != null) {
      return retryStatusCodes.contains(err.response!.statusCode);
    }

    // 网络错误可重试
    return err.type == DioExceptionType.connectionTimeout ||
           err.type == DioExceptionType.receiveTimeout ||
           err.type == DioExceptionType.connectionError;
  }
}
```

#### 错误处理拦截器
```dart
// packages/core/core_network/lib/src/interceptors/error_interceptor.dart
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:core_error/core_error.dart';

@injectable
class ErrorInterceptor extends Interceptor {
  @override
  void onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) {
    final failure = _mapDioExceptionToFailure(err);
    
    // 创建新的DioException，包含映射后的错误信息
    final mappedException = DioException(
      requestOptions: err.requestOptions,
      response: err.response,
      type: err.type,
      error: failure,
      message: failure.message,
    );

    handler.next(mappedException);
  }

  /// 将DioException映射为业务错误
  Failure _mapDioExceptionToFailure(DioException exception) {
    switch (exception.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const NetworkFailure(
          message: '网络连接超时，请检查网络设置',
          code: 'NETWORK_TIMEOUT',
        );

      case DioExceptionType.connectionError:
        return const NetworkFailure(
          message: '网络连接失败，请检查网络设置',
          code: 'NETWORK_ERROR',
        );

      case DioExceptionType.badResponse:
        return _handleBadResponse(exception.response!);

      case DioExceptionType.cancel:
        return const NetworkFailure(
          message: '请求已取消',
          code: 'REQUEST_CANCELLED',
        );

      case DioExceptionType.unknown:
      default:
        return NetworkFailure(
          message: '未知网络错误: ${exception.message}',
          code: 'UNKNOWN_ERROR',
        );
    }
  }

  /// 处理HTTP响应错误
  Failure _handleBadResponse(Response response) {
    final statusCode = response.statusCode ?? 0;
    final data = response.data;

    // 尝试从响应中提取错误信息
    String message = '请求失败';
    String code = 'HTTP_$statusCode';

    if (data is Map<String, dynamic>) {
      message = data['message'] ?? data['error'] ?? message;
      code = data['code'] ?? code;
    }

    switch (statusCode) {
      case 400:
        return ValidationFailure(
          message: message.isNotEmpty ? message : '请求参数错误',
          code: code,
          details: data is Map ? data : null,
        );

      case 401:
        return const AuthFailure(
          message: '认证失败，请重新登录',
          code: 'UNAUTHORIZED',
        );

      case 403:
        return const AuthFailure(
          message: '权限不足，无法访问该资源',
          code: 'FORBIDDEN',
        );

      case 404:
        return ServerFailure(
          message: message.isNotEmpty ? message : '请求的资源不存在',
          code: code,
        );

      case 422:
        return ValidationFailure(
          message: message.isNotEmpty ? message : '数据验证失败',
          code: code,
          details: data is Map ? data : null,
        );

      case 429:
        return const NetworkFailure(
          message: '请求过于频繁，请稍后再试',
          code: 'RATE_LIMITED',
        );

      case 500:
      case 502:
      case 503:
      case 504:
        return ServerFailure(
          message: message.isNotEmpty ? message : '服务器错误，请稍后再试',
          code: code,
        );

      default:
        return ServerFailure(
          message: message.isNotEmpty ? message : '服务器响应异常',
          code: code,
        );
    }
  }
}
```

#### 缓存拦截器
```dart
// packages/core/core_network/lib/src/interceptors/cache_interceptor.dart
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:core_storage/core_storage.dart';
import 'dart:convert';

@injectable
class CacheInterceptor extends Interceptor {
  CacheInterceptor(this._cacheStorage);

  final CacheStorageService _cacheStorage;
  static const Duration _defaultCacheDuration = Duration(minutes: 5);

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    // 只缓存GET请求
    if (options.method.toUpperCase() != 'GET') {
      handler.next(options);
      return;
    }

    // 检查是否启用缓存
    final cacheEnabled = options.extra['cache_enabled'] ?? false;
    if (!cacheEnabled) {
      handler.next(options);
      return;
    }

    try {
      final cacheKey = _generateCacheKey(options);
      final cachedData = await _cacheStorage.get(cacheKey);
      
      if (cachedData != null) {
        final cachedResponse = _createResponseFromCache(options, cachedData);
        handler.resolve(cachedResponse);
        return;
      }
    } catch (e) {
      // 缓存读取失败，继续正常请求
    }

    handler.next(options);
  }

  @override
  void onResponse(
    Response response,
    ResponseInterceptorHandler handler,
  ) async {
    // 只缓存成功的GET请求
    if (response.requestOptions.method.toUpperCase() == 'GET' &&
        response.statusCode == 200) {
      
      final cacheEnabled = response.requestOptions.extra['cache_enabled'] ?? false;
      if (cacheEnabled) {
        try {
          await _cacheResponse(response);
        } catch (e) {
          // 缓存写入失败，不影响正常响应
        }
      }
    }

    handler.next(response);
  }

  /// 生成缓存键
  String _generateCacheKey(RequestOptions options) {
    final uri = options.uri.toString();
    final headers = options.headers.toString();
    final queryParams = options.queryParameters.toString();
    
    final combined = '$uri$headers$queryParams';
    return 'http_cache_${combined.hashCode}';
  }

  /// 缓存响应
  Future<void> _cacheResponse(Response response) async {
    final cacheKey = _generateCacheKey(response.requestOptions);
    final cacheDuration = Duration(
      milliseconds: response.requestOptions.extra['cache_duration'] ?? 
                   _defaultCacheDuration.inMilliseconds,
    );

    final cacheData = {
      'data': response.data,
      'headers': response.headers.map,
      'statusCode': response.statusCode,
      'statusMessage': response.statusMessage,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    await _cacheStorage.set(
      cacheKey,
      jsonEncode(cacheData),
      duration: cacheDuration,
    );
  }

  /// 从缓存创建响应
  Response _createResponseFromCache(
    RequestOptions options,
    String cachedData,
  ) {
    final cacheMap = jsonDecode(cachedData) as Map<String, dynamic>;
    
    return Response(
      data: cacheMap['data'],
      headers: Headers.fromMap(
        Map<String, List<String>>.from(cacheMap['headers'] ?? {}),
      ),
      statusCode: cacheMap['statusCode'] ?? 200,
      statusMessage: cacheMap['statusMessage'] ?? 'OK',
      requestOptions: options,
      extra: {'from_cache': true},
    );
  }
}
```

## 2. API服务基类

### 基础API服务
```dart
// packages/core/core_network/lib/src/api_service.dart
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:core_error/core_error.dart';
import 'models/api_response.dart';
import 'models/pagination_response.dart';

@injectable
class ApiService {
  ApiService(this._dio);

  final Dio _dio;

  /// GET请求
  Future<Result<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    bool enableCache = false,
    Duration? cacheDuration,
    T Function(dynamic)? fromJson,
    CancelToken? cancelToken,
  }) async {
    try {
      final options = Options(
        headers: headers,
        extra: {
          'cache_enabled': enableCache,
          if (cacheDuration != null) 'cache_duration': cacheDuration.inMilliseconds,
        },
      );

      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );

      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return Result.failure(_extractFailure(e));
    } catch (e) {
      return Result.failure(UnknownFailure('Unexpected error: $e'));
    }
  }

  /// POST请求
  Future<Result<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    T Function(dynamic)? fromJson,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
      );

      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return Result.failure(_extractFailure(e));
    } catch (e) {
      return Result.failure(UnknownFailure('Unexpected error: $e'));
    }
  }

  /// PUT请求
  Future<Result<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    T Function(dynamic)? fromJson,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
        cancelToken: cancelToken,
      );

      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return Result.failure(_extractFailure(e));
    } catch (e) {
      return Result.failure(UnknownFailure('Unexpected error: $e'));
    }
  }

  /// DELETE请求
  Future<Result<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    T Function(dynamic)? fromJson,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
        cancelToken: cancelToken,
      );

      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return Result.failure(_extractFailure(e));
    } catch (e) {
      return Result.failure(UnknownFailure('Unexpected error: $e'));
    }
  }

  /// 文件上传
  Future<Result<T>> upload<T>(
    String path,
    FormData formData, {
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    T Function(dynamic)? fromJson,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: formData,
        queryParameters: queryParameters,
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data',
            ...?headers,
          },
        ),
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
      );

      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return Result.failure(_extractFailure(e));
    } catch (e) {
      return Result.failure(UnknownFailure('Unexpected error: $e'));
    }
  }

  /// 文件下载
  Future<Result<void>> download(
    String urlPath,
    String savePath, {
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      await _dio.download(
        urlPath,
        savePath,
        queryParameters: queryParameters,
        options: Options(headers: headers),
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );

      return const Result.success(null);
    } on DioException catch (e) {
      return Result.failure(_extractFailure(e));
    } catch (e) {
      return Result.failure(UnknownFailure('Unexpected error: $e'));
    }
  }

  /// 分页请求
  Future<Result<PaginationResponse<T>>> getPaginated<T>(
    String path, {
    required int page,
    required int pageSize,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    bool enableCache = false,
    required T Function(dynamic) fromJson,
    CancelToken? cancelToken,
  }) async {
    final params = {
      'page': page,
      'page_size': pageSize,
      ...?queryParameters,
    };

    final result = await get<Map<String, dynamic>>(
      path,
      queryParameters: params,
      headers: headers,
      enableCache: enableCache,
      cancelToken: cancelToken,
    );

    return result.fold(
      onSuccess: (data) {
        try {
          final paginationResponse = PaginationResponse<T>.fromJson(
            data,
            (json) => fromJson(json),
          );
          return Result.success(paginationResponse);
        } catch (e) {
          return Result.failure(ParseFailure('Failed to parse pagination response: $e'));
        }
      },
      onFailure: (failure) => Result.failure(failure),
    );
  }

  /// 处理响应
  Result<T> _handleResponse<T>(
    Response response,
    T Function(dynamic)? fromJson,
  ) {
    try {
      if (response.statusCode == 204) {
        // No Content
        return const Result.success(null);
      }

      final data = response.data;
      
      if (fromJson != null) {
        final parsedData = fromJson(data);
        return Result.success(parsedData);
      } else {
        return Result.success(data as T);
      }
    } catch (e) {
      return Result.failure(ParseFailure('Failed to parse response: $e'));
    }
  }

  /// 提取错误信息
  Failure _extractFailure(DioException exception) {
    if (exception.error is Failure) {
      return exception.error as Failure;
    }
    
    return NetworkFailure(
      message: exception.message ?? 'Network error occurred',
      code: 'DIO_ERROR',
    );
  }
}
```

## 3. 具体API服务实现

### 认证API服务
```dart
// packages/features/feature_auth/lib/src/data/datasources/auth_remote_datasource.dart
import 'package:injectable/injectable.dart';
import 'package:core_network/core_network.dart';
import 'package:core_error/core_error.dart';
import '../models/auth_request_model.dart';
import '../models/auth_response_model.dart';
import '../models/user_model.dart';

abstract class AuthRemoteDataSource {
  Future<Result<AuthResponseModel>> login(LoginRequestModel request);
  Future<Result<AuthResponseModel>> register(RegisterRequestModel request);
  Future<Result<AuthResponseModel>> refreshToken(String refreshToken);
  Future<Result<UserModel>> getCurrentUser();
  Future<Result<void>> logout();
  Future<Result<void>> forgotPassword(String email);
  Future<Result<void>> resetPassword(ResetPasswordRequestModel request);
  Future<Result<void>> changePassword(ChangePasswordRequestModel request);
}

@Injectable(as: AuthRemoteDataSource)
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  AuthRemoteDataSourceImpl(this._apiService);

  final ApiService _apiService;

  @override
  Future<Result<AuthResponseModel>> login(LoginRequestModel request) async {
    return _apiService.post<AuthResponseModel>(
      '/auth/login',
      data: request.toJson(),
      fromJson: (json) => AuthResponseModel.fromJson(json),
    );
  }

  @override
  Future<Result<AuthResponseModel>> register(RegisterRequestModel request) async {
    return _apiService.post<AuthResponseModel>(
      '/auth/register',
      data: request.toJson(),
      fromJson: (json) => AuthResponseModel.fromJson(json),
    );
  }

  @override
  Future<Result<AuthResponseModel>> refreshToken(String refreshToken) async {
    return _apiService.post<AuthResponseModel>(
      '/auth/refresh',
      data: {'refresh_token': refreshToken},
      fromJson: (json) => AuthResponseModel.fromJson(json),
    );
  }

  @override
  Future<Result<UserModel>> getCurrentUser() async {
    return _apiService.get<UserModel>(
      '/auth/me',
      enableCache: true,
      cacheDuration: const Duration(minutes: 5),
      fromJson: (json) => UserModel.fromJson(json['user']),
    );
  }

  @override
  Future<Result<void>> logout() async {
    return _apiService.post<void>('/auth/logout');
  }

  @override
  Future<Result<void>> forgotPassword(String email) async {
    return _apiService.post<void>(
      '/auth/forgot-password',
      data: {'email': email},
    );
  }

  @override
  Future<Result<void>> resetPassword(ResetPasswordRequestModel request) async {
    return _apiService.post<void>(
      '/auth/reset-password',
      data: request.toJson(),
    );
  }

  @override
  Future<Result<void>> changePassword(ChangePasswordRequestModel request) async {
    return _apiService.put<void>(
      '/auth/change-password',
      data: request.toJson(),
    );
  }
}
```

### 用户API服务
```dart
// packages/features/feature_user/lib/src/data/datasources/user_remote_datasource.dart
import 'package:injectable/injectable.dart';
import 'package:core_network/core_network.dart';
import 'package:core_error/core_error.dart';
import '../models/user_model.dart';
import '../models/user_detail_model.dart';
import '../models/update_profile_request_model.dart';

abstract class UserRemoteDataSource {
  Future<Result<PaginationResponse<UserModel>>> getUsers({
    required int page,
    required int pageSize,
    String? search,
  });
  
  Future<Result<UserDetailModel>> getUserDetail(String userId);
  Future<Result<UserModel>> updateProfile(UpdateProfileRequestModel request);
  Future<Result<void>> followUser(String userId);
  Future<Result<void>> unfollowUser(String userId);
  Future<Result<PaginationResponse<UserModel>>> getFollowers({
    required String userId,
    required int page,
    required int pageSize,
  });
  Future<Result<PaginationResponse<UserModel>>> getFollowing({
    required String userId,
    required int page,
    required int pageSize,
  });
  Future<Result<String>> uploadAvatar(String filePath);
}

@Injectable(as: UserRemoteDataSource)
class UserRemoteDataSourceImpl implements UserRemoteDataSource {
  UserRemoteDataSourceImpl(this._apiService);

  final ApiService _apiService;

  @override
  Future<Result<PaginationResponse<UserModel>>> getUsers({
    required int page,
    required int pageSize,
    String? search,
  }) async {
    final queryParams = <String, dynamic>{
      if (search != null && search.isNotEmpty) 'search': search,
    };

    return _apiService.getPaginated<UserModel>(
      '/users',
      page: page,
      pageSize: pageSize,
      queryParameters: queryParams,
      enableCache: search == null, // 只缓存非搜索请求
      fromJson: (json) => UserModel.fromJson(json),
    );
  }

  @override
  Future<Result<UserDetailModel>> getUserDetail(String userId) async {
    return _apiService.get<UserDetailModel>(
      '/users/$userId',
      enableCache: true,
      cacheDuration: const Duration(minutes: 2),
      fromJson: (json) => UserDetailModel.fromJson(json),
    );
  }

  @override
  Future<Result<UserModel>> updateProfile(UpdateProfileRequestModel request) async {
    return _apiService.put<UserModel>(
      '/users/profile',
      data: request.toJson(),
      fromJson: (json) => UserModel.fromJson(json['user']),
    );
  }

  @override
  Future<Result<void>> followUser(String userId) async {
    return _apiService.post<void>('/users/$userId/follow');
  }

  @override
  Future<Result<void>> unfollowUser(String userId) async {
    return _apiService.delete<void>('/users/$userId/follow');
  }

  @override
  Future<Result<PaginationResponse<UserModel>>> getFollowers({
    required String userId,
    required int page,
    required int pageSize,
  }) async {
    return _apiService.getPaginated<UserModel>(
      '/users/$userId/followers',
      page: page,
      pageSize: pageSize,
      enableCache: true,
      fromJson: (json) => UserModel.fromJson(json),
    );
  }

  @override
  Future<Result<PaginationResponse<UserModel>>> getFollowing({
    required String userId,
    required int page,
    required int pageSize,
  }) async {
    return _apiService.getPaginated<UserModel>(
      '/users/$userId/following',
      page: page,
      pageSize: pageSize,
      enableCache: true,
      fromJson: (json) => UserModel.fromJson(json),
    );
  }

  @override
  Future<Result<String>> uploadAvatar(String filePath) async {
    final formData = FormData.fromMap({
      'avatar': await MultipartFile.fromFile(filePath),
    });

    final result = await _apiService.upload<Map<String, dynamic>>(
      '/users/avatar',
      formData,
      fromJson: (json) => json as Map<String, dynamic>,
    );

    return result.fold(
      onSuccess: (data) => Result.success(data['avatar_url'] as String),
      onFailure: (failure) => Result.failure(failure),
    );
  }
}
```

## 4. 网络状态监控

### 网络连接监控服务
```dart
// packages/core/core_network/lib/src/network_monitor.dart
import 'package:injectable/injectable.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:async';
import 'dart:io';

@singleton
class NetworkMonitor {
  NetworkMonitor() {
    _init();
  }

  final Connectivity _connectivity = Connectivity();
  final StreamController<NetworkStatus> _statusController = 
      StreamController<NetworkStatus>.broadcast();
  
  NetworkStatus _currentStatus = NetworkStatus.unknown;
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  /// 网络状态流
  Stream<NetworkStatus> get statusStream => _statusController.stream;

  /// 当前网络状态
  NetworkStatus get currentStatus => _currentStatus;

  /// 是否已连接
  bool get isConnected => _currentStatus == NetworkStatus.connected;

  /// 初始化
  void _init() {
    _connectivitySubscription = _connectivity.onConnectivityChanged
        .listen(_onConnectivityChanged);
    
    // 检查初始状态
    _checkInitialStatus();
  }

  /// 检查初始网络状态
  Future<void> _checkInitialStatus() async {
    final result = await _connectivity.checkConnectivity();
    await _onConnectivityChanged(result);
  }

  /// 处理网络连接变化
  Future<void> _onConnectivityChanged(ConnectivityResult result) async {
    NetworkStatus newStatus;
    
    switch (result) {
      case ConnectivityResult.none:
        newStatus = NetworkStatus.disconnected;
        break;
      case ConnectivityResult.mobile:
      case ConnectivityResult.wifi:
      case ConnectivityResult.ethernet:
        // 进一步验证网络连接
        final hasInternet = await _hasInternetConnection();
        newStatus = hasInternet 
            ? NetworkStatus.connected 
            : NetworkStatus.disconnected;
        break;
      default:
        newStatus = NetworkStatus.unknown;
    }

    if (_currentStatus != newStatus) {
      _currentStatus = newStatus;
      _statusController.add(newStatus);
    }
  }

  /// 验证是否有实际的网络连接
  Future<bool> _hasInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com')
          .timeout(const Duration(seconds: 5));
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// 手动检查网络状态
  Future<NetworkStatus> checkStatus() async {
    final result = await _connectivity.checkConnectivity();
    await _onConnectivityChanged(result);
    return _currentStatus;
  }

  /// 释放资源
  void dispose() {
    _connectivitySubscription?.cancel();
    _statusController.close();
  }
}

/// 网络状态枚举
enum NetworkStatus {
  connected,
  disconnected,
  unknown,
}
```

这个网络层实现提供了：

1. **完整的HTTP客户端配置**：包含认证、重试、错误处理、缓存等拦截器
2. **统一的API服务基类**：支持各种HTTP方法、文件上传下载、分页请求
3. **具体的API服务实现**：认证和用户相关的API服务
4. **网络状态监控**：实时监控网络连接状态
5. **错误处理机制**：将网络错误映射为业务错误
6. **缓存支持**：GET请求的智能缓存
7. **请求重试**：自动重试失败的请求
8. **认证管理**：自动添加认证头和token刷新

所有实现都遵循依赖注入和错误处理的最佳实践。