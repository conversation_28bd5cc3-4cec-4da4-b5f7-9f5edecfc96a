# 错误处理和日志系统实现示例

## 1. 错误类型定义

### 基础错误类
```dart
// packages/core/core_error/lib/src/failures.dart
import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  const Failure({
    required this.message,
    this.code,
    this.details,
    this.stackTrace,
    this.timestamp,
  });

  final String message;
  final String? code;
  final Map<String, dynamic>? details;
  final StackTrace? stackTrace;
  final DateTime? timestamp;

  @override
  List<Object?> get props => [message, code, details];

  /// 获取用户友好的错误消息
  String get userMessage => message;

  /// 获取技术错误详情
  String get technicalMessage => details?.toString() ?? message;

  /// 是否为可重试的错误
  bool get isRetryable => false;

  /// 是否需要用户操作
  bool get requiresUserAction => false;

  /// 转换为Map用于日志记录
  Map<String, dynamic> toMap() {
    return {
      'type': runtimeType.toString(),
      'message': message,
      'code': code,
      'details': details,
      'timestamp': timestamp?.toIso8601String(),
      'stackTrace': stackTrace?.toString(),
    };
  }
}

/// 网络相关错误
class NetworkFailure extends Failure {
  const NetworkFailure({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    super.timestamp,
    this.statusCode,
    this.requestUrl,
    this.requestMethod,
  });

  final int? statusCode;
  final String? requestUrl;
  final String? requestMethod;

  @override
  bool get isRetryable => statusCode == null || statusCode! >= 500 || statusCode == 408 || statusCode == 429;

  @override
  String get userMessage {
    if (statusCode == null) {
      return '网络连接失败，请检查网络设置';
    }
    
    switch (statusCode!) {
      case 400:
        return '请求参数错误';
      case 401:
        return '身份验证失败，请重新登录';
      case 403:
        return '没有权限访问此资源';
      case 404:
        return '请求的资源不存在';
      case 408:
        return '请求超时，请稍后重试';
      case 429:
        return '请求过于频繁，请稍后重试';
      case 500:
        return '服务器内部错误';
      case 502:
        return '网关错误';
      case 503:
        return '服务暂时不可用';
      default:
        return '网络请求失败（$statusCode）';
    }
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map.addAll({
      'statusCode': statusCode,
      'requestUrl': requestUrl,
      'requestMethod': requestMethod,
    });
    return map;
  }
}

/// 认证相关错误
class AuthFailure extends Failure {
  const AuthFailure({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    super.timestamp,
    this.authType,
  });

  final String? authType;

  @override
  bool get requiresUserAction => true;

  @override
  String get userMessage {
    switch (code) {
      case 'invalid_credentials':
        return '用户名或密码错误';
      case 'account_locked':
        return '账户已被锁定，请联系客服';
      case 'account_disabled':
        return '账户已被禁用';
      case 'token_expired':
        return '登录已过期，请重新登录';
      case 'invalid_token':
        return '登录信息无效，请重新登录';
      case 'biometric_not_available':
        return '生物识别功能不可用';
      case 'biometric_not_enrolled':
        return '请先设置生物识别';
      default:
        return '身份验证失败';
    }
  }
}

/// 验证相关错误
class ValidationFailure extends Failure {
  const ValidationFailure({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    super.timestamp,
    this.field,
    this.validationErrors,
  });

  final String? field;
  final Map<String, List<String>>? validationErrors;

  @override
  bool get requiresUserAction => true;

  @override
  String get userMessage {
    if (validationErrors != null && validationErrors!.isNotEmpty) {
      final errors = <String>[];
      validationErrors!.forEach((field, fieldErrors) {
        errors.addAll(fieldErrors.map((error) => '$field: $error'));
      });
      return errors.join('\n');
    }
    return message;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map.addAll({
      'field': field,
      'validationErrors': validationErrors,
    });
    return map;
  }
}

/// 服务器相关错误
class ServerFailure extends Failure {
  const ServerFailure({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    super.timestamp,
    this.statusCode,
  });

  final int? statusCode;

  @override
  bool get isRetryable => statusCode == null || statusCode! >= 500;

  @override
  String get userMessage {
    switch (statusCode) {
      case 500:
        return '服务器内部错误，请稍后重试';
      case 502:
        return '网关错误，请稍后重试';
      case 503:
        return '服务暂时不可用，请稍后重试';
      case 504:
        return '网关超时，请稍后重试';
      default:
        return '服务器错误，请稍后重试';
    }
  }
}

/// 缓存相关错误
class CacheFailure extends Failure {
  const CacheFailure({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    super.timestamp,
    this.operation,
  });

  final String? operation;

  @override
  String get userMessage => '数据缓存错误';
}

/// 存储相关错误
class StorageFailure extends Failure {
  const StorageFailure({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    super.timestamp,
    this.storageType,
  });

  final String? storageType;

  @override
  String get userMessage {
    switch (code) {
      case 'storage_full':
        return '存储空间不足';
      case 'permission_denied':
        return '没有存储权限';
      case 'file_not_found':
        return '文件不存在';
      default:
        return '存储操作失败';
    }
  }
}

/// 业务逻辑错误
class BusinessFailure extends Failure {
  const BusinessFailure({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    super.timestamp,
    this.businessRule,
  });

  final String? businessRule;

  @override
  bool get requiresUserAction => true;

  @override
  String get userMessage => message;
}

/// 未知错误
class UnknownFailure extends Failure {
  const UnknownFailure({
    required super.message,
    super.code,
    super.details,
    super.stackTrace,
    super.timestamp,
    this.originalException,
  });

  final Object? originalException;

  @override
  String get userMessage => '发生未知错误，请稍后重试';

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map.addAll({
      'originalException': originalException?.toString(),
    });
    return map;
  }
}
```

### 错误处理器
```dart
// packages/core/core_error/lib/src/error_handler.dart
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'failures.dart';
import 'logger_service.dart';

abstract class ErrorHandler {
  Failure handleError(Object error, [StackTrace? stackTrace]);
  Failure handleDioError(DioException error);
  void reportError(Failure failure);
}

@Injectable(as: ErrorHandler)
class ErrorHandlerImpl implements ErrorHandler {
  ErrorHandlerImpl(this._logger);

  final LoggerService _logger;

  @override
  Failure handleError(Object error, [StackTrace? stackTrace]) {
    final timestamp = DateTime.now();
    
    if (error is DioException) {
      return handleDioError(error);
    }
    
    if (error is Failure) {
      return error;
    }
    
    // 处理常见的Dart异常
    if (error is FormatException) {
      return ValidationFailure(
        message: 'Data format error: ${error.message}',
        code: 'format_error',
        details: {'source': error.source},
        stackTrace: stackTrace,
        timestamp: timestamp,
      );
    }
    
    if (error is ArgumentError) {
      return ValidationFailure(
        message: 'Invalid argument: ${error.message}',
        code: 'invalid_argument',
        details: {'name': error.name, 'value': error.invalidValue},
        stackTrace: stackTrace,
        timestamp: timestamp,
      );
    }
    
    if (error is StateError) {
      return BusinessFailure(
        message: 'Invalid state: ${error.message}',
        code: 'invalid_state',
        stackTrace: stackTrace,
        timestamp: timestamp,
      );
    }
    
    if (error is TypeError) {
      return ValidationFailure(
        message: 'Type error: ${error.toString()}',
        code: 'type_error',
        stackTrace: stackTrace,
        timestamp: timestamp,
      );
    }
    
    // 未知错误
    return UnknownFailure(
      message: error.toString(),
      code: 'unknown_error',
      originalException: error,
      stackTrace: stackTrace,
      timestamp: timestamp,
    );
  }

  @override
  Failure handleDioError(DioException error) {
    final timestamp = DateTime.now();
    
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return NetworkFailure(
          message: 'Request timeout',
          code: 'timeout',
          statusCode: 408,
          requestUrl: error.requestOptions.uri.toString(),
          requestMethod: error.requestOptions.method,
          stackTrace: error.stackTrace,
          timestamp: timestamp,
        );
        
      case DioExceptionType.badResponse:
        return _handleResponseError(error, timestamp);
        
      case DioExceptionType.cancel:
        return NetworkFailure(
          message: 'Request cancelled',
          code: 'cancelled',
          requestUrl: error.requestOptions.uri.toString(),
          requestMethod: error.requestOptions.method,
          stackTrace: error.stackTrace,
          timestamp: timestamp,
        );
        
      case DioExceptionType.connectionError:
        return NetworkFailure(
          message: 'Connection error',
          code: 'connection_error',
          details: {'error': error.error?.toString()},
          requestUrl: error.requestOptions.uri.toString(),
          requestMethod: error.requestOptions.method,
          stackTrace: error.stackTrace,
          timestamp: timestamp,
        );
        
      case DioExceptionType.badCertificate:
        return NetworkFailure(
          message: 'SSL certificate error',
          code: 'ssl_error',
          requestUrl: error.requestOptions.uri.toString(),
          requestMethod: error.requestOptions.method,
          stackTrace: error.stackTrace,
          timestamp: timestamp,
        );
        
      case DioExceptionType.unknown:
      default:
        return NetworkFailure(
          message: 'Unknown network error',
          code: 'unknown_network_error',
          details: {'error': error.error?.toString()},
          requestUrl: error.requestOptions.uri.toString(),
          requestMethod: error.requestOptions.method,
          stackTrace: error.stackTrace,
          timestamp: timestamp,
        );
    }
  }

  Failure _handleResponseError(DioException error, DateTime timestamp) {
    final statusCode = error.response?.statusCode;
    final responseData = error.response?.data;
    
    // 尝试解析服务器返回的错误信息
    String message = 'HTTP Error $statusCode';
    String? code;
    Map<String, dynamic>? details;
    
    if (responseData is Map<String, dynamic>) {
      message = responseData['message'] ?? responseData['error'] ?? message;
      code = responseData['code']?.toString();
      details = responseData;
    }
    
    // 根据状态码返回不同类型的错误
    if (statusCode == 401) {
      return AuthFailure(
        message: message,
        code: code ?? 'unauthorized',
        details: details,
        stackTrace: error.stackTrace,
        timestamp: timestamp,
      );
    }
    
    if (statusCode == 422) {
      Map<String, List<String>>? validationErrors;
      if (responseData is Map<String, dynamic> && responseData.containsKey('errors')) {
        final errors = responseData['errors'];
        if (errors is Map<String, dynamic>) {
          validationErrors = errors.map((key, value) {
            if (value is List) {
              return MapEntry(key, value.map((e) => e.toString()).toList());
            }
            return MapEntry(key, [value.toString()]);
          });
        }
      }
      
      return ValidationFailure(
        message: message,
        code: code ?? 'validation_error',
        details: details,
        validationErrors: validationErrors,
        stackTrace: error.stackTrace,
        timestamp: timestamp,
      );
    }
    
    if (statusCode != null && statusCode >= 500) {
      return ServerFailure(
        message: message,
        code: code ?? 'server_error',
        details: details,
        statusCode: statusCode,
        stackTrace: error.stackTrace,
        timestamp: timestamp,
      );
    }
    
    return NetworkFailure(
      message: message,
      code: code ?? 'http_error',
      details: details,
      statusCode: statusCode,
      requestUrl: error.requestOptions.uri.toString(),
      requestMethod: error.requestOptions.method,
      stackTrace: error.stackTrace,
      timestamp: timestamp,
    );
  }

  @override
  void reportError(Failure failure) {
    // 记录错误日志
    _logger.error(
      'Error occurred: ${failure.message}',
      error: failure,
      stackTrace: failure.stackTrace,
      extra: failure.toMap(),
    );
    
    // 根据错误类型决定是否上报到崩溃分析服务
    if (_shouldReportToCrashlytics(failure)) {
      _reportToCrashlytics(failure);
    }
  }

  bool _shouldReportToCrashlytics(Failure failure) {
    // 不上报用户输入错误和认证错误
    if (failure is ValidationFailure || failure is AuthFailure) {
      return false;
    }
    
    // 不上报网络连接错误
    if (failure is NetworkFailure && failure.statusCode == null) {
      return false;
    }
    
    return true;
  }

  void _reportToCrashlytics(Failure failure) {
    // 这里可以集成Firebase Crashlytics或其他崩溃分析服务
    // FirebaseCrashlytics.instance.recordError(
    //   failure,
    //   failure.stackTrace,
    //   information: failure.toMap(),
    // );
  }
}
```

## 2. 日志系统实现

### 日志服务接口
```dart
// packages/core/core_error/lib/src/logger_service.dart
import 'package:injectable/injectable.dart';

enum LogLevel {
  debug(0),
  info(1),
  warning(2),
  error(3),
  fatal(4);

  const LogLevel(this.value);
  final int value;

  bool operator >=(LogLevel other) => value >= other.value;
  bool operator >(LogLevel other) => value > other.value;
  bool operator <=(LogLevel other) => value <= other.value;
  bool operator <(LogLevel other) => value < other.value;
}

abstract class LoggerService {
  void debug(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? extra});
  void info(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? extra});
  void warning(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? extra});
  void error(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? extra});
  void fatal(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? extra});
  
  void setLevel(LogLevel level);
  LogLevel getLevel();
  
  Future<void> flush();
  Future<List<LogEntry>> getLogs({LogLevel? level, DateTime? since, int? limit});
  Future<void> clearLogs();
}

class LogEntry {
  LogEntry({
    required this.level,
    required this.message,
    required this.timestamp,
    this.error,
    this.stackTrace,
    this.extra,
    this.tag,
  });

  final LogLevel level;
  final String message;
  final DateTime timestamp;
  final Object? error;
  final StackTrace? stackTrace;
  final Map<String, dynamic>? extra;
  final String? tag;

  Map<String, dynamic> toMap() {
    return {
      'level': level.name,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'error': error?.toString(),
      'stackTrace': stackTrace?.toString(),
      'extra': extra,
      'tag': tag,
    };
  }

  factory LogEntry.fromMap(Map<String, dynamic> map) {
    return LogEntry(
      level: LogLevel.values.firstWhere((l) => l.name == map['level']),
      message: map['message'],
      timestamp: DateTime.parse(map['timestamp']),
      error: map['error'],
      stackTrace: map['stackTrace'] != null ? StackTrace.fromString(map['stackTrace']) : null,
      extra: map['extra'],
      tag: map['tag'],
    );
  }
}
```

### 日志服务实现
```dart
// packages/core/core_error/lib/src/logger_service_impl.dart
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:injectable/injectable.dart';
import 'package:path_provider/path_provider.dart';
import 'package:core_config/core_config.dart';
import 'logger_service.dart';

@Injectable(as: LoggerService)
class LoggerServiceImpl implements LoggerService {
  LoggerServiceImpl(this._appConfig) {
    _initialize();
  }

  final AppConfig _appConfig;
  LogLevel _currentLevel = LogLevel.info;
  final List<LogEntry> _memoryLogs = [];
  final StreamController<LogEntry> _logController = StreamController<LogEntry>.broadcast();
  
  File? _logFile;
  final int _maxMemoryLogs = 1000;
  final int _maxFileLogs = 10000;
  
  Timer? _flushTimer;
  final List<LogEntry> _pendingLogs = [];

  void _initialize() {
    _currentLevel = _parseLogLevel(_appConfig.logLevel);
    
    if (_appConfig.enableLogging) {
      _initializeFileLogging();
      _startPeriodicFlush();
    }
  }

  LogLevel _parseLogLevel(String level) {
    switch (level.toLowerCase()) {
      case 'debug':
        return LogLevel.debug;
      case 'info':
        return LogLevel.info;
      case 'warning':
      case 'warn':
        return LogLevel.warning;
      case 'error':
        return LogLevel.error;
      case 'fatal':
        return LogLevel.fatal;
      default:
        return LogLevel.info;
    }
  }

  Future<void> _initializeFileLogging() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logDirectory = Directory('${directory.path}/logs');
      
      if (!await logDirectory.exists()) {
        await logDirectory.create(recursive: true);
      }
      
      final fileName = 'app_${DateTime.now().toIso8601String().split('T')[0]}.log';
      _logFile = File('${logDirectory.path}/$fileName');
      
      // 清理旧日志文件
      _cleanupOldLogs(logDirectory);
    } catch (e) {
      print('Failed to initialize file logging: $e');
    }
  }

  Future<void> _cleanupOldLogs(Directory logDirectory) async {
    try {
      final files = await logDirectory.list().toList();
      final logFiles = files.whereType<File>().where((f) => f.path.endsWith('.log')).toList();
      
      // 保留最近7天的日志
      final cutoffDate = DateTime.now().subtract(const Duration(days: 7));
      
      for (final file in logFiles) {
        final stat = await file.stat();
        if (stat.modified.isBefore(cutoffDate)) {
          await file.delete();
        }
      }
    } catch (e) {
      print('Failed to cleanup old logs: $e');
    }
  }

  void _startPeriodicFlush() {
    _flushTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      flush();
    });
  }

  @override
  void debug(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? extra}) {
    _log(LogLevel.debug, message, error: error, stackTrace: stackTrace, extra: extra);
  }

  @override
  void info(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? extra}) {
    _log(LogLevel.info, message, error: error, stackTrace: stackTrace, extra: extra);
  }

  @override
  void warning(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? extra}) {
    _log(LogLevel.warning, message, error: error, stackTrace: stackTrace, extra: extra);
  }

  @override
  void error(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? extra}) {
    _log(LogLevel.error, message, error: error, stackTrace: stackTrace, extra: extra);
  }

  @override
  void fatal(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? extra}) {
    _log(LogLevel.fatal, message, error: error, stackTrace: stackTrace, extra: extra);
  }

  void _log(
    LogLevel level,
    String message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? extra,
  }) {
    if (level < _currentLevel) return;
    
    final entry = LogEntry(
      level: level,
      message: message,
      timestamp: DateTime.now(),
      error: error,
      stackTrace: stackTrace,
      extra: extra,
    );
    
    // 添加到内存日志
    _memoryLogs.add(entry);
    if (_memoryLogs.length > _maxMemoryLogs) {
      _memoryLogs.removeAt(0);
    }
    
    // 添加到待写入队列
    if (_appConfig.enableLogging) {
      _pendingLogs.add(entry);
    }
    
    // 发送到流
    _logController.add(entry);
    
    // 控制台输出（仅在调试模式）
    if (_appConfig.environment.isDevelopment) {
      _printToConsole(entry);
    }
    
    // 立即刷新严重错误
    if (level >= LogLevel.error) {
      flush();
    }
  }

  void _printToConsole(LogEntry entry) {
    final timestamp = entry.timestamp.toIso8601String();
    final levelStr = entry.level.name.toUpperCase().padRight(7);
    
    print('[$timestamp] $levelStr ${entry.message}');
    
    if (entry.error != null) {
      print('Error: ${entry.error}');
    }
    
    if (entry.stackTrace != null) {
      print('StackTrace: ${entry.stackTrace}');
    }
    
    if (entry.extra != null && entry.extra!.isNotEmpty) {
      print('Extra: ${jsonEncode(entry.extra)}');
    }
  }

  @override
  void setLevel(LogLevel level) {
    _currentLevel = level;
  }

  @override
  LogLevel getLevel() {
    return _currentLevel;
  }

  @override
  Future<void> flush() async {
    if (_pendingLogs.isEmpty || _logFile == null) return;
    
    try {
      final logsToWrite = List<LogEntry>.from(_pendingLogs);
      _pendingLogs.clear();
      
      final buffer = StringBuffer();
      for (final entry in logsToWrite) {
        buffer.writeln(jsonEncode(entry.toMap()));
      }
      
      await _logFile!.writeAsString(buffer.toString(), mode: FileMode.append);
      
      // 检查文件大小，如果太大则轮转
      await _rotateLogFileIfNeeded();
    } catch (e) {
      print('Failed to flush logs: $e');
    }
  }

  Future<void> _rotateLogFileIfNeeded() async {
    if (_logFile == null) return;
    
    try {
      final stat = await _logFile!.stat();
      const maxFileSize = 10 * 1024 * 1024; // 10MB
      
      if (stat.size > maxFileSize) {
        final directory = _logFile!.parent;
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final newName = '${_logFile!.path}.$timestamp';
        
        await _logFile!.rename(newName);
        
        // 创建新的日志文件
        _logFile = File(_logFile!.path);
      }
    } catch (e) {
      print('Failed to rotate log file: $e');
    }
  }

  @override
  Future<List<LogEntry>> getLogs({LogLevel? level, DateTime? since, int? limit}) async {
    var logs = List<LogEntry>.from(_memoryLogs);
    
    // 从文件加载更多日志
    if (_logFile != null && await _logFile!.exists()) {
      try {
        final content = await _logFile!.readAsString();
        final lines = content.split('\n').where((line) => line.trim().isNotEmpty);
        
        for (final line in lines) {
          try {
            final map = jsonDecode(line) as Map<String, dynamic>;
            final entry = LogEntry.fromMap(map);
            
            // 避免重复添加内存中已有的日志
            if (!_memoryLogs.any((l) => l.timestamp == entry.timestamp && l.message == entry.message)) {
              logs.add(entry);
            }
          } catch (e) {
            // 忽略解析错误的行
          }
        }
      } catch (e) {
        print('Failed to read log file: $e');
      }
    }
    
    // 排序
    logs.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    
    // 过滤
    if (level != null) {
      logs = logs.where((log) => log.level >= level).toList();
    }
    
    if (since != null) {
      logs = logs.where((log) => log.timestamp.isAfter(since)).toList();
    }
    
    // 限制数量
    if (limit != null && logs.length > limit) {
      logs = logs.sublist(logs.length - limit);
    }
    
    return logs;
  }

  @override
  Future<void> clearLogs() async {
    _memoryLogs.clear();
    _pendingLogs.clear();
    
    if (_logFile != null && await _logFile!.exists()) {
      try {
        await _logFile!.delete();
        await _initializeFileLogging();
      } catch (e) {
        print('Failed to clear log file: $e');
      }
    }
  }

  Stream<LogEntry> get logStream => _logController.stream;

  void dispose() {
    _flushTimer?.cancel();
    flush();
    _logController.close();
  }
}
```

## 3. 全局错误处理

### 全局错误捕获器
```dart
// packages/core/core_error/lib/src/global_error_handler.dart
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'error_handler.dart';
import 'logger_service.dart';
import 'failures.dart';

@singleton
class GlobalErrorHandler {
  GlobalErrorHandler(
    this._errorHandler,
    this._logger,
  );

  final ErrorHandler _errorHandler;
  final LoggerService _logger;
  
  final StreamController<Failure> _errorController = StreamController<Failure>.broadcast();

  void initialize() {
    // 捕获Flutter框架错误
    FlutterError.onError = (FlutterErrorDetails details) {
      final failure = _errorHandler.handleError(details.exception, details.stack);
      _handleError(failure, details);
    };
    
    // 捕获异步错误
    PlatformDispatcher.instance.onError = (error, stack) {
      final failure = _errorHandler.handleError(error, stack);
      _handleError(failure);
      return true;
    };
    
    // 捕获Zone错误
    runZonedGuarded(
      () {
        // 应用启动代码
      },
      (error, stack) {
        final failure = _errorHandler.handleError(error, stack);
        _handleError(failure);
      },
    );
  }

  void _handleError(Failure failure, [FlutterErrorDetails? details]) {
    // 记录错误
    _errorHandler.reportError(failure);
    
    // 发送到错误流
    _errorController.add(failure);
    
    // 在调试模式下打印详细信息
    if (kDebugMode && details != null) {
      FlutterError.presentError(details);
    }
  }

  /// 手动报告错误
  void reportError(Object error, [StackTrace? stackTrace]) {
    final failure = _errorHandler.handleError(error, stackTrace);
    _handleError(failure);
  }

  /// 错误流
  Stream<Failure> get errorStream => _errorController.stream;

  void dispose() {
    _errorController.close();
  }
}
```

### 错误边界Widget
```dart
// packages/core/core_error/lib/src/error_boundary.dart
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'failures.dart';
import 'global_error_handler.dart';

class ErrorBoundary extends StatefulWidget {
  const ErrorBoundary({
    super.key,
    required this.child,
    this.onError,
    this.errorWidgetBuilder,
  });

  final Widget child;
  final void Function(Failure failure)? onError;
  final Widget Function(BuildContext context, Failure failure)? errorWidgetBuilder;

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  Failure? _error;

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return widget.errorWidgetBuilder?.call(context, _error!) ??
          DefaultErrorWidget(failure: _error!);
    }

    return ErrorWidget.builder = (FlutterErrorDetails details) {
      final globalErrorHandler = getIt<GlobalErrorHandler>();
      globalErrorHandler.reportError(details.exception, details.stack);
      
      // 这里可以返回自定义的错误Widget
      return DefaultErrorWidget(
        failure: UnknownFailure(
          message: details.exception.toString(),
          stackTrace: details.stack,
          timestamp: DateTime.now(),
        ),
      );
    };
    
    return widget.child;
  }

  void _handleError(Failure failure) {
    setState(() {
      _error = failure;
    });
    
    widget.onError?.call(failure);
  }
}

/// 默认错误显示Widget
class DefaultErrorWidget extends StatelessWidget {
  const DefaultErrorWidget({
    super.key,
    required this.failure,
    this.onRetry,
  });

  final Failure failure;
  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getErrorIcon(),
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              '出现错误',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              failure.userMessage,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            if (failure.isRetryable && onRetry != null)
              ElevatedButton(
                onPressed: onRetry,
                child: const Text('重试'),
              ),
            if (kDebugMode) ...
              [
                const SizedBox(height: 16),
                ExpansionTile(
                  title: const Text('错误详情'),
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Text(
                        failure.technicalMessage,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ),
                  ],
                ),
              ],
          ],
        ),
      ),
    );
  }

  IconData _getErrorIcon() {
    if (failure is NetworkFailure) {
      return Icons.wifi_off;
    }
    if (failure is AuthFailure) {
      return Icons.lock;
    }
    if (failure is ValidationFailure) {
      return Icons.error_outline;
    }
    return Icons.error;
  }
}
```

## 4. 错误报告和分析

### 崩溃报告服务
```dart
// packages/core/core_error/lib/src/crash_reporting_service.dart
import 'package:injectable/injectable.dart';
import 'package:core_config/core_config.dart';
import 'failures.dart';
import 'logger_service.dart';

abstract class CrashReportingService {
  Future<void> initialize();
  Future<void> reportError(Failure failure);
  Future<void> reportCrash(Object error, StackTrace? stackTrace);
  void setUserIdentifier(String userId);
  void setCustomKey(String key, String value);
  void log(String message);
}

@Injectable(as: CrashReportingService)
class CrashReportingServiceImpl implements CrashReportingService {
  CrashReportingServiceImpl(
    this._appConfig,
    this._logger,
  );

  final AppConfig _appConfig;
  final LoggerService _logger;
  
  final Map<String, String> _customKeys = {};
  String? _userId;

  @override
  Future<void> initialize() async {
    if (!_appConfig.enableCrashReporting) {
      _logger.info('Crash reporting is disabled');
      return;
    }
    
    try {
      // 这里可以初始化Firebase Crashlytics或其他崩溃报告服务
      // await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
      
      _logger.info('Crash reporting initialized');
    } catch (e) {
      _logger.error('Failed to initialize crash reporting', error: e);
    }
  }

  @override
  Future<void> reportError(Failure failure) async {
    if (!_appConfig.enableCrashReporting) return;
    
    try {
      // 设置自定义键值
      await _setCustomKeys({
        'error_type': failure.runtimeType.toString(),
        'error_code': failure.code ?? 'unknown',
        'timestamp': failure.timestamp?.toIso8601String() ?? DateTime.now().toIso8601String(),
        'environment': _appConfig.environment.value,
        'app_version': _appConfig.appVersion,
      });
      
      // 记录错误
      // await FirebaseCrashlytics.instance.recordError(
      //   failure,
      //   failure.stackTrace,
      //   information: failure.toMap(),
      //   fatal: failure is UnknownFailure,
      // );
      
      _logger.info('Error reported to crash reporting service', extra: failure.toMap());
    } catch (e) {
      _logger.error('Failed to report error to crash reporting service', error: e);
    }
  }

  @override
  Future<void> reportCrash(Object error, StackTrace? stackTrace) async {
    if (!_appConfig.enableCrashReporting) return;
    
    try {
      // await FirebaseCrashlytics.instance.recordError(
      //   error,
      //   stackTrace,
      //   fatal: true,
      // );
      
      _logger.fatal('Crash reported', error: error, stackTrace: stackTrace);
    } catch (e) {
      _logger.error('Failed to report crash', error: e);
    }
  }

  @override
  void setUserIdentifier(String userId) {
    _userId = userId;
    
    if (!_appConfig.enableCrashReporting) return;
    
    try {
      // FirebaseCrashlytics.instance.setUserIdentifier(userId);
      _logger.info('User identifier set for crash reporting', extra: {'userId': userId});
    } catch (e) {
      _logger.error('Failed to set user identifier', error: e);
    }
  }

  @override
  void setCustomKey(String key, String value) {
    _customKeys[key] = value;
    
    if (!_appConfig.enableCrashReporting) return;
    
    try {
      // FirebaseCrashlytics.instance.setCustomKey(key, value);
    } catch (e) {
      _logger.error('Failed to set custom key', error: e);
    }
  }

  @override
  void log(String message) {
    if (!_appConfig.enableCrashReporting) return;
    
    try {
      // FirebaseCrashlytics.instance.log(message);
    } catch (e) {
      _logger.error('Failed to log message to crash reporting', error: e);
    }
  }

  Future<void> _setCustomKeys(Map<String, String> keys) async {
    for (final entry in keys.entries) {
      setCustomKey(entry.key, entry.value);
    }
  }
}
```

这个错误处理和日志系统实现提供了：

1. **完整的错误类型体系**：网络、认证、验证、服务器、缓存、存储、业务逻辑等错误类型
2. **智能错误处理器**：自动将异常转换为结构化的错误对象
3. **多级日志系统**：内存、文件、控制台多种输出方式
4. **全局错误捕获**：捕获Flutter框架、异步和Zone错误
5. **错误边界组件**：防止错误导致应用崩溃
6. **崩溃报告集成**：支持Firebase Crashlytics等服务
7. **用户友好的错误显示**：根据错误类型显示合适的用户消息
8. **开发调试支持**：详细的错误信息和堆栈跟踪

所有实现都遵循Clean Architecture原则和依赖注入模式。