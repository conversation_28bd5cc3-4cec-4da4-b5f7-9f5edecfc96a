# Data Layer 实现示例

## 1. 数据模型 (Data Models)

### 用户数据模型
```dart
// packages/features/feature_auth/lib/src/data/models/user_model.dart
import 'package:json_annotation/json_annotation.dart';
import 'package:shared_models/shared_models.dart';

part 'user_model.g.dart';

/// 用户数据模型
@JsonSerializable()
class UserModel {
  const UserModel({
    required this.id,
    required this.email,
    required this.name,
    required this.avatar,
    required this.isEmailVerified,
    required this.createdAt,
    this.phone,
    this.bio,
    this.lastLoginAt,
  });

  final String id;
  final String email;
  final String name;
  final String? phone;
  final String? avatar;
  final String? bio;
  @Json<PERSON>ey(name: 'email_verified')
  final bool isEmailVerified;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @Json<PERSON>ey(name: 'last_login_at')
  final DateTime? lastLoginAt;

  /// 从JSON创建
  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  /// 转换为实体
  UserEntity toEntity() {
    return UserEntity(
      id: id,
      email: email,
      name: name,
      phone: phone,
      avatar: avatar,
      bio: bio,
      isEmailVerified: isEmailVerified,
      createdAt: createdAt,
      lastLoginAt: lastLoginAt,
    );
  }

  /// 从实体创建
  factory UserModel.fromEntity(UserEntity entity) {
    return UserModel(
      id: entity.id,
      email: entity.email,
      name: entity.name,
      phone: entity.phone,
      avatar: entity.avatar,
      bio: entity.bio,
      isEmailVerified: entity.isEmailVerified,
      createdAt: entity.createdAt,
      lastLoginAt: entity.lastLoginAt,
    );
  }

  /// 复制方法
  UserModel copyWith({
    String? id,
    String? email,
    String? name,
    String? phone,
    String? avatar,
    String? bio,
    bool? isEmailVerified,
    DateTime? createdAt,
    DateTime? lastLoginAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      bio: bio ?? this.bio,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }
}
```

### 认证数据模型
```dart
// packages/features/feature_auth/lib/src/data/models/auth_model.dart
import 'package:json_annotation/json_annotation.dart';
import 'package:shared_models/shared_models.dart';

part 'auth_model.g.dart';

/// 认证响应模型
@JsonSerializable()
class AuthResponseModel {
  const AuthResponseModel({
    required this.accessToken,
    required this.refreshToken,
    required this.expiresIn,
    required this.tokenType,
    this.scope,
  });

  @JsonKey(name: 'access_token')
  final String accessToken;
  @JsonKey(name: 'refresh_token')
  final String refreshToken;
  @JsonKey(name: 'expires_in')
  final int expiresIn; // 秒数
  @JsonKey(name: 'token_type')
  final String tokenType;
  final String? scope;

  factory AuthResponseModel.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$AuthResponseModelToJson(this);

  /// 转换为实体
  AuthEntity toEntity() {
    final expiresAt = DateTime.now().add(Duration(seconds: expiresIn));
    return AuthEntity(
      accessToken: accessToken,
      refreshToken: refreshToken,
      expiresAt: expiresAt,
      tokenType: tokenType,
      scope: scope,
    );
  }
}

/// 登录请求模型
@JsonSerializable()
class LoginRequestModel {
  const LoginRequestModel({
    required this.email,
    required this.password,
    this.grantType = 'password',
  });

  final String email;
  final String password;
  @JsonKey(name: 'grant_type')
  final String grantType;

  factory LoginRequestModel.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginRequestModelToJson(this);
}

/// 注册请求模型
@JsonSerializable()
class RegisterRequestModel {
  const RegisterRequestModel({
    required this.email,
    required this.password,
    required this.name,
  });

  final String email;
  final String password;
  final String name;

  factory RegisterRequestModel.fromJson(Map<String, dynamic> json) =>
      _$RegisterRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$RegisterRequestModelToJson(this);
}
```

## 2. 数据源 (Data Sources)

### 远程数据源
```dart
// packages/features/feature_auth/lib/src/data/datasources/auth_remote_datasource.dart
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';
import '../models/auth_model.dart';
import '../models/user_model.dart';

part 'auth_remote_datasource.g.dart';

/// 认证远程数据源接口
abstract class AuthRemoteDataSource {
  Future<AuthResponseModel> login(LoginRequestModel request);
  Future<AuthResponseModel> register(RegisterRequestModel request);
  Future<AuthResponseModel> refreshToken(String refreshToken);
  Future<void> logout();
  Future<UserModel> getCurrentUser();
  Future<void> forgotPassword(String email);
  Future<void> resetPassword(String token, String newPassword);
}

/// 认证远程数据源实现
@RestApi()
@injectable
abstract class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  @factoryMethod
  factory AuthRemoteDataSourceImpl(Dio dio) = _AuthRemoteDataSourceImpl;

  @override
  @POST('/auth/login')
  Future<AuthResponseModel> login(@Body() LoginRequestModel request);

  @override
  @POST('/auth/register')
  Future<AuthResponseModel> register(@Body() RegisterRequestModel request);

  @override
  @POST('/auth/refresh')
  Future<AuthResponseModel> refreshToken(
    @Field('refresh_token') String refreshToken,
  );

  @override
  @POST('/auth/logout')
  Future<void> logout();

  @override
  @GET('/user/me')
  Future<UserModel> getCurrentUser();

  @override
  @POST('/auth/forgot-password')
  Future<void> forgotPassword(@Field('email') String email);

  @override
  @POST('/auth/reset-password')
  Future<void> resetPassword(
    @Field('token') String token,
    @Field('password') String newPassword,
  );
}
```

### 本地数据源
```dart
// packages/features/feature_auth/lib/src/data/datasources/auth_local_datasource.dart
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/auth_model.dart';
import '../models/user_model.dart';

/// 认证本地数据源接口
abstract class AuthLocalDataSource {
  Future<void> saveAuth(AuthResponseModel auth);
  Future<AuthResponseModel?> getAuth();
  Future<void> clearAuth();
  Future<void> saveUser(UserModel user);
  Future<UserModel?> getUser();
  Future<void> clearUser();
  Future<bool> isFirstLaunch();
  Future<void> setFirstLaunchCompleted();
}

/// 认证本地数据源实现
@Injectable(as: AuthLocalDataSource)
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  const AuthLocalDataSourceImpl(
    this._secureStorage,
    this._sharedPreferences,
  );

  final FlutterSecureStorage _secureStorage;
  final SharedPreferences _sharedPreferences;

  static const String _authKey = 'auth_data';
  static const String _userKey = 'user_data';
  static const String _firstLaunchKey = 'first_launch';

  @override
  Future<void> saveAuth(AuthResponseModel auth) async {
    final authJson = jsonEncode(auth.toJson());
    await _secureStorage.write(key: _authKey, value: authJson);
  }

  @override
  Future<AuthResponseModel?> getAuth() async {
    try {
      final authJson = await _secureStorage.read(key: _authKey);
      if (authJson == null) return null;
      
      final authMap = jsonDecode(authJson) as Map<String, dynamic>;
      return AuthResponseModel.fromJson(authMap);
    } catch (e) {
      // 如果解析失败，清除损坏的数据
      await clearAuth();
      return null;
    }
  }

  @override
  Future<void> clearAuth() async {
    await _secureStorage.delete(key: _authKey);
  }

  @override
  Future<void> saveUser(UserModel user) async {
    final userJson = jsonEncode(user.toJson());
    await _sharedPreferences.setString(_userKey, userJson);
  }

  @override
  Future<UserModel?> getUser() async {
    try {
      final userJson = _sharedPreferences.getString(_userKey);
      if (userJson == null) return null;
      
      final userMap = jsonDecode(userJson) as Map<String, dynamic>;
      return UserModel.fromJson(userMap);
    } catch (e) {
      // 如果解析失败，清除损坏的数据
      await clearUser();
      return null;
    }
  }

  @override
  Future<void> clearUser() async {
    await _sharedPreferences.remove(_userKey);
  }

  @override
  Future<bool> isFirstLaunch() async {
    return !_sharedPreferences.containsKey(_firstLaunchKey);
  }

  @override
  Future<void> setFirstLaunchCompleted() async {
    await _sharedPreferences.setBool(_firstLaunchKey, true);
  }
}
```

### 数据库数据源
```dart
// packages/features/feature_auth/lib/src/data/datasources/user_database_datasource.dart
import 'package:core_database/core_database.dart';
import 'package:injectable/injectable.dart';
import '../models/user_model.dart';

/// 用户数据库数据源接口
abstract class UserDatabaseDataSource {
  Future<void> insertUser(UserModel user);
  Future<UserModel?> getUserById(String id);
  Future<List<UserModel>> getAllUsers();
  Future<void> updateUser(UserModel user);
  Future<void> deleteUser(String id);
  Future<void> clearAllUsers();
  Stream<UserModel?> watchUser(String id);
  Future<List<UserModel>> searchUsers(String query);
}

/// 用户数据库数据源实现
@Injectable(as: UserDatabaseDataSource)
class UserDatabaseDataSourceImpl implements UserDatabaseDataSource {
  const UserDatabaseDataSourceImpl(this._database);

  final AppDatabase _database;

  @override
  Future<void> insertUser(UserModel user) async {
    final userCompanion = UsersCompanion.insert(
      id: user.id,
      email: user.email,
      name: user.name,
      phone: Value(user.phone),
      avatar: Value(user.avatar),
      bio: Value(user.bio),
      isEmailVerified: user.isEmailVerified,
      createdAt: user.createdAt,
      lastLoginAt: Value(user.lastLoginAt),
    );
    
    await _database.userDao.insertUser(userCompanion);
  }

  @override
  Future<UserModel?> getUserById(String id) async {
    final user = await _database.userDao.getUserById(id);
    return user?.toModel();
  }

  @override
  Future<List<UserModel>> getAllUsers() async {
    final users = await _database.userDao.getAllUsers();
    return users.map((user) => user.toModel()).toList();
  }

  @override
  Future<void> updateUser(UserModel user) async {
    final userCompanion = UsersCompanion(
      id: Value(user.id),
      email: Value(user.email),
      name: Value(user.name),
      phone: Value(user.phone),
      avatar: Value(user.avatar),
      bio: Value(user.bio),
      isEmailVerified: Value(user.isEmailVerified),
      createdAt: Value(user.createdAt),
      lastLoginAt: Value(user.lastLoginAt),
    );
    
    await _database.userDao.updateUser(userCompanion);
  }

  @override
  Future<void> deleteUser(String id) async {
    await _database.userDao.deleteUser(id);
  }

  @override
  Future<void> clearAllUsers() async {
    await _database.userDao.clearAllUsers();
  }

  @override
  Stream<UserModel?> watchUser(String id) {
    return _database.userDao
        .watchUser(id)
        .map((user) => user?.toModel());
  }

  @override
  Future<List<UserModel>> searchUsers(String query) async {
    final users = await _database.userDao.searchUsers(query);
    return users.map((user) => user.toModel()).toList();
  }
}

/// 扩展方法：数据库实体转换为数据模型
extension UserEntityExtension on User {
  UserModel toModel() {
    return UserModel(
      id: id,
      email: email,
      name: name,
      phone: phone,
      avatar: avatar,
      bio: bio,
      isEmailVerified: isEmailVerified,
      createdAt: createdAt,
      lastLoginAt: lastLoginAt,
    );
  }
}
```

## 3. 仓储实现 (Repository Implementation)

### 认证仓储实现
```dart
// packages/features/feature_auth/lib/src/data/repositories/auth_repository_impl.dart
import 'package:injectable/injectable.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_models/shared_models.dart';
import 'package:core_error/core_error.dart';
import 'package:core_network/core_network.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_remote_datasource.dart';
import '../datasources/auth_local_datasource.dart';
import '../models/auth_model.dart';

/// 认证仓储实现
@Injectable(as: AuthRepository)
class AuthRepositoryImpl implements AuthRepository {
  const AuthRepositoryImpl(
    this._remoteDataSource,
    this._localDataSource,
    this._connectivity,
    this._networkInfo,
  );

  final AuthRemoteDataSource _remoteDataSource;
  final AuthLocalDataSource _localDataSource;
  final Connectivity _connectivity;
  final NetworkInfo _networkInfo;

  @override
  Future<Result<AuthEntity>> loginWithEmail({
    required EmailVO email,
    required PasswordVO password,
  }) async {
    try {
      // 检查网络连接
      if (!await _networkInfo.isConnected) {
        return Result.failure(
          NetworkFailure(message: 'No internet connection'),
        );
      }

      // 创建请求模型
      final request = LoginRequestModel(
        email: email.value,
        password: password.value,
      );

      // 调用远程API
      final authResponse = await _remoteDataSource.login(request);
      
      // 保存到本地
      await _localDataSource.saveAuth(authResponse);
      
      // 转换为实体并返回
      return Result.success(authResponse.toEntity());
    } on DioException catch (e) {
      return Result.failure(_handleDioError(e));
    } catch (e) {
      return Result.failure(
        UnknownFailure(message: 'Login failed: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Result<AuthEntity>> loginWithPhone({
    required String phone,
    required String verificationCode,
  }) async {
    try {
      if (!await _networkInfo.isConnected) {
        return Result.failure(
          NetworkFailure(message: 'No internet connection'),
        );
      }

      // TODO: 实现手机号登录逻辑
      throw UnimplementedError('Phone login not implemented yet');
    } on DioException catch (e) {
      return Result.failure(_handleDioError(e));
    } catch (e) {
      return Result.failure(
        UnknownFailure(message: 'Phone login failed: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Result<AuthEntity>> loginWithProvider({
    required AuthProvider provider,
    required String token,
  }) async {
    try {
      if (!await _networkInfo.isConnected) {
        return Result.failure(
          NetworkFailure(message: 'No internet connection'),
        );
      }

      // TODO: 实现第三方登录逻辑
      throw UnimplementedError('Provider login not implemented yet');
    } on DioException catch (e) {
      return Result.failure(_handleDioError(e));
    } catch (e) {
      return Result.failure(
        UnknownFailure(message: 'Provider login failed: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Result<AuthEntity>> register({
    required EmailVO email,
    required PasswordVO password,
    required String name,
  }) async {
    try {
      if (!await _networkInfo.isConnected) {
        return Result.failure(
          NetworkFailure(message: 'No internet connection'),
        );
      }

      final request = RegisterRequestModel(
        email: email.value,
        password: password.value,
        name: name,
      );

      final authResponse = await _remoteDataSource.register(request);
      await _localDataSource.saveAuth(authResponse);
      
      return Result.success(authResponse.toEntity());
    } on DioException catch (e) {
      return Result.failure(_handleDioError(e));
    } catch (e) {
      return Result.failure(
        UnknownFailure(message: 'Registration failed: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Result<AuthEntity>> refreshToken(String refreshToken) async {
    try {
      if (!await _networkInfo.isConnected) {
        return Result.failure(
          NetworkFailure(message: 'No internet connection'),
        );
      }

      final authResponse = await _remoteDataSource.refreshToken(refreshToken);
      await _localDataSource.saveAuth(authResponse);
      
      return Result.success(authResponse.toEntity());
    } on DioException catch (e) {
      return Result.failure(_handleDioError(e));
    } catch (e) {
      return Result.failure(
        UnknownFailure(message: 'Token refresh failed: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Result<void>> logout() async {
    try {
      // 尝试调用远程登出API（即使网络不可用也要清除本地数据）
      if (await _networkInfo.isConnected) {
        try {
          await _remoteDataSource.logout();
        } catch (e) {
          // 忽略远程登出错误，继续清除本地数据
        }
      }

      // 清除本地认证数据
      await _localDataSource.clearAuth();
      await _localDataSource.clearUser();
      
      return const Result.success(null);
    } catch (e) {
      return Result.failure(
        UnknownFailure(message: 'Logout failed: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Result<void>> forgotPassword(EmailVO email) async {
    try {
      if (!await _networkInfo.isConnected) {
        return Result.failure(
          NetworkFailure(message: 'No internet connection'),
        );
      }

      await _remoteDataSource.forgotPassword(email.value);
      return const Result.success(null);
    } on DioException catch (e) {
      return Result.failure(_handleDioError(e));
    } catch (e) {
      return Result.failure(
        UnknownFailure(message: 'Forgot password failed: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Result<void>> resetPassword({
    required String resetToken,
    required PasswordVO newPassword,
  }) async {
    try {
      if (!await _networkInfo.isConnected) {
        return Result.failure(
          NetworkFailure(message: 'No internet connection'),
        );
      }

      await _remoteDataSource.resetPassword(resetToken, newPassword.value);
      return const Result.success(null);
    } on DioException catch (e) {
      return Result.failure(_handleDioError(e));
    } catch (e) {
      return Result.failure(
        UnknownFailure(message: 'Reset password failed: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Result<bool>> isAuthenticated() async {
    try {
      final auth = await _localDataSource.getAuth();
      if (auth == null) return const Result.success(false);
      
      final authEntity = auth.toEntity();
      return Result.success(!authEntity.isExpired);
    } catch (e) {
      return Result.failure(
        UnknownFailure(message: 'Auth check failed: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Result<AuthEntity?>> getCurrentAuth() async {
    try {
      final auth = await _localDataSource.getAuth();
      return Result.success(auth?.toEntity());
    } catch (e) {
      return Result.failure(
        UnknownFailure(message: 'Get auth failed: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Result<void>> sendPhoneVerification(String phone) async {
    try {
      if (!await _networkInfo.isConnected) {
        return Result.failure(
          NetworkFailure(message: 'No internet connection'),
        );
      }

      // TODO: 实现发送手机验证码逻辑
      throw UnimplementedError('Send phone verification not implemented yet');
    } on DioException catch (e) {
      return Result.failure(_handleDioError(e));
    } catch (e) {
      return Result.failure(
        UnknownFailure(message: 'Send verification failed: ${e.toString()}'),
      );
    }
  }

  /// 处理Dio错误
  Failure _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return NetworkFailure(message: 'Connection timeout');
      
      case DioExceptionType.connectionError:
        return NetworkFailure(message: 'Connection error');
      
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message = error.response?.data?['message'] ?? 'Server error';
        
        if (statusCode == 401) {
          return AuthFailure(message: 'Invalid credentials');
        } else if (statusCode == 422) {
          return ValidationFailure(message: message);
        } else if (statusCode != null && statusCode >= 500) {
          return ServerFailure(message: 'Server error: $statusCode');
        } else {
          return ServerFailure(message: message);
        }
      
      case DioExceptionType.cancel:
        return NetworkFailure(message: 'Request cancelled');
      
      case DioExceptionType.unknown:
      default:
        return UnknownFailure(message: error.message ?? 'Unknown error');
    }
  }
}
```

## 4. 网络拦截器

### 认证拦截器
```dart
// packages/core/core_network/lib/src/interceptors/auth_interceptor.dart
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:convert';

/// 认证拦截器
@injectable
class AuthInterceptor extends Interceptor {
  AuthInterceptor(this._secureStorage);

  final FlutterSecureStorage _secureStorage;
  static const String _authKey = 'auth_data';

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    try {
      // 获取存储的认证信息
      final authJson = await _secureStorage.read(key: _authKey);
      if (authJson != null) {
        final authData = jsonDecode(authJson) as Map<String, dynamic>;
        final accessToken = authData['access_token'] as String?;
        final tokenType = authData['token_type'] as String? ?? 'Bearer';
        
        if (accessToken != null) {
          options.headers['Authorization'] = '$tokenType $accessToken';
        }
      }
    } catch (e) {
      // 如果获取认证信息失败，继续请求但不添加认证头
    }
    
    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // 如果是401错误，清除本地认证信息
    if (err.response?.statusCode == 401) {
      _clearAuth();
    }
    
    handler.next(err);
  }

  Future<void> _clearAuth() async {
    try {
      await _secureStorage.delete(key: _authKey);
    } catch (e) {
      // 忽略清除错误
    }
  }
}
```

### 重试拦截器
```dart
// packages/core/core_network/lib/src/interceptors/retry_interceptor.dart
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

/// 重试拦截器
@injectable
class RetryInterceptor extends Interceptor {
  RetryInterceptor({
    this.maxRetries = 3,
    this.retryDelay = const Duration(seconds: 1),
  });

  final int maxRetries;
  final Duration retryDelay;

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final extra = err.requestOptions.extra;
    final retryCount = extra['retry_count'] as int? ?? 0;

    // 检查是否应该重试
    if (_shouldRetry(err) && retryCount < maxRetries) {
      // 增加重试计数
      err.requestOptions.extra['retry_count'] = retryCount + 1;
      
      // 等待一段时间后重试
      await Future.delayed(retryDelay * (retryCount + 1));
      
      try {
        // 重新发起请求
        final response = await Dio().fetch(err.requestOptions);
        handler.resolve(response);
        return;
      } catch (e) {
        // 重试失败，继续处理原始错误
      }
    }
    
    handler.next(err);
  }

  /// 判断是否应该重试
  bool _shouldRetry(DioException error) {
    // 只对网络错误和5xx服务器错误进行重试
    return error.type == DioExceptionType.connectionTimeout ||
           error.type == DioExceptionType.sendTimeout ||
           error.type == DioExceptionType.receiveTimeout ||
           error.type == DioExceptionType.connectionError ||
           (error.response?.statusCode != null &&
            error.response!.statusCode! >= 500);
  }
}
```

## 5. 数据转换器

### JSON转换器
```dart
// packages/core/core_network/lib/src/converters/json_converter.dart
import 'package:dio/dio.dart';
import 'dart:convert';

/// 自定义JSON转换器
class CustomJsonConverter {
  /// 将响应数据转换为指定类型
  static T fromJson<T>(
    dynamic json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    if (json is String) {
      return fromJsonT(jsonDecode(json) as Map<String, dynamic>);
    } else if (json is Map<String, dynamic>) {
      return fromJsonT(json);
    } else {
      throw ArgumentError('Invalid JSON format');
    }
  }

  /// 将对象转换为JSON字符串
  static String toJson<T>(
    T object,
    Map<String, dynamic> Function(T) toJsonT,
  ) {
    return jsonEncode(toJsonT(object));
  }

  /// 处理分页响应
  static PaginatedResponse<T> fromPaginatedJson<T>(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    final data = (json['data'] as List<dynamic>)
        .map((item) => fromJsonT(item as Map<String, dynamic>))
        .toList();
    
    return PaginatedResponse<T>(
      data: data,
      currentPage: json['current_page'] as int,
      lastPage: json['last_page'] as int,
      perPage: json['per_page'] as int,
      total: json['total'] as int,
    );
  }
}

/// 分页响应模型
class PaginatedResponse<T> {
  const PaginatedResponse({
    required this.data,
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.total,
  });

  final List<T> data;
  final int currentPage;
  final int lastPage;
  final int perPage;
  final int total;

  bool get hasNextPage => currentPage < lastPage;
  bool get hasPreviousPage => currentPage > 1;
}
```

这个Data Layer实现展示了：

1. **数据模型**：JSON序列化和实体转换
2. **数据源**：远程API、本地存储、数据库访问
3. **仓储实现**：协调多个数据源，处理错误
4. **网络拦截器**：认证、重试、日志等横切关注点
5. **数据转换**：JSON处理和分页响应

所有实现都遵循依赖倒置原则，通过接口与Domain Layer交互。