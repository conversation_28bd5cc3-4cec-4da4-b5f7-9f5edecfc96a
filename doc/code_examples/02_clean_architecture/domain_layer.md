# Domain Layer 实现示例

## 1. 实体 (Entities)

### 用户实体
```dart
// packages/shared/shared_models/lib/src/user/user_entity.dart
import 'package:equatable/equatable.dart';

/// 用户实体 - 业务核心对象
class UserEntity extends Equatable {
  const UserEntity({
    required this.id,
    required this.email,
    required this.name,
    required this.avatar,
    required this.isEmailVerified,
    required this.createdAt,
    this.phone,
    this.bio,
    this.lastLoginAt,
  });

  final String id;
  final String email;
  final String name;
  final String? phone;
  final String? avatar;
  final String? bio;
  final bool isEmailVerified;
  final DateTime createdAt;
  final DateTime? lastLoginAt;

  /// 业务逻辑：检查用户是否完整
  bool get isProfileComplete {
    return name.isNotEmpty && 
           avatar != null && 
           avatar!.isNotEmpty &&
           isEmailVerified;
  }

  /// 业务逻辑：检查用户是否活跃
  bool get isActiveUser {
    if (lastLoginAt == null) return false;
    final daysSinceLastLogin = DateTime.now().difference(lastLoginAt!).inDays;
    return daysSinceLastLogin <= 30;
  }

  /// 业务逻辑：获取显示名称
  String get displayName {
    if (name.isNotEmpty) return name;
    return email.split('@').first;
  }

  /// 复制方法
  UserEntity copyWith({
    String? id,
    String? email,
    String? name,
    String? phone,
    String? avatar,
    String? bio,
    bool? isEmailVerified,
    DateTime? createdAt,
    DateTime? lastLoginAt,
  }) {
    return UserEntity(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      bio: bio ?? this.bio,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        name,
        phone,
        avatar,
        bio,
        isEmailVerified,
        createdAt,
        lastLoginAt,
      ];
}
```

### 认证实体
```dart
// packages/shared/shared_models/lib/src/auth/auth_entity.dart
import 'package:equatable/equatable.dart';

/// 认证状态实体
class AuthEntity extends Equatable {
  const AuthEntity({
    required this.accessToken,
    required this.refreshToken,
    required this.expiresAt,
    required this.tokenType,
    this.scope,
  });

  final String accessToken;
  final String refreshToken;
  final DateTime expiresAt;
  final String tokenType;
  final String? scope;

  /// 业务逻辑：检查token是否过期
  bool get isExpired {
    return DateTime.now().isAfter(expiresAt);
  }

  /// 业务逻辑：检查token是否即将过期（5分钟内）
  bool get isExpiringSoon {
    final fiveMinutesFromNow = DateTime.now().add(const Duration(minutes: 5));
    return fiveMinutesFromNow.isAfter(expiresAt);
  }

  /// 业务逻辑：获取Authorization header值
  String get authorizationHeader {
    return '$tokenType $accessToken';
  }

  AuthEntity copyWith({
    String? accessToken,
    String? refreshToken,
    DateTime? expiresAt,
    String? tokenType,
    String? scope,
  }) {
    return AuthEntity(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      expiresAt: expiresAt ?? this.expiresAt,
      tokenType: tokenType ?? this.tokenType,
      scope: scope ?? this.scope,
    );
  }

  @override
  List<Object?> get props => [
        accessToken,
        refreshToken,
        expiresAt,
        tokenType,
        scope,
      ];
}
```

## 2. 值对象 (Value Objects)

### 邮箱值对象
```dart
// packages/shared/shared_models/lib/src/common/email_vo.dart
import 'package:equatable/equatable.dart';

/// 邮箱值对象
class EmailVO extends Equatable {
  const EmailVO._(this.value);

  factory EmailVO.create(String email) {
    if (!_isValidEmail(email)) {
      throw ArgumentError('Invalid email format: $email');
    }
    return EmailVO._(email.toLowerCase().trim());
  }

  final String value;

  /// 获取邮箱域名
  String get domain => value.split('@').last;

  /// 获取邮箱用户名
  String get username => value.split('@').first;

  /// 检查是否为企业邮箱
  bool get isBusinessEmail {
    const businessDomains = [
      'gmail.com',
      'outlook.com',
      'yahoo.com',
      'hotmail.com',
      '163.com',
      'qq.com',
    ];
    return !businessDomains.contains(domain);
  }

  static bool _isValidEmail(String email) {
    return RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    ).hasMatch(email);
  }

  @override
  List<Object> get props => [value];

  @override
  String toString() => value;
}
```

### 密码值对象
```dart
// packages/shared/shared_models/lib/src/common/password_vo.dart
import 'package:equatable/equatable.dart';

/// 密码值对象
class PasswordVO extends Equatable {
  const PasswordVO._(this.value);

  factory PasswordVO.create(String password) {
    final validation = _validatePassword(password);
    if (!validation.isValid) {
      throw ArgumentError('Invalid password: ${validation.errors.join(', ')}');
    }
    return PasswordVO._(password);
  }

  final String value;

  /// 密码强度枚举
  PasswordStrength get strength {
    if (value.length < 6) return PasswordStrength.weak;
    
    int score = 0;
    if (value.length >= 8) score++;
    if (RegExp(r'[A-Z]').hasMatch(value)) score++;
    if (RegExp(r'[a-z]').hasMatch(value)) score++;
    if (RegExp(r'[0-9]').hasMatch(value)) score++;
    if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) score++;
    
    if (score >= 4) return PasswordStrength.strong;
    if (score >= 2) return PasswordStrength.medium;
    return PasswordStrength.weak;
  }

  static PasswordValidation _validatePassword(String password) {
    final errors = <String>[];
    
    if (password.length < 6) {
      errors.add('Password must be at least 6 characters long');
    }
    
    if (password.length > 128) {
      errors.add('Password must be less than 128 characters');
    }
    
    if (!RegExp(r'[a-zA-Z]').hasMatch(password)) {
      errors.add('Password must contain at least one letter');
    }
    
    return PasswordValidation(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  @override
  List<Object> get props => [value];
}

/// 密码强度枚举
enum PasswordStrength {
  weak,
  medium,
  strong,
}

/// 密码验证结果
class PasswordValidation {
  const PasswordValidation({
    required this.isValid,
    required this.errors,
  });

  final bool isValid;
  final List<String> errors;
}
```

## 3. 仓储接口 (Repository Interfaces)

### 用户仓储接口
```dart
// packages/features/feature_auth/lib/src/domain/repositories/user_repository.dart
import 'package:shared_models/shared_models.dart';
import 'package:core_error/core_error.dart';

/// 用户仓储接口
abstract class UserRepository {
  /// 获取当前用户信息
  Future<Result<UserEntity>> getCurrentUser();
  
  /// 根据ID获取用户
  Future<Result<UserEntity>> getUserById(String userId);
  
  /// 更新用户信息
  Future<Result<UserEntity>> updateUser(UserEntity user);
  
  /// 更新用户头像
  Future<Result<String>> updateAvatar(String imagePath);
  
  /// 验证邮箱
  Future<Result<void>> verifyEmail(String verificationCode);
  
  /// 发送邮箱验证码
  Future<Result<void>> sendEmailVerification();
  
  /// 更新密码
  Future<Result<void>> updatePassword({
    required String currentPassword,
    required String newPassword,
  });
  
  /// 删除用户账户
  Future<Result<void>> deleteAccount();
  
  /// 搜索用户
  Future<Result<List<UserEntity>>> searchUsers({
    required String query,
    int page = 1,
    int limit = 20,
  });
  
  /// 获取用户统计信息
  Future<Result<UserStatsEntity>> getUserStats(String userId);
}
```

### 认证仓储接口
```dart
// packages/features/feature_auth/lib/src/domain/repositories/auth_repository.dart
import 'package:shared_models/shared_models.dart';
import 'package:core_error/core_error.dart';

/// 认证仓储接口
abstract class AuthRepository {
  /// 邮箱密码登录
  Future<Result<AuthEntity>> loginWithEmail({
    required EmailVO email,
    required PasswordVO password,
  });
  
  /// 手机号验证码登录
  Future<Result<AuthEntity>> loginWithPhone({
    required String phone,
    required String verificationCode,
  });
  
  /// 第三方登录
  Future<Result<AuthEntity>> loginWithProvider({
    required AuthProvider provider,
    required String token,
  });
  
  /// 注册账户
  Future<Result<AuthEntity>> register({
    required EmailVO email,
    required PasswordVO password,
    required String name,
  });
  
  /// 刷新Token
  Future<Result<AuthEntity>> refreshToken(String refreshToken);
  
  /// 登出
  Future<Result<void>> logout();
  
  /// 忘记密码
  Future<Result<void>> forgotPassword(EmailVO email);
  
  /// 重置密码
  Future<Result<void>> resetPassword({
    required String resetToken,
    required PasswordVO newPassword,
  });
  
  /// 检查认证状态
  Future<Result<bool>> isAuthenticated();
  
  /// 获取当前认证信息
  Future<Result<AuthEntity?>> getCurrentAuth();
  
  /// 发送手机验证码
  Future<Result<void>> sendPhoneVerification(String phone);
}

/// 第三方认证提供商
enum AuthProvider {
  google,
  apple,
  wechat,
  qq,
  weibo,
}
```

## 4. 用例 (Use Cases)

### 登录用例
```dart
// packages/features/feature_auth/lib/src/domain/usecases/login_usecase.dart
import 'package:injectable/injectable.dart';
import 'package:shared_models/shared_models.dart';
import 'package:core_error/core_error.dart';
import '../repositories/auth_repository.dart';
import '../repositories/user_repository.dart';

/// 登录用例参数
class LoginParams {
  const LoginParams({
    required this.email,
    required this.password,
    this.rememberMe = false,
  });

  final String email;
  final String password;
  final bool rememberMe;
}

/// 登录结果
class LoginResult {
  const LoginResult({
    required this.auth,
    required this.user,
  });

  final AuthEntity auth;
  final UserEntity user;
}

/// 登录用例
@injectable
class LoginUseCase {
  const LoginUseCase(
    this._authRepository,
    this._userRepository,
  );

  final AuthRepository _authRepository;
  final UserRepository _userRepository;

  /// 执行登录
  Future<Result<LoginResult>> call(LoginParams params) async {
    try {
      // 1. 验证输入参数
      final emailValidation = _validateEmail(params.email);
      if (emailValidation.isFailure) {
        return emailValidation.cast<LoginResult>();
      }

      final passwordValidation = _validatePassword(params.password);
      if (passwordValidation.isFailure) {
        return passwordValidation.cast<LoginResult>();
      }

      final email = EmailVO.create(params.email);
      final password = PasswordVO.create(params.password);

      // 2. 执行登录
      final authResult = await _authRepository.loginWithEmail(
        email: email,
        password: password,
      );

      if (authResult.isFailure) {
        return authResult.cast<LoginResult>();
      }

      // 3. 获取用户信息
      final userResult = await _userRepository.getCurrentUser();
      if (userResult.isFailure) {
        return userResult.cast<LoginResult>();
      }

      // 4. 返回结果
      return Result.success(
        LoginResult(
          auth: authResult.data!,
          user: userResult.data!,
        ),
      );
    } catch (e) {
      return Result.failure(
        UnknownFailure(message: 'Login failed: ${e.toString()}'),
      );
    }
  }

  /// 验证邮箱格式
  Result<void> _validateEmail(String email) {
    try {
      EmailVO.create(email);
      return const Result.success(null);
    } catch (e) {
      return Result.failure(
        ValidationFailure(message: 'Invalid email format'),
      );
    }
  }

  /// 验证密码格式
  Result<void> _validatePassword(String password) {
    try {
      PasswordVO.create(password);
      return const Result.success(null);
    } catch (e) {
      return Result.failure(
        ValidationFailure(message: 'Invalid password format'),
      );
    }
  }
}
```

### 注册用例
```dart
// packages/features/feature_auth/lib/src/domain/usecases/register_usecase.dart
import 'package:injectable/injectable.dart';
import 'package:shared_models/shared_models.dart';
import 'package:core_error/core_error.dart';
import '../repositories/auth_repository.dart';

/// 注册用例参数
class RegisterParams {
  const RegisterParams({
    required this.email,
    required this.password,
    required this.confirmPassword,
    required this.name,
    this.acceptTerms = false,
  });

  final String email;
  final String password;
  final String confirmPassword;
  final String name;
  final bool acceptTerms;
}

/// 注册用例
@injectable
class RegisterUseCase {
  const RegisterUseCase(this._authRepository);

  final AuthRepository _authRepository;

  /// 执行注册
  Future<Result<AuthEntity>> call(RegisterParams params) async {
    try {
      // 1. 验证输入参数
      final validation = _validateParams(params);
      if (validation.isFailure) {
        return validation.cast<AuthEntity>();
      }

      // 2. 创建值对象
      final email = EmailVO.create(params.email);
      final password = PasswordVO.create(params.password);

      // 3. 执行注册
      return await _authRepository.register(
        email: email,
        password: password,
        name: params.name.trim(),
      );
    } catch (e) {
      return Result.failure(
        UnknownFailure(message: 'Registration failed: ${e.toString()}'),
      );
    }
  }

  /// 验证注册参数
  Result<void> _validateParams(RegisterParams params) {
    // 验证邮箱
    try {
      EmailVO.create(params.email);
    } catch (e) {
      return Result.failure(
        ValidationFailure(message: 'Invalid email format'),
      );
    }

    // 验证密码
    try {
      PasswordVO.create(params.password);
    } catch (e) {
      return Result.failure(
        ValidationFailure(message: 'Invalid password format'),
      );
    }

    // 验证密码确认
    if (params.password != params.confirmPassword) {
      return Result.failure(
        ValidationFailure(message: 'Passwords do not match'),
      );
    }

    // 验证姓名
    if (params.name.trim().isEmpty) {
      return Result.failure(
        ValidationFailure(message: 'Name is required'),
      );
    }

    if (params.name.trim().length < 2) {
      return Result.failure(
        ValidationFailure(message: 'Name must be at least 2 characters'),
      );
    }

    // 验证服务条款
    if (!params.acceptTerms) {
      return Result.failure(
        ValidationFailure(message: 'You must accept the terms and conditions'),
      );
    }

    return const Result.success(null);
  }
}
```

## 5. 领域服务 (Domain Services)

### 密码服务
```dart
// packages/features/feature_auth/lib/src/domain/services/password_service.dart
import 'package:injectable/injectable.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'dart:math';

/// 密码服务
@injectable
class PasswordService {
  /// 生成随机密码
  String generateRandomPassword({
    int length = 12,
    bool includeUppercase = true,
    bool includeLowercase = true,
    bool includeNumbers = true,
    bool includeSymbols = true,
  }) {
    const uppercaseChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercaseChars = 'abcdefghijklmnopqrstuvwxyz';
    const numberChars = '0123456789';
    const symbolChars = '!@#\$%^&*()_+-=[]{}|;:,.<>?';

    String chars = '';
    if (includeUppercase) chars += uppercaseChars;
    if (includeLowercase) chars += lowercaseChars;
    if (includeNumbers) chars += numberChars;
    if (includeSymbols) chars += symbolChars;

    if (chars.isEmpty) {
      throw ArgumentError('At least one character type must be included');
    }

    final random = Random.secure();
    return List.generate(
      length,
      (index) => chars[random.nextInt(chars.length)],
    ).join();
  }

  /// 哈希密码
  String hashPassword(String password, String salt) {
    final bytes = utf8.encode(password + salt);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// 生成盐值
  String generateSalt() {
    final random = Random.secure();
    final saltBytes = List.generate(32, (_) => random.nextInt(256));
    return base64.encode(saltBytes);
  }

  /// 验证密码强度
  PasswordStrengthResult checkPasswordStrength(String password) {
    int score = 0;
    final feedback = <String>[];

    // 长度检查
    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.add('Use at least 8 characters');
    }

    if (password.length >= 12) {
      score += 1;
    }

    // 字符类型检查
    if (RegExp(r'[A-Z]').hasMatch(password)) {
      score += 1;
    } else {
      feedback.add('Add uppercase letters');
    }

    if (RegExp(r'[a-z]').hasMatch(password)) {
      score += 1;
    } else {
      feedback.add('Add lowercase letters');
    }

    if (RegExp(r'[0-9]').hasMatch(password)) {
      score += 1;
    } else {
      feedback.add('Add numbers');
    }

    if (RegExp(r'[!@#\$%^&*(),.?":{}|<>]').hasMatch(password)) {
      score += 1;
    } else {
      feedback.add('Add symbols');
    }

    // 常见模式检查
    if (RegExp(r'(.)\1{2,}').hasMatch(password)) {
      score -= 1;
      feedback.add('Avoid repeated characters');
    }

    if (RegExp(r'(012|123|234|345|456|567|678|789|890)').hasMatch(password)) {
      score -= 1;
      feedback.add('Avoid sequential numbers');
    }

    if (RegExp(r'(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)', caseSensitive: false).hasMatch(password)) {
      score -= 1;
      feedback.add('Avoid sequential letters');
    }

    // 确定强度等级
    PasswordStrength strength;
    if (score >= 5) {
      strength = PasswordStrength.strong;
    } else if (score >= 3) {
      strength = PasswordStrength.medium;
    } else {
      strength = PasswordStrength.weak;
    }

    return PasswordStrengthResult(
      strength: strength,
      score: score,
      feedback: feedback,
    );
  }
}

/// 密码强度检查结果
class PasswordStrengthResult {
  const PasswordStrengthResult({
    required this.strength,
    required this.score,
    required this.feedback,
  });

  final PasswordStrength strength;
  final int score;
  final List<String> feedback;
}
```

这个Domain Layer实现展示了：

1. **实体设计**：包含业务逻辑的核心对象
2. **值对象**：不可变的值类型，包含验证逻辑
3. **仓储接口**：定义数据访问的抽象
4. **用例**：封装业务逻辑的应用服务
5. **领域服务**：跨实体的业务逻辑

所有代码都遵循Clean Architecture原则，确保业务逻辑独立于外部依赖。