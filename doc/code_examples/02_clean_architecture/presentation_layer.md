# Presentation Layer 实现示例

## 1. BLoC 状态管理

### 认证状态定义
```dart
// packages/features/feature_auth/lib/src/presentation/bloc/auth_state.dart
import 'package:equatable/equatable.dart';
import 'package:shared_models/shared_models.dart';

/// 认证状态基类
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class AuthInitial extends AuthState {
  const AuthInitial();
}

/// 加载中状态
class AuthLoading extends AuthState {
  const AuthLoading();
}

/// 已认证状态
class AuthAuthenticated extends AuthState {
  const AuthAuthenticated({
    required this.user,
    required this.auth,
  });

  final UserEntity user;
  final AuthEntity auth;

  @override
  List<Object> get props => [user, auth];
}

/// 未认证状态
class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated();
}

/// 认证失败状态
class AuthFailure extends AuthState {
  const AuthFailure({
    required this.message,
    this.canRetry = true,
  });

  final String message;
  final bool canRetry;

  @override
  List<Object> get props => [message, canRetry];
}

/// Token即将过期状态
class AuthTokenExpiring extends AuthState {
  const AuthTokenExpiring({
    required this.user,
    required this.auth,
  });

  final UserEntity user;
  final AuthEntity auth;

  @override
  List<Object> get props => [user, auth];
}
```

### 认证事件定义
```dart
// packages/features/feature_auth/lib/src/presentation/bloc/auth_event.dart
import 'package:equatable/equatable.dart';
import 'package:shared_models/shared_models.dart';

/// 认证事件基类
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

/// 检查认证状态事件
class AuthCheckRequested extends AuthEvent {
  const AuthCheckRequested();
}

/// 登录事件
class AuthLoginRequested extends AuthEvent {
  const AuthLoginRequested({
    required this.email,
    required this.password,
    this.rememberMe = false,
  });

  final String email;
  final String password;
  final bool rememberMe;

  @override
  List<Object> get props => [email, password, rememberMe];
}

/// 注册事件
class AuthRegisterRequested extends AuthEvent {
  const AuthRegisterRequested({
    required this.email,
    required this.password,
    required this.confirmPassword,
    required this.name,
    this.acceptTerms = false,
  });

  final String email;
  final String password;
  final String confirmPassword;
  final String name;
  final bool acceptTerms;

  @override
  List<Object> get props => [
        email,
        password,
        confirmPassword,
        name,
        acceptTerms,
      ];
}

/// 第三方登录事件
class AuthSocialLoginRequested extends AuthEvent {
  const AuthSocialLoginRequested({
    required this.provider,
    required this.token,
  });

  final AuthProvider provider;
  final String token;

  @override
  List<Object> get props => [provider, token];
}

/// 登出事件
class AuthLogoutRequested extends AuthEvent {
  const AuthLogoutRequested();
}

/// 刷新Token事件
class AuthTokenRefreshRequested extends AuthEvent {
  const AuthTokenRefreshRequested();
}

/// 忘记密码事件
class AuthForgotPasswordRequested extends AuthEvent {
  const AuthForgotPasswordRequested({required this.email});

  final String email;

  @override
  List<Object> get props => [email];
}

/// 重置密码事件
class AuthResetPasswordRequested extends AuthEvent {
  const AuthResetPasswordRequested({
    required this.token,
    required this.newPassword,
    required this.confirmPassword,
  });

  final String token;
  final String newPassword;
  final String confirmPassword;

  @override
  List<Object> get props => [token, newPassword, confirmPassword];
}
```

### 认证BLoC实现
```dart
// packages/features/feature_auth/lib/src/presentation/bloc/auth_bloc.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_models/shared_models.dart';
import 'package:core_error/core_error.dart';
import '../../domain/usecases/login_usecase.dart';
import '../../domain/usecases/register_usecase.dart';
import '../../domain/usecases/logout_usecase.dart';
import '../../domain/usecases/refresh_token_usecase.dart';
import '../../domain/usecases/forgot_password_usecase.dart';
import '../../domain/usecases/reset_password_usecase.dart';
import '../../domain/usecases/check_auth_usecase.dart';
import 'auth_event.dart';
import 'auth_state.dart';
import 'dart:async';

/// 认证BLoC
@injectable
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  AuthBloc(
    this._loginUseCase,
    this._registerUseCase,
    this._logoutUseCase,
    this._refreshTokenUseCase,
    this._forgotPasswordUseCase,
    this._resetPasswordUseCase,
    this._checkAuthUseCase,
  ) : super(const AuthInitial()) {
    // 注册事件处理器
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<AuthLoginRequested>(_onAuthLoginRequested);
    on<AuthRegisterRequested>(_onAuthRegisterRequested);
    on<AuthSocialLoginRequested>(_onAuthSocialLoginRequested);
    on<AuthLogoutRequested>(_onAuthLogoutRequested);
    on<AuthTokenRefreshRequested>(_onAuthTokenRefreshRequested);
    on<AuthForgotPasswordRequested>(_onAuthForgotPasswordRequested);
    on<AuthResetPasswordRequested>(_onAuthResetPasswordRequested);

    // 启动Token过期检查
    _startTokenExpirationCheck();
  }

  final LoginUseCase _loginUseCase;
  final RegisterUseCase _registerUseCase;
  final LogoutUseCase _logoutUseCase;
  final RefreshTokenUseCase _refreshTokenUseCase;
  final ForgotPasswordUseCase _forgotPasswordUseCase;
  final ResetPasswordUseCase _resetPasswordUseCase;
  final CheckAuthUseCase _checkAuthUseCase;

  Timer? _tokenExpirationTimer;

  /// 检查认证状态
  Future<void> _onAuthCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _checkAuthUseCase();
    result.fold(
      onSuccess: (authResult) {
        if (authResult != null) {
          emit(AuthAuthenticated(
            user: authResult.user,
            auth: authResult.auth,
          ));
        } else {
          emit(const AuthUnauthenticated());
        }
      },
      onFailure: (failure) {
        emit(AuthFailure(message: failure.message));
      },
    );
  }

  /// 处理登录
  Future<void> _onAuthLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final params = LoginParams(
      email: event.email,
      password: event.password,
      rememberMe: event.rememberMe,
    );

    final result = await _loginUseCase(params);
    result.fold(
      onSuccess: (loginResult) {
        emit(AuthAuthenticated(
          user: loginResult.user,
          auth: loginResult.auth,
        ));
      },
      onFailure: (failure) {
        emit(AuthFailure(
          message: failure.message,
          canRetry: failure is! ValidationFailure,
        ));
      },
    );
  }

  /// 处理注册
  Future<void> _onAuthRegisterRequested(
    AuthRegisterRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final params = RegisterParams(
      email: event.email,
      password: event.password,
      confirmPassword: event.confirmPassword,
      name: event.name,
      acceptTerms: event.acceptTerms,
    );

    final result = await _registerUseCase(params);
    result.fold(
      onSuccess: (auth) {
        // 注册成功后需要获取用户信息
        add(const AuthCheckRequested());
      },
      onFailure: (failure) {
        emit(AuthFailure(
          message: failure.message,
          canRetry: failure is! ValidationFailure,
        ));
      },
    );
  }

  /// 处理第三方登录
  Future<void> _onAuthSocialLoginRequested(
    AuthSocialLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    // TODO: 实现第三方登录逻辑
    emit(const AuthFailure(
      message: 'Social login not implemented yet',
      canRetry: false,
    ));
  }

  /// 处理登出
  Future<void> _onAuthLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _logoutUseCase();
    result.fold(
      onSuccess: (_) {
        _cancelTokenExpirationTimer();
        emit(const AuthUnauthenticated());
      },
      onFailure: (failure) {
        // 即使登出失败，也要清除本地状态
        _cancelTokenExpirationTimer();
        emit(const AuthUnauthenticated());
      },
    );
  }

  /// 处理Token刷新
  Future<void> _onAuthTokenRefreshRequested(
    AuthTokenRefreshRequested event,
    Emitter<AuthState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AuthAuthenticated &&
        currentState is! AuthTokenExpiring) {
      return;
    }

    final result = await _refreshTokenUseCase();
    result.fold(
      onSuccess: (authResult) {
        emit(AuthAuthenticated(
          user: authResult.user,
          auth: authResult.auth,
        ));
      },
      onFailure: (failure) {
        // Token刷新失败，需要重新登录
        emit(const AuthUnauthenticated());
      },
    );
  }

  /// 处理忘记密码
  Future<void> _onAuthForgotPasswordRequested(
    AuthForgotPasswordRequested event,
    Emitter<AuthState> emit,
  ) async {
    // 忘记密码不改变认证状态，只是发送邮件
    final result = await _forgotPasswordUseCase(event.email);
    // 可以通过其他方式通知UI结果，比如显示SnackBar
  }

  /// 处理重置密码
  Future<void> _onAuthResetPasswordRequested(
    AuthResetPasswordRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final params = ResetPasswordParams(
      token: event.token,
      newPassword: event.newPassword,
      confirmPassword: event.confirmPassword,
    );

    final result = await _resetPasswordUseCase(params);
    result.fold(
      onSuccess: (_) {
        emit(const AuthUnauthenticated());
      },
      onFailure: (failure) {
        emit(AuthFailure(message: failure.message));
      },
    );
  }

  /// 启动Token过期检查
  void _startTokenExpirationCheck() {
    _tokenExpirationTimer = Timer.periodic(
      const Duration(minutes: 1),
      (_) => _checkTokenExpiration(),
    );
  }

  /// 检查Token是否即将过期
  void _checkTokenExpiration() {
    final currentState = state;
    if (currentState is AuthAuthenticated) {
      if (currentState.auth.isExpired) {
        // Token已过期，尝试刷新
        add(const AuthTokenRefreshRequested());
      } else if (currentState.auth.isExpiringSoon) {
        // Token即将过期，发出警告
        emit(AuthTokenExpiring(
          user: currentState.user,
          auth: currentState.auth,
        ));
      }
    }
  }

  /// 取消Token过期检查
  void _cancelTokenExpirationTimer() {
    _tokenExpirationTimer?.cancel();
    _tokenExpirationTimer = null;
  }

  @override
  Future<void> close() {
    _cancelTokenExpirationTimer();
    return super.close();
  }
}
```

## 2. 登录页面实现

### 登录页面
```dart
// packages/features/feature_auth/lib/src/presentation/pages/login_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:ui_kit/ui_kit.dart';
import '../bloc/auth_bloc.dart';
import '../widgets/login_form.dart';
import '../widgets/social_login_buttons.dart';
import '../widgets/auth_background.dart';

/// 登录页面
class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  static const String routeName = '/login';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthAuthenticated) {
            // 登录成功，导航到主页
            context.go('/home');
          } else if (state is AuthFailure) {
            // 显示错误信息
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Theme.of(context).colorScheme.error,
                action: state.canRetry
                    ? SnackBarAction(
                        label: 'Retry',
                        onPressed: () {
                          // 可以重试的操作
                        },
                      )
                    : null,
              ),
            );
          }
        },
        child: const AuthBackground(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(AppSpacing.lg),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                SizedBox(height: AppSpacing.xxxl),
                _LoginHeader(),
                SizedBox(height: AppSpacing.xl),
                LoginForm(),
                SizedBox(height: AppSpacing.lg),
                _OrDivider(),
                SizedBox(height: AppSpacing.lg),
                SocialLoginButtons(),
                SizedBox(height: AppSpacing.lg),
                _SignUpPrompt(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 登录页面头部
class _LoginHeader extends StatelessWidget {
  const _LoginHeader();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        // Logo
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            Icons.flutter_dash,
            size: 40,
            color: theme.colorScheme.onPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.lg),
        
        // 标题
        Text(
          'Welcome Back',
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        
        // 副标题
        Text(
          'Sign in to your account',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }
}

/// 分割线
class _OrDivider extends StatelessWidget {
  const _OrDivider();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Expanded(
          child: Divider(
            color: theme.colorScheme.outline,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
          child: Text(
            'OR',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Divider(
            color: theme.colorScheme.outline,
          ),
        ),
      ],
    );
  }
}

/// 注册提示
class _SignUpPrompt extends StatelessWidget {
  const _SignUpPrompt();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "Don't have an account? ",
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        GestureDetector(
          onTap: () => context.push('/register'),
          child: Text(
            'Sign Up',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
```

### 登录表单组件
```dart
// packages/features/feature_auth/lib/src/presentation/widgets/login_form.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit/ui_kit.dart';
import '../bloc/auth_bloc.dart';
import '../validators/auth_validators.dart';

/// 登录表单
class LoginForm extends StatefulWidget {
  const LoginForm({super.key});

  @override
  State<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();
  
  bool _obscurePassword = true;
  bool _rememberMe = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        final isLoading = state is AuthLoading;
        
        return Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 邮箱输入框
              AppTextFormField(
                controller: _emailController,
                focusNode: _emailFocusNode,
                labelText: 'Email',
                hintText: 'Enter your email',
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
                prefixIcon: const Icon(Icons.email_outlined),
                validator: AuthValidators.validateEmail,
                enabled: !isLoading,
                onFieldSubmitted: (_) {
                  _passwordFocusNode.requestFocus();
                },
              ),
              const SizedBox(height: AppSpacing.md),
              
              // 密码输入框
              AppTextFormField(
                controller: _passwordController,
                focusNode: _passwordFocusNode,
                labelText: 'Password',
                hintText: 'Enter your password',
                obscureText: _obscurePassword,
                textInputAction: TextInputAction.done,
                prefixIcon: const Icon(Icons.lock_outlined),
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscurePassword
                        ? Icons.visibility_outlined
                        : Icons.visibility_off_outlined,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  },
                ),
                validator: AuthValidators.validatePassword,
                enabled: !isLoading,
                onFieldSubmitted: (_) => _handleLogin(),
              ),
              const SizedBox(height: AppSpacing.sm),
              
              // 记住我和忘记密码
              Row(
                children: [
                  Expanded(
                    child: CheckboxListTile(
                      value: _rememberMe,
                      onChanged: isLoading
                          ? null
                          : (value) {
                              setState(() {
                                _rememberMe = value ?? false;
                              });
                            },
                      title: const Text('Remember me'),
                      controlAffinity: ListTileControlAffinity.leading,
                      contentPadding: EdgeInsets.zero,
                      dense: true,
                    ),
                  ),
                  TextButton(
                    onPressed: isLoading ? null : _handleForgotPassword,
                    child: const Text('Forgot Password?'),
                  ),
                ],
              ),
              const SizedBox(height: AppSpacing.lg),
              
              // 登录按钮
              AppButton(
                onPressed: isLoading ? null : _handleLogin,
                isLoading: isLoading,
                child: const Text('Sign In'),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 处理登录
  void _handleLogin() {
    if (_formKey.currentState?.validate() ?? false) {
      context.read<AuthBloc>().add(
            AuthLoginRequested(
              email: _emailController.text.trim(),
              password: _passwordController.text,
              rememberMe: _rememberMe,
            ),
          );
    }
  }

  /// 处理忘记密码
  void _handleForgotPassword() {
    // 导航到忘记密码页面或显示对话框
    showDialog(
      context: context,
      builder: (context) => const ForgotPasswordDialog(),
    );
  }
}
```

### 第三方登录按钮
```dart
// packages/features/feature_auth/lib/src/presentation/widgets/social_login_buttons.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit/ui_kit.dart';
import 'package:shared_models/shared_models.dart';
import '../bloc/auth_bloc.dart';

/// 第三方登录按钮组
class SocialLoginButtons extends StatelessWidget {
  const SocialLoginButtons({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        final isLoading = state is AuthLoading;
        
        return Column(
          children: [
            // Google登录
            _SocialLoginButton(
              provider: AuthProvider.google,
              label: 'Continue with Google',
              icon: Icons.g_mobiledata, // 实际项目中使用Google图标
              backgroundColor: Colors.white,
              textColor: Colors.black87,
              borderColor: Colors.grey.shade300,
              enabled: !isLoading,
            ),
            const SizedBox(height: AppSpacing.sm),
            
            // Apple登录（仅iOS）
            if (Theme.of(context).platform == TargetPlatform.iOS) ..[
              _SocialLoginButton(
                provider: AuthProvider.apple,
                label: 'Continue with Apple',
                icon: Icons.apple,
                backgroundColor: Colors.black,
                textColor: Colors.white,
                enabled: !isLoading,
              ),
              const SizedBox(height: AppSpacing.sm),
            ],
            
            // 微信登录（中国版）
            _SocialLoginButton(
              provider: AuthProvider.wechat,
              label: 'Continue with WeChat',
              icon: Icons.chat, // 实际项目中使用微信图标
              backgroundColor: const Color(0xFF07C160),
              textColor: Colors.white,
              enabled: !isLoading,
            ),
          ],
        );
      },
    );
  }
}

/// 第三方登录按钮
class _SocialLoginButton extends StatelessWidget {
  const _SocialLoginButton({
    required this.provider,
    required this.label,
    required this.icon,
    required this.backgroundColor,
    required this.textColor,
    this.borderColor,
    this.enabled = true,
  });

  final AuthProvider provider;
  final String label;
  final IconData icon;
  final Color backgroundColor;
  final Color textColor;
  final Color? borderColor;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: OutlinedButton(
        onPressed: enabled ? () => _handleSocialLogin(context) : null,
        style: OutlinedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: textColor,
          side: BorderSide(
            color: borderColor ?? backgroundColor,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 20),
            const SizedBox(width: AppSpacing.sm),
            Text(
              label,
              style: TextStyle(
                color: textColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 处理第三方登录
  void _handleSocialLogin(BuildContext context) {
    // 这里应该调用相应的第三方登录SDK
    // 获取token后触发登录事件
    
    // 示例：模拟获取token
    _simulateGetToken().then((token) {
      if (token != null) {
        context.read<AuthBloc>().add(
              AuthSocialLoginRequested(
                provider: provider,
                token: token,
              ),
            );
      }
    });
  }

  /// 模拟获取第三方登录token
  Future<String?> _simulateGetToken() async {
    // 实际实现中，这里会调用相应的SDK
    switch (provider) {
      case AuthProvider.google:
        // 调用Google Sign-In SDK
        return 'google_token_example';
      case AuthProvider.apple:
        // 调用Apple Sign-In SDK
        return 'apple_token_example';
      case AuthProvider.wechat:
        // 调用微信SDK
        return 'wechat_token_example';
      default:
        return null;
    }
  }
}
```

## 3. 表单验证器

### 认证验证器
```dart
// packages/features/feature_auth/lib/src/presentation/validators/auth_validators.dart
import 'package:shared_models/shared_models.dart';

/// 认证相关验证器
class AuthValidators {
  AuthValidators._();

  /// 验证邮箱
  static String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required';
    }

    try {
      EmailVO.create(value.trim());
      return null;
    } catch (e) {
      return 'Please enter a valid email address';
    }
  }

  /// 验证密码
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }

    try {
      PasswordVO.create(value);
      return null;
    } catch (e) {
      return 'Password must be at least 6 characters long';
    }
  }

  /// 验证密码确认
  static String? validateConfirmPassword(String? value, String password) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }

    if (value != password) {
      return 'Passwords do not match';
    }

    return null;
  }

  /// 验证姓名
  static String? validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Name is required';
    }

    final trimmedValue = value.trim();
    if (trimmedValue.length < 2) {
      return 'Name must be at least 2 characters long';
    }

    if (trimmedValue.length > 50) {
      return 'Name must be less than 50 characters';
    }

    // 检查是否包含特殊字符
    if (!RegExp(r'^[a-zA-Z\s\u4e00-\u9fa5]+$').hasMatch(trimmedValue)) {
      return 'Name can only contain letters and spaces';
    }

    return null;
  }

  /// 验证手机号
  static String? validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Phone number is required';
    }

    final trimmedValue = value.trim();
    
    // 中国手机号验证
    if (RegExp(r'^1[3-9]\d{9}$').hasMatch(trimmedValue)) {
      return null;
    }

    // 国际手机号验证（简单版本）
    if (RegExp(r'^\+\d{10,15}$').hasMatch(trimmedValue)) {
      return null;
    }

    return 'Please enter a valid phone number';
  }

  /// 验证验证码
  static String? validateVerificationCode(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Verification code is required';
    }

    final trimmedValue = value.trim();
    if (!RegExp(r'^\d{4,6}$').hasMatch(trimmedValue)) {
      return 'Please enter a valid verification code';
    }

    return null;
  }

  /// 验证服务条款同意
  static String? validateTermsAcceptance(bool? value) {
    if (value != true) {
      return 'You must accept the terms and conditions';
    }
    return null;
  }
}
```

## 4. 自定义组件

### 认证背景组件
```dart
// packages/features/feature_auth/lib/src/presentation/widgets/auth_background.dart
import 'package:flutter/material.dart';

/// 认证页面背景
class AuthBackground extends StatelessWidget {
  const AuthBackground({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    
    return Container(
      width: size.width,
      height: size.height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary.withOpacity(0.1),
            theme.colorScheme.secondary.withOpacity(0.05),
            theme.colorScheme.surface,
          ],
        ),
      ),
      child: Stack(
        children: [
          // 装饰性圆圈
          Positioned(
            top: -50,
            right: -50,
            child: Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: theme.colorScheme.primary.withOpacity(0.1),
              ),
            ),
          ),
          Positioned(
            bottom: -100,
            left: -100,
            child: Container(
              width: 300,
              height: 300,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: theme.colorScheme.secondary.withOpacity(0.1),
              ),
            ),
          ),
          
          // 主要内容
          SafeArea(
            child: child,
          ),
        ],
      ),
    );
  }
}
```

### 忘记密码对话框
```dart
// packages/features/feature_auth/lib/src/presentation/widgets/forgot_password_dialog.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit/ui_kit.dart';
import '../bloc/auth_bloc.dart';
import '../validators/auth_validators.dart';

/// 忘记密码对话框
class ForgotPasswordDialog extends StatefulWidget {
  const ForgotPasswordDialog({super.key});

  @override
  State<ForgotPasswordDialog> createState() => _ForgotPasswordDialogState();
}

class _ForgotPasswordDialogState extends State<ForgotPasswordDialog> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AlertDialog(
      title: Text(
        'Forgot Password',
        style: theme.textTheme.headlineSmall,
      ),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Enter your email address and we\'ll send you a link to reset your password.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: AppSpacing.lg),
            AppTextFormField(
              controller: _emailController,
              labelText: 'Email',
              hintText: 'Enter your email',
              keyboardType: TextInputType.emailAddress,
              prefixIcon: const Icon(Icons.email_outlined),
              validator: AuthValidators.validateEmail,
              enabled: !_isLoading,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        AppButton(
          onPressed: _isLoading ? null : _handleSendResetEmail,
          isLoading: _isLoading,
          child: const Text('Send Reset Link'),
        ),
      ],
    );
  }

  /// 发送重置密码邮件
  void _handleSendResetEmail() async {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      context.read<AuthBloc>().add(
            AuthForgotPasswordRequested(
              email: _emailController.text.trim(),
            ),
          );

      // 模拟API调用
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        Navigator.of(context).pop();
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Reset link sent to your email'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }
}
```

这个Presentation Layer实现展示了：

1. **BLoC状态管理**：完整的状态、事件和BLoC实现
2. **页面组件**：响应式UI设计和用户交互
3. **表单处理**：验证、提交和错误处理
4. **自定义组件**：可复用的UI组件
5. **用户体验**：加载状态、错误提示、导航等

所有组件都遵循Material Design规范，并与UI Kit集成。