# 性能优化实现示例

## 1. 内存管理和优化

### 内存监控服务
```dart
// packages/core/core_performance/lib/src/memory_monitor.dart
import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:core_error/core_error.dart';

/// 内存使用信息
class MemoryInfo {
  const MemoryInfo({
    required this.usedMemory,
    required this.totalMemory,
    required this.freeMemory,
    required this.timestamp,
  });

  final int usedMemory; // 已使用内存（字节）
  final int totalMemory; // 总内存（字节）
  final int freeMemory; // 可用内存（字节）
  final DateTime timestamp;

  double get usagePercentage => (usedMemory / totalMemory) * 100;

  String get formattedUsedMemory => _formatBytes(usedMemory);
  String get formattedTotalMemory => _formatBytes(totalMemory);
  String get formattedFreeMemory => _formatBytes(freeMemory);

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  Map<String, dynamic> toMap() {
    return {
      'usedMemory': usedMemory,
      'totalMemory': totalMemory,
      'freeMemory': freeMemory,
      'usagePercentage': usagePercentage,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// 内存警告级别
enum MemoryWarningLevel {
  normal,
  warning,
  critical,
}

/// 内存监控服务
abstract class MemoryMonitor {
  Stream<MemoryInfo> get memoryStream;
  Stream<MemoryWarningLevel> get warningStream;
  
  Future<MemoryInfo> getCurrentMemoryInfo();
  void startMonitoring({Duration interval = const Duration(seconds: 5)});
  void stopMonitoring();
  void forceGarbageCollection();
  
  // 内存警告阈值设置
  void setWarningThreshold(double percentage);
  void setCriticalThreshold(double percentage);
}

@Injectable(as: MemoryMonitor)
class MemoryMonitorImpl implements MemoryMonitor {
  MemoryMonitorImpl(this._logger);

  final LoggerService _logger;
  
  Timer? _monitoringTimer;
  final StreamController<MemoryInfo> _memoryController = StreamController<MemoryInfo>.broadcast();
  final StreamController<MemoryWarningLevel> _warningController = StreamController<MemoryWarningLevel>.broadcast();
  
  double _warningThreshold = 70.0; // 70%
  double _criticalThreshold = 85.0; // 85%
  MemoryWarningLevel _lastWarningLevel = MemoryWarningLevel.normal;

  @override
  Stream<MemoryInfo> get memoryStream => _memoryController.stream;

  @override
  Stream<MemoryWarningLevel> get warningStream => _warningController.stream;

  @override
  Future<MemoryInfo> getCurrentMemoryInfo() async {
    try {
      final processInfo = await Process.run('cat', ['/proc/meminfo']);
      final lines = processInfo.stdout.toString().split('\n');
      
      int totalMemory = 0;
      int freeMemory = 0;
      int availableMemory = 0;
      
      for (final line in lines) {
        if (line.startsWith('MemTotal:')) {
          totalMemory = _parseMemoryLine(line) * 1024; // Convert KB to bytes
        } else if (line.startsWith('MemFree:')) {
          freeMemory = _parseMemoryLine(line) * 1024;
        } else if (line.startsWith('MemAvailable:')) {
          availableMemory = _parseMemoryLine(line) * 1024;
        }
      }
      
      final usedMemory = totalMemory - (availableMemory > 0 ? availableMemory : freeMemory);
      
      return MemoryInfo(
        usedMemory: usedMemory,
        totalMemory: totalMemory,
        freeMemory: availableMemory > 0 ? availableMemory : freeMemory,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      // Fallback for platforms that don't support /proc/meminfo
      return _getFallbackMemoryInfo();
    }
  }

  int _parseMemoryLine(String line) {
    final parts = line.split(RegExp(r'\s+'));
    return int.tryParse(parts[1]) ?? 0;
  }

  MemoryInfo _getFallbackMemoryInfo() {
    // 使用Dart VM的内存信息作为后备
    final vmInfo = developer.Service.getIsolateMemoryUsage(developer.Service.getIsolateID(Isolate.current)!);
    
    // 这里的数值是估算的，实际项目中可能需要更精确的实现
    const estimatedTotalMemory = 2 * 1024 * 1024 * 1024; // 2GB
    const estimatedUsedMemory = 512 * 1024 * 1024; // 512MB
    
    return MemoryInfo(
      usedMemory: estimatedUsedMemory,
      totalMemory: estimatedTotalMemory,
      freeMemory: estimatedTotalMemory - estimatedUsedMemory,
      timestamp: DateTime.now(),
    );
  }

  @override
  void startMonitoring({Duration interval = const Duration(seconds: 5)}) {
    stopMonitoring();
    
    _monitoringTimer = Timer.periodic(interval, (_) async {
      try {
        final memoryInfo = await getCurrentMemoryInfo();
        _memoryController.add(memoryInfo);
        
        // 检查内存警告级别
        _checkMemoryWarning(memoryInfo);
        
        _logger.debug('Memory usage: ${memoryInfo.formattedUsedMemory}/${memoryInfo.formattedTotalMemory} (${memoryInfo.usagePercentage.toStringAsFixed(1)}%)');
      } catch (e) {
        _logger.error('Failed to get memory info', error: e);
      }
    });
    
    _logger.info('Memory monitoring started with interval: $interval');
  }

  @override
  void stopMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    _logger.info('Memory monitoring stopped');
  }

  void _checkMemoryWarning(MemoryInfo memoryInfo) {
    MemoryWarningLevel currentLevel;
    
    if (memoryInfo.usagePercentage >= _criticalThreshold) {
      currentLevel = MemoryWarningLevel.critical;
    } else if (memoryInfo.usagePercentage >= _warningThreshold) {
      currentLevel = MemoryWarningLevel.warning;
    } else {
      currentLevel = MemoryWarningLevel.normal;
    }
    
    if (currentLevel != _lastWarningLevel) {
      _lastWarningLevel = currentLevel;
      _warningController.add(currentLevel);
      
      switch (currentLevel) {
        case MemoryWarningLevel.warning:
          _logger.warning('Memory usage warning: ${memoryInfo.usagePercentage.toStringAsFixed(1)}%');
          break;
        case MemoryWarningLevel.critical:
          _logger.error('Critical memory usage: ${memoryInfo.usagePercentage.toStringAsFixed(1)}%');
          // 自动触发垃圾回收
          forceGarbageCollection();
          break;
        case MemoryWarningLevel.normal:
          _logger.info('Memory usage back to normal: ${memoryInfo.usagePercentage.toStringAsFixed(1)}%');
          break;
      }
    }
  }

  @override
  void forceGarbageCollection() {
    _logger.info('Forcing garbage collection');
    
    // 强制垃圾回收
    for (int i = 0; i < 3; i++) {
      developer.gc();
    }
    
    _logger.info('Garbage collection completed');
  }

  @override
  void setWarningThreshold(double percentage) {
    _warningThreshold = percentage;
    _logger.info('Memory warning threshold set to: $percentage%');
  }

  @override
  void setCriticalThreshold(double percentage) {
    _criticalThreshold = percentage;
    _logger.info('Memory critical threshold set to: $percentage%');
  }

  void dispose() {
    stopMonitoring();
    _memoryController.close();
    _warningController.close();
  }
}
```

### 图片缓存优化
```dart
// packages/core/core_performance/lib/src/optimized_image_cache.dart
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:injectable/injectable.dart';
import 'package:core_error/core_error.dart';

/// 优化的图片缓存管理器
@singleton
class OptimizedImageCache {
  OptimizedImageCache(this._logger) {
    _setupImageCache();
  }

  final LoggerService _logger;
  
  // 图片缓存配置
  static const int _maxCacheSize = 100 * 1024 * 1024; // 100MB
  static const int _maxCacheObjects = 1000;
  
  void _setupImageCache() {
    // 设置图片缓存大小
    PaintingBinding.instance.imageCache.maximumSize = _maxCacheObjects;
    PaintingBinding.instance.imageCache.maximumSizeBytes = _maxCacheSize;
    
    _logger.info('Image cache configured: ${_maxCacheObjects} objects, ${_formatBytes(_maxCacheSize)}');
  }

  /// 预加载图片
  Future<void> preloadImage(String imageUrl, BuildContext context) async {
    try {
      final imageProvider = NetworkImage(imageUrl);
      await precacheImage(imageProvider, context);
      _logger.debug('Image preloaded: $imageUrl');
    } catch (e) {
      _logger.error('Failed to preload image: $imageUrl', error: e);
    }
  }

  /// 批量预加载图片
  Future<void> preloadImages(List<String> imageUrls, BuildContext context) async {
    final futures = imageUrls.map((url) => preloadImage(url, context));
    await Future.wait(futures);
    _logger.info('Preloaded ${imageUrls.length} images');
  }

  /// 清理图片缓存
  void clearImageCache() {
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
    _logger.info('Image cache cleared');
  }

  /// 获取缓存状态
  ImageCacheStatus getCacheStatus() {
    final cache = PaintingBinding.instance.imageCache;
    return ImageCacheStatus(
      currentSize: cache.currentSize,
      currentSizeBytes: cache.currentSizeBytes,
      maximumSize: cache.maximumSize,
      maximumSizeBytes: cache.maximumSizeBytes,
      liveImageCount: cache.liveImageCount,
      pendingImageCount: cache.pendingImageCount,
    );
  }

  /// 优化图片大小
  Future<Uint8List> optimizeImage(
    Uint8List imageData, {
    int? maxWidth,
    int? maxHeight,
    int quality = 85,
  }) async {
    try {
      final codec = await ui.instantiateImageCodec(
        imageData,
        targetWidth: maxWidth,
        targetHeight: maxHeight,
      );
      
      final frame = await codec.getNextFrame();
      final image = frame.image;
      
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final optimizedData = byteData!.buffer.asUint8List();
      
      _logger.debug('Image optimized: ${imageData.length} -> ${optimizedData.length} bytes');
      
      return optimizedData;
    } catch (e) {
      _logger.error('Failed to optimize image', error: e);
      return imageData;
    }
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}

/// 图片缓存状态
class ImageCacheStatus {
  const ImageCacheStatus({
    required this.currentSize,
    required this.currentSizeBytes,
    required this.maximumSize,
    required this.maximumSizeBytes,
    required this.liveImageCount,
    required this.pendingImageCount,
  });

  final int currentSize;
  final int currentSizeBytes;
  final int maximumSize;
  final int maximumSizeBytes;
  final int liveImageCount;
  final int pendingImageCount;

  double get usagePercentage => (currentSize / maximumSize) * 100;
  double get sizeUsagePercentage => (currentSizeBytes / maximumSizeBytes) * 100;

  Map<String, dynamic> toMap() {
    return {
      'currentSize': currentSize,
      'currentSizeBytes': currentSizeBytes,
      'maximumSize': maximumSize,
      'maximumSizeBytes': maximumSizeBytes,
      'liveImageCount': liveImageCount,
      'pendingImageCount': pendingImageCount,
      'usagePercentage': usagePercentage,
      'sizeUsagePercentage': sizeUsagePercentage,
    };
  }
}

/// 优化的网络图片组件
class OptimizedNetworkImage extends StatelessWidget {
  const OptimizedNetworkImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.fadeInDuration = const Duration(milliseconds: 300),
    this.cacheWidth,
    this.cacheHeight,
  });

  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final Duration fadeInDuration;
  final int? cacheWidth;
  final int? cacheHeight;

  @override
  Widget build(BuildContext context) {
    return Image.network(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) {
          return AnimatedOpacity(
            opacity: 1.0,
            duration: fadeInDuration,
            child: child,
          );
        }
        
        return placeholder ??
            Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                    : null,
              ),
            );
      },
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ??
            Container(
              width: width,
              height: height,
              color: Colors.grey[300],
              child: const Icon(
                Icons.error,
                color: Colors.grey,
              ),
            );
      },
    );
  }
}
```

## 2. 渲染性能优化

### 性能监控工具
```dart
// packages/core/core_performance/lib/src/performance_monitor.dart
import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:injectable/injectable.dart';
import 'package:core_error/core_error.dart';

/// 性能指标
class PerformanceMetrics {
  const PerformanceMetrics({
    required this.frameTime,
    required this.fps,
    required this.jankCount,
    required this.timestamp,
  });

  final Duration frameTime;
  final double fps;
  final int jankCount;
  final DateTime timestamp;

  bool get isJanky => frameTime.inMilliseconds > 16; // 超过16ms认为是卡顿

  Map<String, dynamic> toMap() {
    return {
      'frameTime': frameTime.inMicroseconds,
      'fps': fps,
      'jankCount': jankCount,
      'isJanky': isJanky,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// 性能监控服务
abstract class PerformanceMonitor {
  Stream<PerformanceMetrics> get metricsStream;
  
  void startMonitoring();
  void stopMonitoring();
  
  PerformanceMetrics? get currentMetrics;
  List<PerformanceMetrics> getRecentMetrics({int count = 100});
  
  void markFrameStart();
  void markFrameEnd();
}

@Injectable(as: PerformanceMonitor)
class PerformanceMonitorImpl implements PerformanceMonitor {
  PerformanceMonitorImpl(this._logger);

  final LoggerService _logger;
  
  final StreamController<PerformanceMetrics> _metricsController = StreamController<PerformanceMetrics>.broadcast();
  final List<PerformanceMetrics> _recentMetrics = [];
  
  bool _isMonitoring = false;
  DateTime? _frameStartTime;
  int _jankCount = 0;
  final List<Duration> _frameTimes = [];
  
  static const int _maxRecentMetrics = 1000;
  static const Duration _targetFrameTime = Duration(milliseconds: 16); // 60 FPS

  @override
  Stream<PerformanceMetrics> get metricsStream => _metricsController.stream;

  @override
  PerformanceMetrics? get currentMetrics => _recentMetrics.isNotEmpty ? _recentMetrics.last : null;

  @override
  void startMonitoring() {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    _jankCount = 0;
    _frameTimes.clear();
    
    // 监听帧回调
    SchedulerBinding.instance.addPersistentFrameCallback(_onFrame);
    
    _logger.info('Performance monitoring started');
  }

  @override
  void stopMonitoring() {
    if (!_isMonitoring) return;
    
    _isMonitoring = false;
    
    // 移除帧回调
    SchedulerBinding.instance.removePersistentFrameCallback(_onFrame);
    
    _logger.info('Performance monitoring stopped');
  }

  void _onFrame(Duration timestamp) {
    if (!_isMonitoring) return;
    
    final now = DateTime.now();
    
    if (_frameStartTime != null) {
      final frameTime = now.difference(_frameStartTime!);
      _frameTimes.add(frameTime);
      
      // 保持最近100帧的数据
      if (_frameTimes.length > 100) {
        _frameTimes.removeAt(0);
      }
      
      // 检查是否卡顿
      if (frameTime > _targetFrameTime) {
        _jankCount++;
      }
      
      // 计算FPS
      final fps = _calculateFPS();
      
      // 创建性能指标
      final metrics = PerformanceMetrics(
        frameTime: frameTime,
        fps: fps,
        jankCount: _jankCount,
        timestamp: now,
      );
      
      // 添加到最近指标列表
      _recentMetrics.add(metrics);
      if (_recentMetrics.length > _maxRecentMetrics) {
        _recentMetrics.removeAt(0);
      }
      
      // 发送到流
      _metricsController.add(metrics);
      
      // 记录严重的性能问题
      if (frameTime.inMilliseconds > 32) { // 超过32ms
        _logger.warning('Severe frame jank detected: ${frameTime.inMilliseconds}ms');
      }
    }
    
    _frameStartTime = now;
  }

  double _calculateFPS() {
    if (_frameTimes.isEmpty) return 0.0;
    
    final totalTime = _frameTimes.fold<Duration>(
      Duration.zero,
      (sum, frameTime) => sum + frameTime,
    );
    
    final averageFrameTime = totalTime.inMicroseconds / _frameTimes.length;
    return 1000000 / averageFrameTime; // 1秒 = 1,000,000微秒
  }

  @override
  List<PerformanceMetrics> getRecentMetrics({int count = 100}) {
    final startIndex = _recentMetrics.length > count ? _recentMetrics.length - count : 0;
    return _recentMetrics.sublist(startIndex);
  }

  @override
  void markFrameStart() {
    _frameStartTime = DateTime.now();
  }

  @override
  void markFrameEnd() {
    if (_frameStartTime == null) return;
    
    final frameTime = DateTime.now().difference(_frameStartTime!);
    _frameTimes.add(frameTime);
    
    if (frameTime > _targetFrameTime) {
      _jankCount++;
    }
  }

  void dispose() {
    stopMonitoring();
    _metricsController.close();
  }
}
```

### 优化的列表组件
```dart
// packages/core/core_performance/lib/src/optimized_list_view.dart
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

/// 优化的列表视图
class OptimizedListView<T> extends StatefulWidget {
  const OptimizedListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.separatorBuilder,
    this.onLoadMore,
    this.onRefresh,
    this.loadingWidget,
    this.emptyWidget,
    this.errorWidget,
    this.isLoading = false,
    this.hasError = false,
    this.hasReachedMax = false,
    this.cacheExtent = 250.0,
    this.itemExtent,
    this.physics,
    this.controller,
  });

  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final Widget Function(BuildContext context, int index)? separatorBuilder;
  final VoidCallback? onLoadMore;
  final Future<void> Function()? onRefresh;
  final Widget? loadingWidget;
  final Widget? emptyWidget;
  final Widget? errorWidget;
  final bool isLoading;
  final bool hasError;
  final bool hasReachedMax;
  final double cacheExtent;
  final double? itemExtent;
  final ScrollPhysics? physics;
  final ScrollController? controller;

  @override
  State<OptimizedListView<T>> createState() => _OptimizedListViewState<T>();
}

class _OptimizedListViewState<T> extends State<OptimizedListView<T>> {
  late ScrollController _scrollController;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    } else {
      _scrollController.removeListener(_onScroll);
    }
    super.dispose();
  }

  void _onScroll() {
    if (_isLoadingMore || widget.hasReachedMax || widget.onLoadMore == null) return;
    
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.position.pixels;
    const threshold = 200.0; // 距离底部200像素时开始加载
    
    if (currentScroll >= maxScroll - threshold) {
      _loadMore();
    }
  }

  Future<void> _loadMore() async {
    if (_isLoadingMore) return;
    
    setState(() {
      _isLoadingMore = true;
    });
    
    widget.onLoadMore?.call();
    
    // 等待一小段时间，确保状态更新
    await Future.delayed(const Duration(milliseconds: 100));
    
    if (mounted) {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.hasError) {
      return widget.errorWidget ?? _buildDefaultErrorWidget();
    }
    
    if (widget.items.isEmpty && !widget.isLoading) {
      return widget.emptyWidget ?? _buildDefaultEmptyWidget();
    }
    
    Widget listView = ListView.separated(
      controller: _scrollController,
      physics: widget.physics,
      cacheExtent: widget.cacheExtent,
      itemExtent: widget.itemExtent,
      itemCount: _getItemCount(),
      separatorBuilder: widget.separatorBuilder ?? (_, __) => const SizedBox.shrink(),
      itemBuilder: (context, index) {
        if (index < widget.items.length) {
          return widget.itemBuilder(context, widget.items[index], index);
        } else {
          return _buildLoadingIndicator();
        }
      },
    );
    
    if (widget.onRefresh != null) {
      listView = RefreshIndicator(
        onRefresh: widget.onRefresh!,
        child: listView,
      );
    }
    
    return listView;
  }

  int _getItemCount() {
    int count = widget.items.length;
    
    // 如果正在加载更多或还有更多数据，添加加载指示器
    if ((widget.isLoading && widget.items.isEmpty) || 
        (_isLoadingMore && !widget.hasReachedMax)) {
      count += 1;
    }
    
    return count;
  }

  Widget _buildLoadingIndicator() {
    return widget.loadingWidget ??
        const Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        );
  }

  Widget _buildDefaultErrorWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '加载失败',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultEmptyWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '暂无数据',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}

/// 优化的网格视图
class OptimizedGridView<T> extends StatefulWidget {
  const OptimizedGridView({
    super.key,
    required this.items,
    required this.itemBuilder,
    required this.crossAxisCount,
    this.onLoadMore,
    this.onRefresh,
    this.loadingWidget,
    this.emptyWidget,
    this.errorWidget,
    this.isLoading = false,
    this.hasError = false,
    this.hasReachedMax = false,
    this.cacheExtent = 250.0,
    this.childAspectRatio = 1.0,
    this.crossAxisSpacing = 0.0,
    this.mainAxisSpacing = 0.0,
    this.physics,
    this.controller,
  });

  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final int crossAxisCount;
  final VoidCallback? onLoadMore;
  final Future<void> Function()? onRefresh;
  final Widget? loadingWidget;
  final Widget? emptyWidget;
  final Widget? errorWidget;
  final bool isLoading;
  final bool hasError;
  final bool hasReachedMax;
  final double cacheExtent;
  final double childAspectRatio;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  final ScrollPhysics? physics;
  final ScrollController? controller;

  @override
  State<OptimizedGridView<T>> createState() => _OptimizedGridViewState<T>();
}

class _OptimizedGridViewState<T> extends State<OptimizedGridView<T>> {
  late ScrollController _scrollController;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    } else {
      _scrollController.removeListener(_onScroll);
    }
    super.dispose();
  }

  void _onScroll() {
    if (_isLoadingMore || widget.hasReachedMax || widget.onLoadMore == null) return;
    
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.position.pixels;
    const threshold = 200.0;
    
    if (currentScroll >= maxScroll - threshold) {
      _loadMore();
    }
  }

  Future<void> _loadMore() async {
    if (_isLoadingMore) return;
    
    setState(() {
      _isLoadingMore = true;
    });
    
    widget.onLoadMore?.call();
    
    await Future.delayed(const Duration(milliseconds: 100));
    
    if (mounted) {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.hasError) {
      return widget.errorWidget ?? _buildDefaultErrorWidget();
    }
    
    if (widget.items.isEmpty && !widget.isLoading) {
      return widget.emptyWidget ?? _buildDefaultEmptyWidget();
    }
    
    Widget gridView = GridView.builder(
      controller: _scrollController,
      physics: widget.physics,
      cacheExtent: widget.cacheExtent,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.crossAxisCount,
        childAspectRatio: widget.childAspectRatio,
        crossAxisSpacing: widget.crossAxisSpacing,
        mainAxisSpacing: widget.mainAxisSpacing,
      ),
      itemCount: _getItemCount(),
      itemBuilder: (context, index) {
        if (index < widget.items.length) {
          return widget.itemBuilder(context, widget.items[index], index);
        } else {
          return _buildLoadingIndicator();
        }
      },
    );
    
    if (widget.onRefresh != null) {
      gridView = RefreshIndicator(
        onRefresh: widget.onRefresh!,
        child: gridView,
      );
    }
    
    return gridView;
  }

  int _getItemCount() {
    int count = widget.items.length;
    
    if ((widget.isLoading && widget.items.isEmpty) || 
        (_isLoadingMore && !widget.hasReachedMax)) {
      // 对于网格，加载指示器需要占满一行
      final remainder = count % widget.crossAxisCount;
      if (remainder != 0) {
        count += widget.crossAxisCount - remainder;
      } else {
        count += widget.crossAxisCount;
      }
    }
    
    return count;
  }

  Widget _buildLoadingIndicator() {
    return widget.loadingWidget ??
        const Center(
          child: CircularProgressIndicator(),
        );
  }

  Widget _buildDefaultErrorWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '加载失败',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultEmptyWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '暂无数据',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}
```

## 3. 网络性能优化

### 网络请求优化器
```dart
// packages/core/core_performance/lib/src/network_optimizer.dart
import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:core_error/core_error.dart';

/// 网络请求优化器
@singleton
class NetworkOptimizer {
  NetworkOptimizer(this._logger);

  final LoggerService _logger;
  
  // 请求去重映射
  final Map<String, Completer<Response>> _pendingRequests = {};
  
  // 请求缓存
  final Map<String, CachedResponse> _responseCache = {};
  
  // 批量请求队列
  final Map<String, List<BatchRequest>> _batchQueues = {};
  Timer? _batchTimer;
  
  static const Duration _batchDelay = Duration(milliseconds: 100);
  static const Duration _defaultCacheTimeout = Duration(minutes: 5);

  /// 去重请求 - 防止相同请求并发执行
  Future<Response> deduplicateRequest(
    String key,
    Future<Response> Function() requestFunction,
  ) async {
    // 如果已有相同请求在进行中，等待其完成
    if (_pendingRequests.containsKey(key)) {
      _logger.debug('Request deduplicated: $key');
      return _pendingRequests[key]!.future;
    }
    
    // 创建新的请求
    final completer = Completer<Response>();
    _pendingRequests[key] = completer;
    
    try {
      final response = await requestFunction();
      completer.complete(response);
      return response;
    } catch (e) {
      completer.completeError(e);
      rethrow;
    } finally {
      _pendingRequests.remove(key);
    }
  }

  /// 缓存响应
  void cacheResponse(
    String key,
    Response response, {
    Duration? timeout,
  }) {
    final cachedResponse = CachedResponse(
      response: response,
      timestamp: DateTime.now(),
      timeout: timeout ?? _defaultCacheTimeout,
    );
    
    _responseCache[key] = cachedResponse;
    _logger.debug('Response cached: $key');
    
    // 清理过期缓存
    _cleanupExpiredCache();
  }

  /// 获取缓存的响应
  Response? getCachedResponse(String key) {
    final cached = _responseCache[key];
    if (cached == null) return null;
    
    if (cached.isExpired) {
      _responseCache.remove(key);
      _logger.debug('Cached response expired: $key');
      return null;
    }
    
    _logger.debug('Using cached response: $key');
    return cached.response;
  }

  /// 批量请求
  Future<List<Response>> batchRequest(
    String batchKey,
    String requestKey,
    Map<String, dynamic> requestData,
    Future<List<Response>> Function(List<Map<String, dynamic>>) batchFunction,
  ) async {
    final completer = Completer<Response>();
    
    final batchRequest = BatchRequest(
      key: requestKey,
      data: requestData,
      completer: completer,
    );
    
    // 添加到批量队列
    _batchQueues.putIfAbsent(batchKey, () => []).add(batchRequest);
    
    // 设置批量处理定时器
    _batchTimer?.cancel();
    _batchTimer = Timer(_batchDelay, () => _processBatch(batchKey, batchFunction));
    
    return [await completer.future];
  }

  Future<void> _processBatch(
    String batchKey,
    Future<List<Response>> Function(List<Map<String, dynamic>>) batchFunction,
  ) async {
    final requests = _batchQueues.remove(batchKey);
    if (requests == null || requests.isEmpty) return;
    
    _logger.debug('Processing batch: $batchKey with ${requests.length} requests');
    
    try {
      final requestDataList = requests.map((r) => r.data).toList();
      final responses = await batchFunction(requestDataList);
      
      // 分发响应给各个请求
      for (int i = 0; i < requests.length && i < responses.length; i++) {
        requests[i].completer.complete(responses[i]);
      }
      
      // 处理剩余的请求（如果响应数量不足）
      for (int i = responses.length; i < requests.length; i++) {
        requests[i].completer.completeError(
          Exception('Batch response insufficient'),
        );
      }
    } catch (e) {
      // 所有请求都失败
      for (final request in requests) {
        request.request.completer.completeError(e);
      }
    }
  }

  /// 预取数据
  Future<void> prefetchData(
    String key,
    Future<Response> Function() requestFunction, {
    Duration? cacheTimeout,
  }) async {
    try {
      final response = await requestFunction();
      cacheResponse(key, response, timeout: cacheTimeout);
      _logger.debug('Data prefetched: $key');
    } catch (e) {
      _logger.error('Failed to prefetch data: $key', error: e);
    }
  }

  /// 清理过期缓存
  void _cleanupExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    _responseCache.forEach((key, cached) {
      if (cached.isExpired) {
        expiredKeys.add(key);
      }
    });
    
    for (final key in expiredKeys) {
      _responseCache.remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      _logger.debug('Cleaned up ${expiredKeys.length} expired cache entries');
    }
  }

  /// 清理所有缓存
  void clearCache() {
    _responseCache.clear();
    _logger.info('All cache cleared');
  }

  /// 获取缓存统计信息
  CacheStatistics getCacheStatistics() {
    int validCount = 0;
    int expiredCount = 0;
    int totalSize = 0;
    
    _responseCache.forEach((key, cached) {
      if (cached.isExpired) {
        expiredCount++;
      } else {
        validCount++;
      }
      
      // 估算缓存大小
      final responseData = cached.response.data;
      if (responseData is String) {
        totalSize += responseData.length;
      } else if (responseData is Map || responseData is List) {
        totalSize += jsonEncode(responseData).length;
      }
    });
    
    return CacheStatistics(
      validCount: validCount,
      expiredCount: expiredCount,
      totalSize: totalSize,
      pendingRequestCount: _pendingRequests.length,
    );
  }

  void dispose() {
    _batchTimer?.cancel();
    _pendingRequests.clear();
    _responseCache.clear();
    _batchQueues.clear();
  }
}

/// 缓存的响应
class CachedResponse {
  const CachedResponse({
    required this.response,
    required this.timestamp,
    required this.timeout,
  });

  final Response response;
  final DateTime timestamp;
  final Duration timeout;

  bool get isExpired => DateTime.now().difference(timestamp) > timeout;
}

/// 批量请求项
class BatchRequest {
  const BatchRequest({
    required this.key,
    required this.data,
    required this.completer,
  });

  final String key;
  final Map<String, dynamic> data;
  final Completer<Response> completer;
}

/// 缓存统计信息
class CacheStatistics {
  const CacheStatistics({
    required this.validCount,
    required this.expiredCount,
    required this.totalSize,
    required this.pendingRequestCount,
  });

  final int validCount;
  final int expiredCount;
  final int totalSize;
  final int pendingRequestCount;

  int get totalCount => validCount + expiredCount;
  
  String get formattedSize {
    if (totalSize < 1024) return '$totalSize B';
    if (totalSize < 1024 * 1024) return '${(totalSize / 1024).toStringAsFixed(1)} KB';
    return '${(totalSize / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  Map<String, dynamic> toMap() {
    return {
      'validCount': validCount,
      'expiredCount': expiredCount,
      'totalCount': totalCount,
      'totalSize': totalSize,
      'formattedSize': formattedSize,
      'pendingRequestCount': pendingRequestCount,
    };
  }
}
```

## 4. 性能分析工具

### 性能分析器
```dart
// packages/core/core_performance/lib/src/performance_analyzer.dart
import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:core_error/core_error.dart';
import 'memory_monitor.dart';
import 'performance_monitor.dart';
import 'network_optimizer.dart';

/// 性能分析报告
class PerformanceReport {
  const PerformanceReport({
    required this.memoryInfo,
    required this.performanceMetrics,
    required this.cacheStatistics,
    required this.timestamp,
    this.recommendations = const [],
  });

  final MemoryInfo memoryInfo;
  final PerformanceMetrics performanceMetrics;
  final CacheStatistics cacheStatistics;
  final DateTime timestamp;
  final List<String> recommendations;

  Map<String, dynamic> toMap() {
    return {
      'memoryInfo': memoryInfo.toMap(),
      'performanceMetrics': performanceMetrics.toMap(),
      'cacheStatistics': cacheStatistics.toMap(),
      'timestamp': timestamp.toIso8601String(),
      'recommendations': recommendations,
    };
  }
}

/// 性能分析器
@singleton
class PerformanceAnalyzer {
  PerformanceAnalyzer(
    this._memoryMonitor,
    this._performanceMonitor,
    this._networkOptimizer,
    this._logger,
  );

  final MemoryMonitor _memoryMonitor;
  final PerformanceMonitor _performanceMonitor;
  final NetworkOptimizer _networkOptimizer;
  final LoggerService _logger;
  
  final StreamController<PerformanceReport> _reportController = StreamController<PerformanceReport>.broadcast();
  Timer? _analysisTimer;
  
  bool _isAnalyzing = false;

  Stream<PerformanceReport> get reportStream => _reportController.stream;

  /// 开始性能分析
  void startAnalysis({Duration interval = const Duration(seconds: 30)}) {
    if (_isAnalyzing) return;
    
    _isAnalyzing = true;
    
    // 启动各个监控器
    _memoryMonitor.startMonitoring();
    _performanceMonitor.startMonitoring();
    
    // 定期生成分析报告
    _analysisTimer = Timer.periodic(interval, (_) => _generateReport());
    
    _logger.info('Performance analysis started with interval: $interval');
  }

  /// 停止性能分析
  void stopAnalysis() {
    if (!_isAnalyzing) return;
    
    _isAnalyzing = false;
    
    // 停止监控器
    _memoryMonitor.stopMonitoring();
    _performanceMonitor.stopMonitoring();
    
    // 停止定时器
    _analysisTimer?.cancel();
    _analysisTimer = null;
    
    _logger.info('Performance analysis stopped');
  }

  /// 生成性能报告
  Future<PerformanceReport> _generateReport() async {
    try {
      final memoryInfo = await _memoryMonitor.getCurrentMemoryInfo();
      final performanceMetrics = _performanceMonitor.currentMetrics ?? 
          const PerformanceMetrics(
            frameTime: Duration.zero,
            fps: 0,
            jankCount: 0,
            timestamp: null,
          );
      final cacheStatistics = _networkOptimizer.getCacheStatistics();
      
      final recommendations = _generateRecommendations(
        memoryInfo,
        performanceMetrics,
        cacheStatistics,
      );
      
      final report = PerformanceReport(
        memoryInfo: memoryInfo,
        performanceMetrics: performanceMetrics,
        cacheStatistics: cacheStatistics,
        timestamp: DateTime.now(),
        recommendations: recommendations,
      );
      
      _reportController.add(report);
      
      // 记录严重的性能问题
      if (recommendations.isNotEmpty) {
        _logger.warning('Performance issues detected: ${recommendations.join(', ')}');
      }
      
      return report;
    } catch (e) {
      _logger.error('Failed to generate performance report', error: e);
      rethrow;
    }
  }

  /// 生成性能建议
  List<String> _generateRecommendations(
    MemoryInfo memoryInfo,
    PerformanceMetrics performanceMetrics,
    CacheStatistics cacheStatistics,
  ) {
    final recommendations = <String>[];
    
    // 内存相关建议
    if (memoryInfo.usagePercentage > 85) {
      recommendations.add('内存使用率过高 (${memoryInfo.usagePercentage.toStringAsFixed(1)}%)，建议清理缓存或优化内存使用');
    } else if (memoryInfo.usagePercentage > 70) {
      recommendations.add('内存使用率较高 (${memoryInfo.usagePercentage.toStringAsFixed(1)}%)，建议监控内存使用情况');
    }
    
    // 性能相关建议
    if (performanceMetrics.fps < 30) {
      recommendations.add('帧率过低 (${performanceMetrics.fps.toStringAsFixed(1)} FPS)，建议优化渲染性能');
    } else if (performanceMetrics.fps < 50) {
      recommendations.add('帧率较低 (${performanceMetrics.fps.toStringAsFixed(1)} FPS)，建议检查渲染优化');
    }
    
    if (performanceMetrics.jankCount > 10) {
      recommendations.add('检测到较多卡顿 (${performanceMetrics.jankCount} 次)，建议优化UI渲染');
    }
    
    // 缓存相关建议
    if (cacheStatistics.expiredCount > cacheStatistics.validCount) {
      recommendations.add('缓存过期率过高，建议调整缓存策略');
    }
    
    if (cacheStatistics.totalSize > 50 * 1024 * 1024) { // 50MB
      recommendations.add('缓存占用过大 (${cacheStatistics.formattedSize})，建议清理缓存');
    }
    
    if (cacheStatistics.pendingRequestCount > 20) {
      recommendations.add('并发请求过多 (${cacheStatistics.pendingRequestCount})，建议优化请求策略');
    }
    
    return recommendations;
  }

  /// 手动生成报告
  Future<PerformanceReport> generateManualReport() async {
    return _generateReport();
  }

  /// 获取性能概览
  Future<Map<String, dynamic>> getPerformanceOverview() async {
    final report = await _generateReport();
    
    return {
      'memory': {
        'usage': '${report.memoryInfo.formattedUsedMemory}/${report.memoryInfo.formattedTotalMemory}',
        'percentage': '${report.memoryInfo.usagePercentage.toStringAsFixed(1)}%',
      },
      'performance': {
        'fps': report.performanceMetrics.fps.toStringAsFixed(1),
        'jankCount': report.performanceMetrics.jankCount,
        'frameTime': '${report.performanceMetrics.frameTime.inMilliseconds}ms',
      },
      'cache': {
        'size': report.cacheStatistics.formattedSize,
        'count': report.cacheStatistics.totalCount,
        'pendingRequests': report.cacheStatistics.pendingRequestCount,
      },
      'recommendations': report.recommendations,
    };
  }

  /// 性能基准测试
  Future<Map<String, dynamic>> runBenchmark() async {
    _logger.info('Starting performance benchmark');
    
    final stopwatch = Stopwatch()..start();
    
    // CPU密集型测试
    stopwatch.reset();
    _runCpuBenchmark();
    final cpuTime = stopwatch.elapsedMilliseconds;
    
    // 内存分配测试
    stopwatch.reset();
    _runMemoryBenchmark();
    final memoryTime = stopwatch.elapsedMilliseconds;
    
    // 渲染性能测试
    stopwatch.reset();
    await _runRenderBenchmark();
    final renderTime = stopwatch.elapsedMilliseconds;
    
    final results = {
      'cpu': {
        'time': cpuTime,
        'score': _calculateCpuScore(cpuTime),
      },
      'memory': {
        'time': memoryTime,
        'score': _calculateMemoryScore(memoryTime),
      },
      'render': {
        'time': renderTime,
        'score': _calculateRenderScore(renderTime),
      },
    };
    
    _logger.info('Performance benchmark completed: $results');
    
    return results;
  }

  void _runCpuBenchmark() {
    // 简单的CPU密集型计算
    int sum = 0;
    for (int i = 0; i < 1000000; i++) {
      sum += i * i;
    }
  }

  void _runMemoryBenchmark() {
    // 内存分配和释放测试
    final lists = <List<int>>[];
    for (int i = 0; i < 1000; i++) {
      lists.add(List.generate(1000, (index) => index));
    }
    lists.clear();
  }

  Future<void> _runRenderBenchmark() async {
    // 模拟渲染操作
    for (int i = 0; i < 100; i++) {
      await Future.delayed(const Duration(microseconds: 100));
    }
  }

  int _calculateCpuScore(int timeMs) {
    // 基准时间100ms，得分100
    return (10000 / timeMs).round().clamp(0, 1000);
  }

  int _calculateMemoryScore(int timeMs) {
    // 基准时间50ms，得分100
    return (5000 / timeMs).round().clamp(0, 1000);
  }

  int _calculateRenderScore(int timeMs) {
    // 基准时间200ms，得分100
    return (20000 / timeMs).round().clamp(0, 1000);
  }

  void dispose() {
    stopAnalysis();
    _reportController.close();
  }
}
```

这个性能优化实现提供了：

1. **内存管理和监控**：实时监控内存使用情况，自动垃圾回收，内存警告机制
2. **图片缓存优化**：智能图片缓存管理，图片预加载，图片尺寸优化
3. **渲染性能优化**：帧率监控，卡顿检测，优化的列表和网格组件
4. **网络性能优化**：请求去重，响应缓存，批量请求，数据预取
5. **性能分析工具**：综合性能报告，性能建议，基准测试

## 5. 启动性能优化

### 启动时间优化器
```dart
// packages/core/core_performance/lib/src/startup_optimizer.dart
import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:injectable/injectable.dart';
import 'package:core_error/core_error.dart';

/// 启动阶段
enum StartupPhase {
  preInit,
  init,
  postInit,
  ready,
}

/// 启动任务
class StartupTask {
  const StartupTask({
    required this.name,
    required this.phase,
    required this.task,
    this.priority = 0,
    this.dependencies = const [],
    this.timeout,
  });

  final String name;
  final StartupPhase phase;
  final Future<void> Function() task;
  final int priority; // 优先级，数字越大优先级越高
  final List<String> dependencies; // 依赖的任务名称
  final Duration? timeout;
}

/// 启动性能指标
class StartupMetrics {
  const StartupMetrics({
    required this.totalTime,
    required this.phaseMetrics,
    required this.taskMetrics,
    required this.timestamp,
  });

  final Duration totalTime;
  final Map<StartupPhase, Duration> phaseMetrics;
  final Map<String, Duration> taskMetrics;
  final DateTime timestamp;

  Map<String, dynamic> toMap() {
    return {
      'totalTime': totalTime.inMilliseconds,
      'phaseMetrics': phaseMetrics.map(
        (phase, duration) => MapEntry(phase.name, duration.inMilliseconds),
      ),
      'taskMetrics': taskMetrics.map(
        (name, duration) => MapEntry(name, duration.inMilliseconds),
      ),
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// 启动时间优化器
@singleton
class StartupOptimizer {
  StartupOptimizer(this._logger);

  final LoggerService _logger;
  
  final List<StartupTask> _tasks = [];
  final Map<String, Completer<void>> _taskCompleters = {};
  final Map<String, Duration> _taskDurations = {};
  final Map<StartupPhase, Duration> _phaseDurations = {};
  
  DateTime? _startupStartTime;
  DateTime? _startupEndTime;
  
  bool _isOptimizing = false;

  /// 注册启动任务
  void registerTask(StartupTask task) {
    _tasks.add(task);
    _logger.debug('Startup task registered: ${task.name} (${task.phase.name})');
  }

  /// 批量注册启动任务
  void registerTasks(List<StartupTask> tasks) {
    _tasks.addAll(tasks);
    _logger.debug('${tasks.length} startup tasks registered');
  }

  /// 开始启动优化
  Future<StartupMetrics> optimize() async {
    if (_isOptimizing) {
      throw StateError('Startup optimization already in progress');
    }
    
    _isOptimizing = true;
    _startupStartTime = DateTime.now();
    
    _logger.info('Starting startup optimization with ${_tasks.length} tasks');
    
    try {
      // 按阶段执行任务
      for (final phase in StartupPhase.values) {
        await _executePhase(phase);
      }
      
      _startupEndTime = DateTime.now();
      
      final metrics = _generateMetrics();
      _logger.info('Startup optimization completed in ${metrics.totalTime.inMilliseconds}ms');
      
      return metrics;
    } catch (e) {
      _logger.error('Startup optimization failed', error: e);
      rethrow;
    } finally {
      _isOptimizing = false;
    }
  }

  /// 执行指定阶段的任务
  Future<void> _executePhase(StartupPhase phase) async {
    final phaseStartTime = DateTime.now();
    
    // 获取当前阶段的任务
    final phaseTasks = _tasks.where((task) => task.phase == phase).toList();
    
    if (phaseTasks.isEmpty) {
      _phaseDurations[phase] = Duration.zero;
      return;
    }
    
    _logger.debug('Executing ${phaseTasks.length} tasks in phase: ${phase.name}');
    
    // 按优先级排序
    phaseTasks.sort((a, b) => b.priority.compareTo(a.priority));
    
    // 解析依赖关系并执行任务
    await _executeTasks(phaseTasks);
    
    final phaseDuration = DateTime.now().difference(phaseStartTime);
    _phaseDurations[phase] = phaseDuration;
    
    _logger.debug('Phase ${phase.name} completed in ${phaseDuration.inMilliseconds}ms');
  }

  /// 执行任务列表（考虑依赖关系）
  Future<void> _executeTasks(List<StartupTask> tasks) async {
    final remainingTasks = List<StartupTask>.from(tasks);
    final completedTasks = <String>{};
    
    while (remainingTasks.isNotEmpty) {
      final readyTasks = remainingTasks.where((task) {
        return task.dependencies.every((dep) => completedTasks.contains(dep));
      }).toList();
      
      if (readyTasks.isEmpty) {
        final pendingDeps = remainingTasks
            .expand((task) => task.dependencies)
            .where((dep) => !completedTasks.contains(dep))
            .toSet();
        throw StateError('Circular dependency detected. Pending dependencies: $pendingDeps');
      }
      
      // 并行执行准备好的任务
      final futures = readyTasks.map((task) => _executeTask(task));
      await Future.wait(futures);
      
      // 标记任务为已完成
      for (final task in readyTasks) {
        completedTasks.add(task.name);
        remainingTasks.remove(task);
      }
    }
  }

  /// 执行单个任务
  Future<void> _executeTask(StartupTask task) async {
    final taskStartTime = DateTime.now();
    
    _logger.debug('Executing task: ${task.name}');
    
    try {
      if (task.timeout != null) {
        await task.task().timeout(task.timeout!);
      } else {
        await task.task();
      }
      
      final taskDuration = DateTime.now().difference(taskStartTime);
      _taskDurations[task.name] = taskDuration;
      
      _logger.debug('Task ${task.name} completed in ${taskDuration.inMilliseconds}ms');
    } catch (e) {
      _logger.error('Task ${task.name} failed', error: e);
      rethrow;
    }
  }

  /// 生成启动指标
  StartupMetrics _generateMetrics() {
    final totalTime = _startupEndTime!.difference(_startupStartTime!);
    
    return StartupMetrics(
      totalTime: totalTime,
      phaseMetrics: Map.from(_phaseDurations),
      taskMetrics: Map.from(_taskDurations),
      timestamp: _startupStartTime!,
    );
  }

  /// 预热关键组件
  Future<void> warmupComponents() async {
    _logger.debug('Warming up components');
    
    // 预热图片缓存
    PaintingBinding.instance.imageCache.maximumSize = 1000;
    
    // 预热字体
    await _warmupFonts();
    
    // 预热着色器
    await _warmupShaders();
    
    _logger.debug('Component warmup completed');
  }

  Future<void> _warmupFonts() async {
    try {
      // 预加载系统字体
      const textStyle = TextStyle(fontSize: 16);
      final painter = TextPainter(
        text: const TextSpan(text: 'Warmup', style: textStyle),
        textDirection: TextDirection.ltr,
      );
      painter.layout();
      painter.dispose();
    } catch (e) {
      _logger.warning('Font warmup failed', error: e);
    }
  }

  Future<void> _warmupShaders() async {
    try {
      // 预编译常用着色器
      await Future.delayed(const Duration(milliseconds: 10));
    } catch (e) {
      _logger.warning('Shader warmup failed', error: e);
    }
  }

  /// 获取启动建议
  List<String> getOptimizationSuggestions(StartupMetrics metrics) {
    final suggestions = <String>[];
    
    // 总启动时间建议
    if (metrics.totalTime.inMilliseconds > 3000) {
      suggestions.add('启动时间过长 (${metrics.totalTime.inMilliseconds}ms)，建议优化启动流程');
    }
    
    // 阶段时间建议
    metrics.phaseMetrics.forEach((phase, duration) {
      if (duration.inMilliseconds > 1000) {
        suggestions.add('${phase.name} 阶段耗时过长 (${duration.inMilliseconds}ms)');
      }
    });
    
    // 任务时间建议
    final slowTasks = metrics.taskMetrics.entries
        .where((entry) => entry.value.inMilliseconds > 500)
        .toList();
    
    if (slowTasks.isNotEmpty) {
      suggestions.add('发现 ${slowTasks.length} 个慢任务，建议优化：${slowTasks.map((e) => e.key).join(', ')}');
    }
    
    return suggestions;
  }

  void dispose() {
    _tasks.clear();
    _taskCompleters.clear();
    _taskDurations.clear();
    _phaseDurations.clear();
  }
}
```

### 懒加载管理器
```dart
// packages/core/core_performance/lib/src/lazy_loader.dart
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:core_error/core_error.dart';

/// 懒加载项
class LazyLoadItem<T> {
  LazyLoadItem({
    required this.key,
    required this.loader,
    this.priority = 0,
    this.timeout,
    this.retryCount = 3,
  });

  final String key;
  final Future<T> Function() loader;
  final int priority;
  final Duration? timeout;
  final int retryCount;
  
  T? _value;
  bool _isLoading = false;
  bool _isLoaded = false;
  Completer<T>? _completer;
  int _currentRetryCount = 0;

  bool get isLoaded => _isLoaded;
  bool get isLoading => _isLoading;
  T? get value => _value;
}

/// 懒加载管理器
@singleton
class LazyLoader {
  LazyLoader(this._logger);

  final LoggerService _logger;
  
  final Map<String, LazyLoadItem> _items = {};
  final Map<String, Timer> _preloadTimers = {};
  
  bool _isPreloading = false;

  /// 注册懒加载项
  void register<T>(LazyLoadItem<T> item) {
    _items[item.key] = item;
    _logger.debug('Lazy load item registered: ${item.key}');
  }

  /// 获取懒加载项的值
  Future<T> get<T>(String key) async {
    final item = _items[key] as LazyLoadItem<T>?;
    if (item == null) {
      throw ArgumentError('Lazy load item not found: $key');
    }
    
    if (item.isLoaded) {
      return item.value!;
    }
    
    if (item.isLoading) {
      return item._completer!.future;
    }
    
    return _loadItem(item);
  }

  /// 加载指定项
  Future<T> _loadItem<T>(LazyLoadItem<T> item) async {
    item._isLoading = true;
    item._completer = Completer<T>();
    
    _logger.debug('Loading lazy item: ${item.key}');
    
    try {
      T value;
      
      if (item.timeout != null) {
        value = await item.loader().timeout(item.timeout!);
      } else {
        value = await item.loader();
      }
      
      item._value = value;
      item._isLoaded = true;
      item._completer!.complete(value);
      
      _logger.debug('Lazy item loaded: ${item.key}');
      
      return value;
    } catch (e) {
      _logger.warning('Failed to load lazy item: ${item.key}', error: e);
      
      // 重试逻辑
      if (item._currentRetryCount < item.retryCount) {
        item._currentRetryCount++;
        item._isLoading = false;
        
        _logger.debug('Retrying lazy item: ${item.key} (${item._currentRetryCount}/${item.retryCount})');
        
        // 延迟重试
        await Future.delayed(Duration(milliseconds: 100 * item._currentRetryCount));
        
        return _loadItem(item);
      }
      
      item._completer!.completeError(e);
      rethrow;
    } finally {
      item._isLoading = false;
    }
  }

  /// 预加载所有项
  Future<void> preloadAll() async {
    if (_isPreloading) return;
    
    _isPreloading = true;
    
    _logger.info('Starting preload of ${_items.length} items');
    
    try {
      // 按优先级排序
      final sortedItems = _items.values.toList()
        ..sort((a, b) => b.priority.compareTo(a.priority));
      
      // 分批预加载
      const batchSize = 5;
      for (int i = 0; i < sortedItems.length; i += batchSize) {
        final batch = sortedItems.skip(i).take(batchSize);
        
        final futures = batch.map((item) async {
          try {
            await _loadItem(item);
          } catch (e) {
            _logger.warning('Preload failed for item: ${item.key}', error: e);
          }
        });
        
        await Future.wait(futures);
        
        // 小延迟，避免阻塞UI
        await Future.delayed(const Duration(milliseconds: 10));
      }
      
      _logger.info('Preload completed');
    } finally {
      _isPreloading = false;
    }
  }

  /// 预加载指定项
  Future<void> preload(List<String> keys) async {
    final items = keys
        .map((key) => _items[key])
        .where((item) => item != null && !item.isLoaded)
        .cast<LazyLoadItem>()
        .toList();
    
    if (items.isEmpty) return;
    
    _logger.debug('Preloading ${items.length} items');
    
    final futures = items.map((item) async {
      try {
        await _loadItem(item);
      } catch (e) {
        _logger.warning('Preload failed for item: ${item.key}', error: e);
      }
    });
    
    await Future.wait(futures);
  }

  /// 延迟预加载
  void schedulePreload(String key, Duration delay) {
    _preloadTimers[key]?.cancel();
    
    _preloadTimers[key] = Timer(delay, () async {
      try {
        await get(key);
      } catch (e) {
        _logger.warning('Scheduled preload failed for item: $key', error: e);
      } finally {
        _preloadTimers.remove(key);
      }
    });
    
    _logger.debug('Scheduled preload for item: $key in ${delay.inMilliseconds}ms');
  }

  /// 检查项是否已加载
  bool isLoaded(String key) {
    return _items[key]?.isLoaded ?? false;
  }

  /// 检查项是否正在加载
  bool isLoading(String key) {
    return _items[key]?.isLoading ?? false;
  }

  /// 清除指定项
  void clear(String key) {
    final item = _items[key];
    if (item != null) {
      item._value = null;
      item._isLoaded = false;
      item._currentRetryCount = 0;
      _logger.debug('Cleared lazy item: $key');
    }
    
    _preloadTimers[key]?.cancel();
    _preloadTimers.remove(key);
  }

  /// 清除所有项
  void clearAll() {
    for (final item in _items.values) {
      item._value = null;
      item._isLoaded = false;
      item._currentRetryCount = 0;
    }
    
    for (final timer in _preloadTimers.values) {
      timer.cancel();
    }
    _preloadTimers.clear();
    
    _logger.info('Cleared all lazy items');
  }

  /// 获取加载统计
  Map<String, dynamic> getStatistics() {
    int loadedCount = 0;
    int loadingCount = 0;
    int totalCount = _items.length;
    
    for (final item in _items.values) {
      if (item.isLoaded) {
        loadedCount++;
      } else if (item.isLoading) {
        loadingCount++;
      }
    }
    
    return {
      'total': totalCount,
      'loaded': loadedCount,
      'loading': loadingCount,
      'pending': totalCount - loadedCount - loadingCount,
      'loadedPercentage': totalCount > 0 ? (loadedCount / totalCount * 100).toStringAsFixed(1) : '0.0',
    };
  }

  void dispose() {
    clearAll();
    _items.clear();
  }
}
```

## 6. 性能监控 Widget

### 性能监控面板
```dart
// packages/core/core_performance/lib/src/performance_overlay.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'memory_monitor.dart';
import 'performance_monitor.dart';
import 'network_optimizer.dart';
import 'performance_analyzer.dart';

/// 性能监控覆盖层
class PerformanceOverlay extends StatefulWidget {
  const PerformanceOverlay({
    super.key,
    required this.child,
    this.enabled = true,
    this.position = PerformanceOverlayPosition.topRight,
  });

  final Widget child;
  final bool enabled;
  final PerformanceOverlayPosition position;

  @override
  State<PerformanceOverlay> createState() => _PerformanceOverlayState();
}

enum PerformanceOverlayPosition {
  topLeft,
  topRight,
  bottomLeft,
  bottomRight,
}

class _PerformanceOverlayState extends State<PerformanceOverlay> {
  bool _isExpanded = false;
  MemoryInfo? _memoryInfo;
  PerformanceMetrics? _performanceMetrics;
  CacheStatistics? _cacheStatistics;

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled) {
      return widget.child;
    }

    return Stack(
      children: [
        widget.child,
        Positioned(
          ...._getPosition(),
          child: _buildOverlay(),
        ),
      ],
    );
  }

  Map<String, double> _getPosition() {
    switch (widget.position) {
      case PerformanceOverlayPosition.topLeft:
        return {'top': 50.0, 'left': 16.0};
      case PerformanceOverlayPosition.topRight:
        return {'top': 50.0, 'right': 16.0};
      case PerformanceOverlayPosition.bottomLeft:
        return {'bottom': 50.0, 'left': 16.0};
      case PerformanceOverlayPosition.bottomRight:
        return {'bottom': 50.0, 'right': 16.0};
    }
  }

  Widget _buildOverlay() {
    return Material(
      color: Colors.black87,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: _isExpanded ? 300 : 120,
          maxHeight: _isExpanded ? 400 : 80,
        ),
        padding: const EdgeInsets.all(8),
        child: _isExpanded ? _buildExpandedView() : _buildCollapsedView(),
      ),
    );
  }

  Widget _buildCollapsedView() {
    return GestureDetector(
      onTap: () => setState(() => _isExpanded = true),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Performance',
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          if (_performanceMetrics != null)
            Text(
              'FPS: ${_performanceMetrics!.fps.toStringAsFixed(1)}',
              style: TextStyle(
                color: _getFpsColor(_performanceMetrics!.fps),
                fontSize: 10,
              ),
            ),
          if (_memoryInfo != null)
            Text(
              'Memory: ${_memoryInfo!.usagePercentage.toStringAsFixed(1)}%',
              style: TextStyle(
                color: _getMemoryColor(_memoryInfo!.usagePercentage),
                fontSize: 10,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildExpandedView() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Performance Monitor',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            GestureDetector(
              onTap: () => setState(() => _isExpanded = false),
              child: Icon(
                Icons.close,
                color: Colors.white,
                size: 16,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildPerformanceSection(),
                const SizedBox(height: 8),
                _buildMemorySection(),
                const SizedBox(height: 8),
                _buildCacheSection(),
                const SizedBox(height: 8),
                _buildActionsSection(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPerformanceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Performance',
          style: TextStyle(
            color: Colors.white70,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        if (_performanceMetrics != null) ..[
          _buildMetricRow(
            'FPS',
            _performanceMetrics!.fps.toStringAsFixed(1),
            _getFpsColor(_performanceMetrics!.fps),
          ),
          _buildMetricRow(
            'Frame Time',
            '${_performanceMetrics!.frameTime.inMilliseconds}ms',
            _getFrameTimeColor(_performanceMetrics!.frameTime.inMilliseconds),
          ),
          _buildMetricRow(
            'Jank Count',
            _performanceMetrics!.jankCount.toString(),
            _getJankColor(_performanceMetrics!.jankCount),
          ),
        ] else
          Text(
            'No data',
            style: TextStyle(color: Colors.white54, fontSize: 10),
          ),
      ],
    );
  }

  Widget _buildMemorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Memory',
          style: TextStyle(
            color: Colors.white70,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        if (_memoryInfo != null) ..[
          _buildMetricRow(
            'Usage',
            '${_memoryInfo!.usagePercentage.toStringAsFixed(1)}%',
            _getMemoryColor(_memoryInfo!.usagePercentage),
          ),
          _buildMetricRow(
            'Used',
            _memoryInfo!.formattedUsedMemory,
            Colors.white54,
          ),
          _buildMetricRow(
            'Total',
            _memoryInfo!.formattedTotalMemory,
            Colors.white54,
          ),
        ] else
          Text(
            'No data',
            style: TextStyle(color: Colors.white54, fontSize: 10),
          ),
      ],
    );
  }

  Widget _buildCacheSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Cache',
          style: TextStyle(
            color: Colors.white70,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        if (_cacheStatistics != null) ..[
          _buildMetricRow(
            'Size',
            _cacheStatistics!.formattedSize,
            Colors.white54,
          ),
          _buildMetricRow(
            'Count',
            _cacheStatistics!.totalCount.toString(),
            Colors.white54,
          ),
          _buildMetricRow(
            'Pending',
            _cacheStatistics!.pendingRequestCount.toString(),
            _getPendingColor(_cacheStatistics!.pendingRequestCount),
          ),
        ] else
          Text(
            'No data',
            style: TextStyle(color: Colors.white54, fontSize: 10),
          ),
      ],
    );
  }

  Widget _buildActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Actions',
          style: TextStyle(
            color: Colors.white70,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            _buildActionButton(
              'GC',
              () => _forceGarbageCollection(),
            ),
            const SizedBox(width: 8),
            _buildActionButton(
              'Clear Cache',
              () => _clearCache(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMetricRow(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(color: Colors.white54, fontSize: 10),
          ),
          Text(
            value,
            style: TextStyle(color: color, fontSize: 10),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(String label, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.blue.withOpacity(0.3),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.blue, width: 1),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: Colors.blue,
            fontSize: 10,
          ),
        ),
      ),
    );
  }

  Color _getFpsColor(double fps) {
    if (fps >= 55) return Colors.green;
    if (fps >= 30) return Colors.orange;
    return Colors.red;
  }

  Color _getFrameTimeColor(int frameTimeMs) {
    if (frameTimeMs <= 16) return Colors.green;
    if (frameTimeMs <= 32) return Colors.orange;
    return Colors.red;
  }

  Color _getJankColor(int jankCount) {
    if (jankCount <= 5) return Colors.green;
    if (jankCount <= 15) return Colors.orange;
    return Colors.red;
  }

  Color _getMemoryColor(double percentage) {
    if (percentage <= 60) return Colors.green;
    if (percentage <= 80) return Colors.orange;
    return Colors.red;
  }

  Color _getPendingColor(int pendingCount) {
    if (pendingCount <= 5) return Colors.green;
    if (pendingCount <= 15) return Colors.orange;
    return Colors.red;
  }

  void _forceGarbageCollection() {
    // 这里应该调用实际的内存监控服务
    HapticFeedback.lightImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Garbage collection triggered'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _clearCache() {
    // 这里应该调用实际的缓存清理服务
    HapticFeedback.lightImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Cache cleared'),
        duration: Duration(seconds: 1),
      ),
    );
  }
}
```

## 7. 使用示例

### 性能优化集成示例
```dart
// lib/main.dart
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:core_performance/core_performance.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化依赖注入
  await setupDependencies();
  
  // 获取启动优化器
  final startupOptimizer = GetIt.instance<StartupOptimizer>();
  
  // 注册启动任务
  _registerStartupTasks(startupOptimizer);
  
  // 执行启动优化
  final metrics = await startupOptimizer.optimize();
  print('Startup completed in ${metrics.totalTime.inMilliseconds}ms');
  
  // 启动性能监控
  final performanceAnalyzer = GetIt.instance<PerformanceAnalyzer>();
  performanceAnalyzer.startAnalysis();
  
  runApp(MyApp());
}

void _registerStartupTasks(StartupOptimizer optimizer) {
  // 预初始化阶段
  optimizer.registerTask(StartupTask(
    name: 'warmup_components',
    phase: StartupPhase.preInit,
    priority: 100,
    task: () => optimizer.warmupComponents(),
  ));
  
  // 初始化阶段
  optimizer.registerTask(StartupTask(
    name: 'init_database',
    phase: StartupPhase.init,
    priority: 90,
    task: () => _initDatabase(),
  ));
  
  optimizer.registerTask(StartupTask(
    name: 'init_network',
    phase: StartupPhase.init,
    priority: 80,
    task: () => _initNetwork(),
  ));
  
  // 后初始化阶段
  optimizer.registerTask(StartupTask(
    name: 'preload_data',
    phase: StartupPhase.postInit,
    priority: 70,
    dependencies: ['init_database', 'init_network'],
    task: () => _preloadData(),
  ));
  
  // 就绪阶段
  optimizer.registerTask(StartupTask(
    name: 'setup_analytics',
    phase: StartupPhase.ready,
    priority: 60,
    task: () => _setupAnalytics(),
  ));
}

Future<void> _initDatabase() async {
  // 数据库初始化逻辑
  await Future.delayed(Duration(milliseconds: 200));
}

Future<void> _initNetwork() async {
  // 网络初始化逻辑
  await Future.delayed(Duration(milliseconds: 150));
}

Future<void> _preloadData() async {
  // 数据预加载逻辑
  final lazyLoader = GetIt.instance<LazyLoader>();
  await lazyLoader.preload(['user_data', 'app_config']);
}

Future<void> _setupAnalytics() async {
  // 分析服务初始化
  await Future.delayed(Duration(milliseconds: 100));
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Performance Optimized App',
      home: PerformanceOverlay(
        enabled: true, // 在开发模式下启用
        child: HomePage(),
      ),
    );
  }
}

class HomePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Performance Demo'),
        actions: [
          IconButton(
            icon: Icon(Icons.analytics),
            onPressed: () => _showPerformanceReport(context),
          ),
        ],
      ),
      body: OptimizedListView<String>(
        items: List.generate(1000, (index) => 'Item $index'),
        itemBuilder: (context, item, index) {
          return ListTile(
            leading: OptimizedNetworkImage(
              imageUrl: 'https://picsum.photos/50/50?random=$index',
              width: 50,
              height: 50,
              cacheWidth: 50,
              cacheHeight: 50,
            ),
            title: Text(item),
            subtitle: Text('Subtitle for $item'),
          );
        },
        onLoadMore: () {
          // 加载更多数据
        },
        onRefresh: () async {
          // 刷新数据
          await Future.delayed(Duration(seconds: 1));
        },
      ),
    );
  }

  void _showPerformanceReport(BuildContext context) async {
    final analyzer = GetIt.instance<PerformanceAnalyzer>();
    final overview = await analyzer.getPerformanceOverview();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Performance Report'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Memory: ${overview['memory']['usage']}'),
              Text('FPS: ${overview['performance']['fps']}'),
              Text('Cache: ${overview['cache']['size']}'),
              if (overview['recommendations'].isNotEmpty) ..[
                SizedBox(height: 16),
                Text('Recommendations:', style: TextStyle(fontWeight: FontWeight.bold)),
                ...overview['recommendations'].map<Widget>((rec) => Text('• $rec')),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }
}
```

这个性能优化实现提供了完整的性能监控和优化解决方案，包括：

1. **内存管理**：实时监控、自动垃圾回收、内存警告
2. **图片优化**：智能缓存、预加载、尺寸优化
3. **渲染优化**：帧率监控、卡顿检测、优化组件
4. **网络优化**：请求去重、缓存、批量处理
5. **启动优化**：任务管理、依赖解析、组件预热
6. **懒加载**：按需加载、预加载、重试机制
7. **性能分析**：综合报告、建议生成、基准测试
8. **监控界面**：实时显示、交互操作、可视化指标

该框架遵循 Clean Architecture 原则，支持依赖注入，易于扩展和维护。