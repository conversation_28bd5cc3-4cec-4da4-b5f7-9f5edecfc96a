# Flutter 企业级应用 - 国际化和本地化实现

本文档展示了 Flutter 企业级应用的国际化和本地化实现示例，包括多语言支持、动态语言切换、本地化资源管理、日期时间格式化、数字格式化、RTL 支持等功能。

## 1. 国际化基础架构

### 应用本地化配置
```dart
// packages/core/core_localization/lib/src/app_localizations.dart
import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:intl/intl.dart';

/// 应用本地化类
class AppLocalizations {
  AppLocalizations(this.locale);

  final Locale locale;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = [
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
  ];

  static const List<Locale> supportedLocales = [
    Locale('en', 'US'), // English
    Locale('zh', 'CN'), // Chinese Simplified
    Locale('zh', 'TW'), // Chinese Traditional
    Locale('ja', 'JP'), // Japanese
    Locale('ko', 'KR'), // Korean
    Locale('es', 'ES'), // Spanish
    Locale('fr', 'FR'), // French
    Locale('de', 'DE'), // German
    Locale('ar', 'SA'), // Arabic
    Locale('ru', 'RU'), // Russian
  ];

  Map<String, String> _localizedStrings = {};

  Future<bool> load() async {
    try {
      // 加载本地化字符串
      final String jsonString = await rootBundle.loadString(
        'assets/l10n/${locale.languageCode}_${locale.countryCode}.json',
      );
      final Map<String, dynamic> jsonMap = json.decode(jsonString);

      _localizedStrings = jsonMap.map((key, value) {
        return MapEntry(key, value.toString());
      });

      return true;
    } catch (e) {
      // 如果加载失败，尝试加载默认语言
      if (locale.languageCode != 'en') {
        try {
          final String jsonString = await rootBundle.loadString(
            'assets/l10n/en_US.json',
          );
          final Map<String, dynamic> jsonMap = json.decode(jsonString);

          _localizedStrings = jsonMap.map((key, value) {
            return MapEntry(key, value.toString());
          });

          return true;
        } catch (e) {
          debugPrint('Failed to load default localization: $e');
        }
      }
      return false;
    }
  }

  String translate(String key, {Map<String, dynamic>? args}) {
    String translation = _localizedStrings[key] ?? key;

    // 处理参数替换
    if (args != null) {
      args.forEach((argKey, argValue) {
        translation = translation.replaceAll('{$argKey}', argValue.toString());
      });
    }

    return translation;
  }

  // 常用翻译方法
  String get appName => translate('app_name');
  String get welcome => translate('welcome');
  String get login => translate('login');
  String get logout => translate('logout');
  String get email => translate('email');
  String get password => translate('password');
  String get confirmPassword => translate('confirm_password');
  String get register => translate('register');
  String get forgotPassword => translate('forgot_password');
  String get resetPassword => translate('reset_password');
  String get save => translate('save');
  String get cancel => translate('cancel');
  String get delete => translate('delete');
  String get edit => translate('edit');
  String get add => translate('add');
  String get search => translate('search');
  String get filter => translate('filter');
  String get sort => translate('sort');
  String get refresh => translate('refresh');
  String get loading => translate('loading');
  String get error => translate('error');
  String get success => translate('success');
  String get warning => translate('warning');
  String get info => translate('info');
  String get yes => translate('yes');
  String get no => translate('no');
  String get ok => translate('ok');
  String get close => translate('close');
  String get back => translate('back');
  String get next => translate('next');
  String get previous => translate('previous');
  String get finish => translate('finish');
  String get settings => translate('settings');
  String get profile => translate('profile');
  String get about => translate('about');
  String get help => translate('help');
  String get contact => translate('contact');
  String get privacy => translate('privacy');
  String get terms => translate('terms');

  // 错误消息
  String get errorGeneral => translate('error_general');
  String get errorNetwork => translate('error_network');
  String get errorTimeout => translate('error_timeout');
  String get errorUnauthorized => translate('error_unauthorized');
  String get errorForbidden => translate('error_forbidden');
  String get errorNotFound => translate('error_not_found');
  String get errorServerError => translate('error_server_error');
  String get errorValidationFailed => translate('error_validation_failed');

  // 验证消息
  String get validationRequired => translate('validation_required');
  String get validationEmailInvalid => translate('validation_email_invalid');
  String get validationPasswordTooShort => translate('validation_password_too_short');
  String get validationPasswordMismatch => translate('validation_password_mismatch');
  String get validationPhoneInvalid => translate('validation_phone_invalid');

  // 格式化方法
  String formatDate(DateTime date) {
    return DateFormat.yMMMd(locale.toString()).format(date);
  }

  String formatTime(DateTime time) {
    return DateFormat.Hm(locale.toString()).format(time);
  }

  String formatDateTime(DateTime dateTime) {
    return DateFormat.yMMMd(locale.toString()).add_Hm().format(dateTime);
  }

  String formatCurrency(double amount, {String? currencyCode}) {
    final format = NumberFormat.currency(
      locale: locale.toString(),
      symbol: currencyCode ?? getCurrencySymbol(),
    );
    return format.format(amount);
  }

  String formatNumber(num number) {
    return NumberFormat('#,##0', locale.toString()).format(number);
  }

  String formatPercent(double value) {
    return NumberFormat.percentPattern(locale.toString()).format(value);
  }

  String getCurrencySymbol() {
    switch (locale.countryCode) {
      case 'US':
        return '\$';
      case 'CN':
      case 'TW':
        return '¥';
      case 'JP':
        return '¥';
      case 'KR':
        return '₩';
      case 'ES':
      case 'FR':
      case 'DE':
        return '€';
      case 'SA':
        return 'ر.س';
      case 'RU':
        return '₽';
      default:
        return '\$';
    }
  }

  // 复数形式处理
  String plural(String key, int count, {Map<String, dynamic>? args}) {
    String pluralKey;
    
    if (count == 0) {
      pluralKey = '${key}_zero';
    } else if (count == 1) {
      pluralKey = '${key}_one';
    } else {
      pluralKey = '${key}_other';
    }

    final translation = _localizedStrings[pluralKey] ?? _localizedStrings[key] ?? key;
    
    String result = translation.replaceAll('{count}', count.toString());
    
    if (args != null) {
      args.forEach((argKey, argValue) {
        result = result.replaceAll('{$argKey}', argValue.toString());
      });
    }

    return result;
  }

  // RTL 支持
  bool get isRTL {
    return ['ar', 'he', 'fa', 'ur'].contains(locale.languageCode);
  }

  TextDirection get textDirection {
    return isRTL ? TextDirection.rtl : TextDirection.ltr;
  }
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return AppLocalizations.supportedLocales
        .any((supportedLocale) => supportedLocale.languageCode == locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    final AppLocalizations localizations = AppLocalizations(locale);
    await localizations.load();
    return localizations;
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
```

### 本地化管理服务
```dart
// packages/core/core_localization/lib/src/localization_service.dart
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'app_localizations.dart';

/// 本地化管理服务
@singleton
class LocalizationService extends ChangeNotifier {
  static const String _localeKey = 'selected_locale';
  
  Locale _currentLocale = const Locale('en', 'US');
  late SharedPreferences _prefs;
  
  Locale get currentLocale => _currentLocale;
  
  /// 初始化服务
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadSavedLocale();
  }
  
  /// 加载保存的语言设置
  Future<void> _loadSavedLocale() async {
    final String? localeString = _prefs.getString(_localeKey);
    if (localeString != null) {
      final parts = localeString.split('_');
      if (parts.length == 2) {
        final locale = Locale(parts[0], parts[1]);
        if (_isLocaleSupported(locale)) {
          _currentLocale = locale;
          notifyListeners();
        }
      }
    } else {
      // 如果没有保存的语言设置，使用系统语言
      _setSystemLocale();
    }
  }
  
  /// 设置系统语言
  void _setSystemLocale() {
    final systemLocale = PlatformDispatcher.instance.locale;
    final supportedLocale = _findBestSupportedLocale(systemLocale);
    if (supportedLocale != null) {
      _currentLocale = supportedLocale;
      notifyListeners();
    }
  }
  
  /// 查找最佳支持的语言
  Locale? _findBestSupportedLocale(Locale locale) {
    // 精确匹配
    for (final supportedLocale in AppLocalizations.supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode &&
          supportedLocale.countryCode == locale.countryCode) {
        return supportedLocale;
      }
    }
    
    // 语言匹配
    for (final supportedLocale in AppLocalizations.supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return supportedLocale;
      }
    }
    
    return null;
  }
  
  /// 检查语言是否支持
  bool _isLocaleSupported(Locale locale) {
    return AppLocalizations.supportedLocales.any(
      (supportedLocale) =>
          supportedLocale.languageCode == locale.languageCode &&
          supportedLocale.countryCode == locale.countryCode,
    );
  }
  
  /// 更改语言
  Future<void> changeLocale(Locale locale) async {
    if (!_isLocaleSupported(locale)) {
      throw ArgumentError('Locale $locale is not supported');
    }
    
    _currentLocale = locale;
    await _prefs.setString(_localeKey, '${locale.languageCode}_${locale.countryCode}');
    notifyListeners();
  }
  
  /// 获取支持的语言列表
  List<LocaleInfo> getSupportedLocales() {
    return AppLocalizations.supportedLocales.map((locale) {
      return LocaleInfo(
        locale: locale,
        name: _getLocaleName(locale),
        nativeName: _getNativeLocaleName(locale),
        flag: _getLocaleFlag(locale),
      );
    }).toList();
  }
  
  /// 获取语言名称
  String _getLocaleName(Locale locale) {
    switch ('${locale.languageCode}_${locale.countryCode}') {
      case 'en_US':
        return 'English (US)';
      case 'zh_CN':
        return 'Chinese (Simplified)';
      case 'zh_TW':
        return 'Chinese (Traditional)';
      case 'ja_JP':
        return 'Japanese';
      case 'ko_KR':
        return 'Korean';
      case 'es_ES':
        return 'Spanish';
      case 'fr_FR':
        return 'French';
      case 'de_DE':
        return 'German';
      case 'ar_SA':
        return 'Arabic';
      case 'ru_RU':
        return 'Russian';
      default:
        return '${locale.languageCode}_${locale.countryCode}';
    }
  }
  
  /// 获取本地语言名称
  String _getNativeLocaleName(Locale locale) {
    switch ('${locale.languageCode}_${locale.countryCode}') {
      case 'en_US':
        return 'English';
      case 'zh_CN':
        return '简体中文';
      case 'zh_TW':
        return '繁體中文';
      case 'ja_JP':
        return '日本語';
      case 'ko_KR':
        return '한국어';
      case 'es_ES':
        return 'Español';
      case 'fr_FR':
        return 'Français';
      case 'de_DE':
        return 'Deutsch';
      case 'ar_SA':
        return 'العربية';
      case 'ru_RU':
        return 'Русский';
      default:
        return _getLocaleName(locale);
    }
  }
  
  /// 获取语言标志
  String _getLocaleFlag(Locale locale) {
    switch (locale.countryCode) {
      case 'US':
        return '🇺🇸';
      case 'CN':
        return '🇨🇳';
      case 'TW':
        return '🇹🇼';
      case 'JP':
        return '🇯🇵';
      case 'KR':
        return '🇰🇷';
      case 'ES':
        return '🇪🇸';
      case 'FR':
        return '🇫🇷';
      case 'DE':
        return '🇩🇪';
      case 'SA':
        return '🇸🇦';
      case 'RU':
        return '🇷🇺';
      default:
        return '🌐';
    }
  }
  
  /// 重置为系统语言
  Future<void> resetToSystemLocale() async {
    await _prefs.remove(_localeKey);
    _setSystemLocale();
  }
  
  /// 获取当前语言信息
  LocaleInfo getCurrentLocaleInfo() {
    return LocaleInfo(
      locale: _currentLocale,
      name: _getLocaleName(_currentLocale),
      nativeName: _getNativeLocaleName(_currentLocale),
      flag: _getLocaleFlag(_currentLocale),
    );
  }
}

/// 语言信息
class LocaleInfo {
  final Locale locale;
  final String name;
  final String nativeName;
  final String flag;
  
  const LocaleInfo({
    required this.locale,
    required this.name,
    required this.nativeName,
    required this.flag,
  });
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocaleInfo && other.locale == locale;
  }
  
  @override
  int get hashCode => locale.hashCode;
}
```

## 2. 动态本地化资源

### 远程本地化服务
```dart
// packages/core/core_localization/lib/src/remote_localization_service.dart
import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';

/// 远程本地化服务
@singleton
class RemoteLocalizationService {
  final Dio _dio;
  
  RemoteLocalizationService(this._dio);
  
  /// 下载本地化资源
  Future<LocalizationDownloadResult> downloadLocalizations({
    required String baseUrl,
    required List<String> locales,
    String? version,
    Map<String, String>? headers,
  }) async {
    try {
      final results = <String, LocalizationFileResult>{};
      
      for (final locale in locales) {
        final result = await _downloadLocalizationFile(
          baseUrl: baseUrl,
          locale: locale,
          version: version,
          headers: headers,
        );
        results[locale] = result;
      }
      
      return LocalizationDownloadResult(
        success: true,
        results: results,
      );
    } catch (e) {
      return LocalizationDownloadResult(
        success: false,
        error: e.toString(),
        results: {},
      );
    }
  }
  
  /// 下载单个本地化文件
  Future<LocalizationFileResult> _downloadLocalizationFile({
    required String baseUrl,
    required String locale,
    String? version,
    Map<String, String>? headers,
  }) async {
    try {
      final url = _buildLocalizationUrl(baseUrl, locale, version);
      
      final response = await _dio.get(
        url,
        options: Options(headers: headers),
      );
      
      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        final content = json.encode(data);
        
        // 保存到本地
        final file = await _saveLocalizationFile(locale, content);
        
        return LocalizationFileResult(
          success: true,
          locale: locale,
          filePath: file.path,
          content: data,
          hash: _generateHash(content),
          size: content.length,
        );
      } else {
        return LocalizationFileResult(
          success: false,
          locale: locale,
          error: 'HTTP ${response.statusCode}',
        );
      }
    } catch (e) {
      return LocalizationFileResult(
        success: false,
        locale: locale,
        error: e.toString(),
      );
    }
  }
  
  /// 构建本地化 URL
  String _buildLocalizationUrl(String baseUrl, String locale, String? version) {
    final buffer = StringBuffer(baseUrl);
    if (!baseUrl.endsWith('/')) {
      buffer.write('/');
    }
    buffer.write('$locale.json');
    
    if (version != null) {
      buffer.write('?v=$version');
    }
    
    return buffer.toString();
  }
  
  /// 保存本地化文件
  Future<File> _saveLocalizationFile(String locale, String content) async {
    final directory = await _getLocalizationDirectory();
    final file = File('${directory.path}/$locale.json');
    
    await file.writeAsString(content);
    return file;
  }
  
  /// 获取本地化目录
  Future<Directory> _getLocalizationDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final localizationDir = Directory('${appDir.path}/localizations');
    
    if (!await localizationDir.exists()) {
      await localizationDir.create(recursive: true);
    }
    
    return localizationDir;
  }
  
  /// 生成内容哈希
  String _generateHash(String content) {
    final bytes = utf8.encode(content);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
  
  /// 检查本地化更新
  Future<LocalizationUpdateResult> checkForUpdates({
    required String baseUrl,
    required List<String> locales,
    Map<String, String>? headers,
  }) async {
    try {
      final url = '$baseUrl/version.json';
      
      final response = await _dio.get(
        url,
        options: Options(headers: headers),
      );
      
      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        final serverVersion = data['version'] as String?;
        final localeVersions = data['locales'] as Map<String, dynamic>? ?? {};
        
        final updates = <String>[];
        
        for (final locale in locales) {
          final localVersion = await _getLocalVersion(locale);
          final serverLocaleVersion = localeVersions[locale] as String?;
          
          if (localVersion == null || 
              (serverLocaleVersion != null && serverLocaleVersion != localVersion)) {
            updates.add(locale);
          }
        }
        
        return LocalizationUpdateResult(
          success: true,
          hasUpdates: updates.isNotEmpty,
          serverVersion: serverVersion,
          updatesAvailable: updates,
        );
      } else {
        return LocalizationUpdateResult(
          success: false,
          error: 'HTTP ${response.statusCode}',
        );
      }
    } catch (e) {
      return LocalizationUpdateResult(
        success: false,
        error: e.toString(),
      );
    }
  }
  
  /// 获取本地版本
  Future<String?> _getLocalVersion(String locale) async {
    try {
      final directory = await _getLocalizationDirectory();
      final versionFile = File('${directory.path}/${locale}_version.txt');
      
      if (await versionFile.exists()) {
        return await versionFile.readAsString();
      }
    } catch (e) {
      // 忽略错误
    }
    return null;
  }
  
  /// 保存版本信息
  Future<void> saveVersion(String locale, String version) async {
    try {
      final directory = await _getLocalizationDirectory();
      final versionFile = File('${directory.path}/${locale}_version.txt');
      await versionFile.writeAsString(version);
    } catch (e) {
      // 忽略错误
    }
  }
  
  /// 清理本地化缓存
  Future<void> clearCache() async {
    try {
      final directory = await _getLocalizationDirectory();
      if (await directory.exists()) {
        await directory.delete(recursive: true);
      }
    } catch (e) {
      // 忽略错误
    }
  }
  
  /// 获取缓存大小
  Future<int> getCacheSize() async {
    try {
      final directory = await _getLocalizationDirectory();
      if (!await directory.exists()) {
        return 0;
      }
      
      int totalSize = 0;
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          final stat = await entity.stat();
          totalSize += stat.size;
        }
      }
      
      return totalSize;
    } catch (e) {
      return 0;
    }
  }
}

/// 本地化下载结果
class LocalizationDownloadResult {
  final bool success;
  final String? error;
  final Map<String, LocalizationFileResult> results;
  
  const LocalizationDownloadResult({
    required this.success,
    this.error,
    required this.results,
  });
}

/// 本地化文件结果
class LocalizationFileResult {
  final bool success;
  final String locale;
  final String? error;
  final String? filePath;
  final Map<String, dynamic>? content;
  final String? hash;
  final int? size;
  
  const LocalizationFileResult({
    required this.success,
    required this.locale,
    this.error,
    this.filePath,
    this.content,
    this.hash,
    this.size,
  });
}

/// 本地化更新结果
class LocalizationUpdateResult {
  final bool success;
  final String? error;
  final bool hasUpdates;
  final String? serverVersion;
  final List<String> updatesAvailable;
  
  const LocalizationUpdateResult({
    required this.success,
    this.error,
    this.hasUpdates = false,
    this.serverVersion,
    this.updatesAvailable = const [],
  });
}
```

## 3. 本地化工具和扩展

### 本地化扩展方法
```dart
// packages/core/core_localization/lib/src/localization_extensions.dart
import 'package:flutter/widgets.dart';
import 'package:intl/intl.dart';
import 'app_localizations.dart';

/// BuildContext 扩展
extension LocalizationExtension on BuildContext {
  AppLocalizations get l10n {
    final localizations = AppLocalizations.of(this);
    if (localizations == null) {
      throw FlutterError(
        'AppLocalizations not found. '
        'Make sure to add AppLocalizations.delegate to your app.',
      );
    }
    return localizations;
  }
  
  /// 翻译文本
  String tr(String key, {Map<String, dynamic>? args}) {
    return l10n.translate(key, args: args);
  }
  
  /// 复数形式
  String plural(String key, int count, {Map<String, dynamic>? args}) {
    return l10n.plural(key, count, args: args);
  }
  
  /// 格式化日期
  String formatDate(DateTime date) {
    return l10n.formatDate(date);
  }
  
  /// 格式化时间
  String formatTime(DateTime time) {
    return l10n.formatTime(time);
  }
  
  /// 格式化日期时间
  String formatDateTime(DateTime dateTime) {
    return l10n.formatDateTime(dateTime);
  }
  
  /// 格式化货币
  String formatCurrency(double amount, {String? currencyCode}) {
    return l10n.formatCurrency(amount, currencyCode: currencyCode);
  }
  
  /// 格式化数字
  String formatNumber(num number) {
    return l10n.formatNumber(number);
  }
  
  /// 格式化百分比
  String formatPercent(double value) {
    return l10n.formatPercent(value);
  }
  
  /// 获取文本方向
  TextDirection get textDirection => l10n.textDirection;
  
  /// 是否为 RTL
  bool get isRTL => l10n.isRTL;
}

/// String 扩展
extension StringLocalizationExtension on String {
  /// 翻译字符串
  String tr(BuildContext context, {Map<String, dynamic>? args}) {
    return context.tr(this, args: args);
  }
  
  /// 复数形式
  String plural(BuildContext context, int count, {Map<String, dynamic>? args}) {
    return context.plural(this, count, args: args);
  }
}

/// DateTime 扩展
extension DateTimeLocalizationExtension on DateTime {
  /// 格式化为本地化日期
  String toLocalizedDate(BuildContext context) {
    return context.formatDate(this);
  }
  
  /// 格式化为本地化时间
  String toLocalizedTime(BuildContext context) {
    return context.formatTime(this);
  }
  
  /// 格式化为本地化日期时间
  String toLocalizedDateTime(BuildContext context) {
    return context.formatDateTime(this);
  }
  
  /// 相对时间格式化
  String toRelativeTime(BuildContext context) {
    final now = DateTime.now();
    final difference = now.difference(this);
    
    if (difference.inDays > 0) {
      return context.plural('days_ago', difference.inDays, 
          args: {'count': difference.inDays});
    } else if (difference.inHours > 0) {
      return context.plural('hours_ago', difference.inHours,
          args: {'count': difference.inHours});
    } else if (difference.inMinutes > 0) {
      return context.plural('minutes_ago', difference.inMinutes,
          args: {'count': difference.inMinutes});
    } else {
      return context.tr('just_now');
    }
  }
}

/// num 扩展
extension NumLocalizationExtension on num {
  /// 格式化为本地化数字
  String toLocalizedNumber(BuildContext context) {
    return context.formatNumber(this);
  }
  
  /// 格式化为本地化货币
  String toLocalizedCurrency(BuildContext context, {String? currencyCode}) {
    return context.formatCurrency(toDouble(), currencyCode: currencyCode);
  }
}

/// double 扩展
extension DoubleLocalizationExtension on double {
  /// 格式化为本地化百分比
  String toLocalizedPercent(BuildContext context) {
    return context.formatPercent(this);
  }
}
```

### 本地化验证器
```dart
// packages/core/core_localization/lib/src/localization_validator.dart
import 'dart:convert';
import 'dart:io';

/// 本地化验证器
class LocalizationValidator {
  /// 验证本地化文件
  static Future<ValidationResult> validateLocalizationFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        return ValidationResult(
          isValid: false,
          errors: ['File does not exist: $filePath'],
        );
      }
      
      final content = await file.readAsString();
      final Map<String, dynamic> data;
      
      try {
        data = json.decode(content);
      } catch (e) {
        return ValidationResult(
          isValid: false,
          errors: ['Invalid JSON format: $e'],
        );
      }
      
      final errors = <String>[];
      final warnings = <String>[];
      
      // 检查必需的键
      final requiredKeys = [
        'app_name',
        'welcome',
        'login',
        'logout',
        'error_general',
        'loading',
      ];
      
      for (final key in requiredKeys) {
        if (!data.containsKey(key)) {
          errors.add('Missing required key: $key');
        }
      }
      
      // 检查空值
      for (final entry in data.entries) {
        if (entry.value == null || entry.value.toString().trim().isEmpty) {
          warnings.add('Empty value for key: ${entry.key}');
        }
      }
      
      // 检查参数占位符
      for (final entry in data.entries) {
        final value = entry.value.toString();
        final placeholders = _extractPlaceholders(value);
        
        for (final placeholder in placeholders) {
          if (!_isValidPlaceholder(placeholder)) {
            warnings.add('Invalid placeholder "$placeholder" in key: ${entry.key}');
          }
        }
      }
      
      return ValidationResult(
        isValid: errors.isEmpty,
        errors: errors,
        warnings: warnings,
        keyCount: data.length,
      );
    } catch (e) {
      return ValidationResult(
        isValid: false,
        errors: ['Validation failed: $e'],
      );
    }
  }
  
  /// 比较本地化文件
  static Future<ComparisonResult> compareLocalizationFiles(
    String baseFilePath,
    String targetFilePath,
  ) async {
    try {
      final baseFile = File(baseFilePath);
      final targetFile = File(targetFilePath);
      
      if (!await baseFile.exists()) {
        return ComparisonResult(
          success: false,
          error: 'Base file does not exist: $baseFilePath',
        );
      }
      
      if (!await targetFile.exists()) {
        return ComparisonResult(
          success: false,
          error: 'Target file does not exist: $targetFilePath',
        );
      }
      
      final baseContent = await baseFile.readAsString();
      final targetContent = await targetFile.readAsString();
      
      final baseData = json.decode(baseContent) as Map<String, dynamic>;
      final targetData = json.decode(targetContent) as Map<String, dynamic>;
      
      final missingKeys = <String>[];
      final extraKeys = <String>[];
      final differentValues = <String>[];
      
      // 检查缺失的键
      for (final key in baseData.keys) {
        if (!targetData.containsKey(key)) {
          missingKeys.add(key);
        } else if (baseData[key] != targetData[key]) {
          differentValues.add(key);
        }
      }
      
      // 检查额外的键
      for (final key in targetData.keys) {
        if (!baseData.containsKey(key)) {
          extraKeys.add(key);
        }
      }
      
      return ComparisonResult(
        success: true,
        missingKeys: missingKeys,
        extraKeys: extraKeys,
        differentValues: differentValues,
        baseKeyCount: baseData.length,
        targetKeyCount: targetData.length,
      );
    } catch (e) {
      return ComparisonResult(
        success: false,
        error: 'Comparison failed: $e',
      );
    }
  }
  
  /// 提取占位符
  static List<String> _extractPlaceholders(String text) {
    final regex = RegExp(r'\{([^}]+)\}');
    final matches = regex.allMatches(text);
    return matches.map((match) => match.group(1)!).toList();
  }
  
  /// 验证占位符
  static bool _isValidPlaceholder(String placeholder) {
    // 检查占位符是否为有效的变量名
    final regex = RegExp(r'^[a-zA-Z_][a-zA-Z0-9_]*$');
    return regex.hasMatch(placeholder);
  }
  
  /// 生成本地化报告
  static Future<LocalizationReport> generateReport(List<String> filePaths) async {
    final fileReports = <FileReport>[];
    final allKeys = <String>{};
    
    for (final filePath in filePaths) {
      final validation = await validateLocalizationFile(filePath);
      final file = File(filePath);
      final fileName = file.uri.pathSegments.last;
      
      if (validation.isValid) {
        final content = await file.readAsString();
        final data = json.decode(content) as Map<String, dynamic>;
        allKeys.addAll(data.keys);
        
        fileReports.add(FileReport(
          fileName: fileName,
          filePath: filePath,
          isValid: true,
          keyCount: data.length,
          errors: validation.errors,
          warnings: validation.warnings,
        ));
      } else {
        fileReports.add(FileReport(
          fileName: fileName,
          filePath: filePath,
          isValid: false,
          keyCount: 0,
          errors: validation.errors,
          warnings: validation.warnings,
        ));
      }
    }
    
    return LocalizationReport(
      fileReports: fileReports,
      totalKeys: allKeys.length,
      totalFiles: filePaths.length,
      validFiles: fileReports.where((r) => r.isValid).length,
    );
  }
}

/// 验证结果
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;
  final int keyCount;
  
  const ValidationResult({
    required this.isValid,
    this.errors = const [],
    this.warnings = const [],
    this.keyCount = 0,
  });
}

/// 比较结果
class ComparisonResult {
  final bool success;
  final String? error;
  final List<String> missingKeys;
  final List<String> extraKeys;
  final List<String> differentValues;
  final int baseKeyCount;
  final int targetKeyCount;
  
  const ComparisonResult({
    required this.success,
    this.error,
    this.missingKeys = const [],
    this.extraKeys = const [],
    this.differentValues = const [],
    this.baseKeyCount = 0,
    this.targetKeyCount = 0,
  });
}

/// 文件报告
class FileReport {
  final String fileName;
  final String filePath;
  final bool isValid;
  final int keyCount;
  final List<String> errors;
  final List<String> warnings;
  
  const FileReport({
    required this.fileName,
    required this.filePath,
    required this.isValid,
    required this.keyCount,
    this.errors = const [],
    this.warnings = const [],
  });
}

/// 本地化报告
class LocalizationReport {
  final List<FileReport> fileReports;
  final int totalKeys;
  final int totalFiles;
  final int validFiles;
  
  const LocalizationReport({
    required this.fileReports,
    required this.totalKeys,
    required this.totalFiles,
    required this.validFiles,
  });
}
```

## 4. UI 组件

### 语言选择器
```dart
// packages/core/core_localization/lib/src/widgets/language_selector.dart
import 'package:flutter/material.dart';
import '../localization_service.dart';
import '../app_localizations.dart';

/// 语言选择器
class LanguageSelector extends StatelessWidget {
  final LocalizationService localizationService;
  final ValueChanged<Locale>? onLocaleChanged;
  final bool showFlag;
  final bool showNativeName;
  final EdgeInsetsGeometry? padding;
  final TextStyle? textStyle;
  
  const LanguageSelector({
    Key? key,
    required this.localizationService,
    this.onLocaleChanged,
    this.showFlag = true,
    this.showNativeName = true,
    this.padding,
    this.textStyle,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: localizationService,
      builder: (context, child) {
        final currentLocale = localizationService.currentLocale;
        final supportedLocales = localizationService.getSupportedLocales();
        
        return PopupMenuButton<Locale>(
          initialValue: currentLocale,
          onSelected: (locale) {
            localizationService.changeLocale(locale);
            onLocaleChanged?.call(locale);
          },
          itemBuilder: (context) {
            return supportedLocales.map((localeInfo) {
              return PopupMenuItem<Locale>(
                value: localeInfo.locale,
                child: Padding(
                  padding: padding ?? EdgeInsets.symmetric(vertical: 8),
                  child: Row(
                    children: [
                      if (showFlag) ..[
                        Text(
                          localeInfo.flag,
                          style: TextStyle(fontSize: 20),
                        ),
                        SizedBox(width: 12),
                      ],
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              localeInfo.name,
                              style: textStyle ?? Theme.of(context).textTheme.bodyMedium,
                            ),
                            if (showNativeName && localeInfo.nativeName != localeInfo.name)
                              Text(
                                localeInfo.nativeName,
                                style: (textStyle ?? Theme.of(context).textTheme.bodySmall)
                                    ?.copyWith(
                                  color: Theme.of(context).textTheme.bodySmall?.color,
                                ),
                              ),
                          ],
                        ),
                      ),
                      if (localeInfo.locale == currentLocale)
                        Icon(
                          Icons.check,
                          color: Theme.of(context).primaryColor,
                          size: 20,
                        ),
                    ],
                  ),
                ),
              );
            }).toList();
          },
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (showFlag) ..[
                  Text(
                    localizationService.getCurrentLocaleInfo().flag,
                    style: TextStyle(fontSize: 20),
                  ),
                  SizedBox(width: 8),
                ],
                Text(
                  showNativeName
                      ? localizationService.getCurrentLocaleInfo().nativeName
                      : localizationService.getCurrentLocaleInfo().name,
                  style: textStyle ?? Theme.of(context).textTheme.bodyMedium,
                ),
                SizedBox(width: 4),
                Icon(
                  Icons.arrow_drop_down,
                  size: 20,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

/// 语言选择对话框
class LanguageSelectionDialog extends StatelessWidget {
  final LocalizationService localizationService;
  final ValueChanged<Locale>? onLocaleChanged;
  final bool showFlag;
  final bool showNativeName;
  
  const LanguageSelectionDialog({
    Key? key,
    required this.localizationService,
    this.onLocaleChanged,
    this.showFlag = true,
    this.showNativeName = true,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final supportedLocales = localizationService.getSupportedLocales();
    
    return AlertDialog(
      title: Text(l10n.translate('select_language')),
      content: SizedBox(
        width: double.maxFinite,
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: supportedLocales.length,
          itemBuilder: (context, index) {
            final localeInfo = supportedLocales[index];
            final isSelected = localeInfo.locale == localizationService.currentLocale;
            
            return ListTile(
              leading: showFlag
                  ? Text(
                      localeInfo.flag,
                      style: TextStyle(fontSize: 24),
                    )
                  : null,
              title: Text(localeInfo.name),
              subtitle: showNativeName && localeInfo.nativeName != localeInfo.name
                  ? Text(localeInfo.nativeName)
                  : null,
              trailing: isSelected
                  ? Icon(
                      Icons.check,
                      color: Theme.of(context).primaryColor,
                    )
                  : null,
              selected: isSelected,
              onTap: () {
                localizationService.changeLocale(localeInfo.locale);
                onLocaleChanged?.call(localeInfo.locale);
                Navigator.of(context).pop();
              },
            );
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(l10n.cancel),
        ),
      ],
    );
  }
  
  /// 显示语言选择对话框
  static Future<void> show(
    BuildContext context,
    LocalizationService localizationService, {
    ValueChanged<Locale>? onLocaleChanged,
    bool showFlag = true,
    bool showNativeName = true,
  }) {
    return showDialog<void>(
      context: context,
      builder: (context) => LanguageSelectionDialog(
        localizationService: localizationService,
        onLocaleChanged: onLocaleChanged,
        showFlag: showFlag,
        showNativeName: showNativeName,
      ),
    );
  }
}

/// 语言切换按钮
class LanguageSwitchButton extends StatelessWidget {
  final LocalizationService localizationService;
  final ValueChanged<Locale>? onLocaleChanged;
  final bool showFlag;
  final bool showText;
  final IconData? icon;
  final String? tooltip;
  
  const LanguageSwitchButton({
    Key? key,
    required this.localizationService,
    this.onLocaleChanged,
    this.showFlag = true,
    this.showText = false,
    this.icon,
    this.tooltip,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: localizationService,
      builder: (context, child) {
        final currentLocaleInfo = localizationService.getCurrentLocaleInfo();
        
        if (showText) {
          return TextButton.icon(
            onPressed: () => _showLanguageDialog(context),
            icon: showFlag
                ? Text(
                    currentLocaleInfo.flag,
                    style: TextStyle(fontSize: 16),
                  )
                : Icon(icon ?? Icons.language),
            label: Text(currentLocaleInfo.nativeName),
          );
        } else {
          return IconButton(
            onPressed: () => _showLanguageDialog(context),
            icon: showFlag
                ? Text(
                    currentLocaleInfo.flag,
                    style: TextStyle(fontSize: 20),
                  )
                : Icon(icon ?? Icons.language),
            tooltip: tooltip ?? AppLocalizations.of(context)?.translate('select_language'),
          );
        }
      },
    );
  }
  
  void _showLanguageDialog(BuildContext context) {
    LanguageSelectionDialog.show(
      context,
      localizationService,
      onLocaleChanged: onLocaleChanged,
    );
  }
}
```

## 5. 使用示例

### 主应用集成
```dart
// lib/main.dart
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get_it/get_it.dart';
import 'package:core_localization/core_localization.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化本地化服务
  await setupLocalizationServices();
  
  runApp(MyApp());
}

Future<void> setupLocalizationServices() async {
  final getIt = GetIt.instance;
  
  // 注册本地化服务
  final localizationService = LocalizationService();
  await localizationService.initialize();
  getIt.registerSingleton<LocalizationService>(localizationService);
  
  // 注册远程本地化服务
  final dio = Dio();
  getIt.registerSingleton<RemoteLocalizationService>(
    RemoteLocalizationService(dio),
  );
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final localizationService = GetIt.instance<LocalizationService>();
    
    return AnimatedBuilder(
      animation: localizationService,
      builder: (context, child) {
        return MaterialApp(
          title: 'Localized Flutter App',
          locale: localizationService.currentLocale,
          localizationsDelegates: AppLocalizations.localizationsDelegates,
          supportedLocales: AppLocalizations.supportedLocales,
          home: LocalizationDemoPage(),
          builder: (context, child) {
            // 设置全局文本方向
            final textDirection = AppLocalizations.of(context)?.textDirection ?? TextDirection.ltr;
            return Directionality(
              textDirection: textDirection,
              child: child!,
            );
          },
        );
      },
    );
  }
}

class LocalizationDemoPage extends StatefulWidget {
  @override
  _LocalizationDemoPageState createState() => _LocalizationDemoPageState();
}

class _LocalizationDemoPageState extends State<LocalizationDemoPage> {
  final _localizationService = GetIt.instance<LocalizationService>();
  final _remoteLocalizationService = GetIt.instance<RemoteLocalizationService>();
  
  int _counter = 0;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.l10n.appName),
        actions: [
          LanguageSwitchButton(
            localizationService: _localizationService,
            onLocaleChanged: (locale) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Language changed to ${_localizationService.getCurrentLocaleInfo().name}',
                  ),
                ),
              );
            },
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'check_updates',
                child: Text(context.tr('check_updates')),
              ),
              PopupMenuItem(
                value: 'download_translations',
                child: Text(context.tr('download_translations')),
              ),
              PopupMenuItem(
                value: 'clear_cache',
                child: Text(context.tr('clear_cache')),
              ),
            ],
          ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildWelcomeSection(),
            SizedBox(height: 20),
            _buildCounterSection(),
            SizedBox(height: 20),
            _buildDateTimeSection(),
            SizedBox(height: 20),
            _buildNumberSection(),
            SizedBox(height: 20),
            _buildLanguageSection(),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _incrementCounter,
        tooltip: context.tr('increment'),
        child: Icon(Icons.add),
      ),
    );
  }
  
  Widget _buildWelcomeSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.l10n.welcome,
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            SizedBox(height: 8),
            Text(
              context.tr('welcome_message', args: {
                'app_name': context.l10n.appName,
              }),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildCounterSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('counter_section'),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 8),
            Text(
              context.plural('button_pressed', _counter, args: {'count': _counter}),
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildDateTimeSection() {
    final now = DateTime.now();
    
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('datetime_section'),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 8),
            Text('${context.tr('current_date')}: ${now.toLocalizedDate(context)}'),
            Text('${context.tr('current_time')}: ${now.toLocalizedTime(context)}'),
            Text('${context.tr('current_datetime')}: ${now.toLocalizedDateTime(context)}'),
            Text('${context.tr('relative_time')}: ${now.toRelativeTime(context)}'),
          ],
        ),
      ),
    );
  }
  
  Widget _buildNumberSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('number_section'),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 8),
            Text('${context.tr('number')}: ${1234567.toLocalizedNumber(context)}'),
            Text('${context.tr('currency')}: ${1234.56.toLocalizedCurrency(context)}'),
            Text('${context.tr('percent')}: ${0.1234.toLocalizedPercent(context)}'),
          ],
        ),
      ),
    );
  }
  
  Widget _buildLanguageSection() {
    final currentLocaleInfo = _localizationService.getCurrentLocaleInfo();
    
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('language_section'),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 8),
            Text('${context.tr('current_language')}: ${currentLocaleInfo.name}'),
            Text('${context.tr('native_name')}: ${currentLocaleInfo.nativeName}'),
            Text('${context.tr('text_direction')}: ${context.isRTL ? "RTL" : "LTR"}'),
            SizedBox(height: 12),
            LanguageSelector(
              localizationService: _localizationService,
              onLocaleChanged: (locale) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      context.tr('language_changed', args: {
                        'language': _localizationService.getCurrentLocaleInfo().name,
                      }),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
  
  void _incrementCounter() {
    setState(() {
      _counter++;
    });
  }
  
  void _handleMenuAction(String action) async {
    switch (action) {
      case 'check_updates':
        await _checkForUpdates();
        break;
      case 'download_translations':
        await _downloadTranslations();
        break;
      case 'clear_cache':
        await _clearCache();
        break;
    }
  }
  
  Future<void> _checkForUpdates() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.tr('checking_updates'))),
      );
      
      final result = await _remoteLocalizationService.checkForUpdates(
        baseUrl: 'https://api.example.com/localizations',
        locales: ['en_US', 'zh_CN', 'ja_JP'],
      );
      
      if (result.success && result.hasUpdates) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              context.plural('updates_available', result.updatesAvailable.length,
                  args: {'count': result.updatesAvailable.length}),
            ),
            action: SnackBarAction(
              label: context.tr('download'),
              onPressed: _downloadTranslations,
            ),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.tr('no_updates_available'))),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.tr('check_updates_failed'))),
      );
    }
  }
  
  Future<void> _downloadTranslations() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.tr('downloading_translations'))),
      );
      
      final result = await _remoteLocalizationService.downloadLocalizations(
        baseUrl: 'https://api.example.com/localizations',
        locales: ['en_US', 'zh_CN', 'ja_JP'],
      );
      
      if (result.success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.tr('download_completed'))),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.tr('download_failed'))),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.tr('download_error'))),
      );
    }
  }
  
  Future<void> _clearCache() async {
    try {
      await _remoteLocalizationService.clearCache();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.tr('cache_cleared'))),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.tr('clear_cache_failed'))),
      );
    }
  }
}
```

### 本地化资源文件示例

#### 英文资源文件
```json
// assets/l10n/en_US.json
{
  "app_name": "Flutter Enterprise App",
  "welcome": "Welcome",
  "welcome_message": "Welcome to {app_name}! This app demonstrates enterprise-level internationalization.",
  "login": "Login",
  "logout": "Logout",
  "email": "Email",
  "password": "Password",
  "confirm_password": "Confirm Password",
  "register": "Register",
  "forgot_password": "Forgot Password",
  "reset_password": "Reset Password",
  "save": "Save",
  "cancel": "Cancel",
  "delete": "Delete",
  "edit": "Edit",
  "add": "Add",
  "search": "Search",
  "filter": "Filter",
  "sort": "Sort",
  "refresh": "Refresh",
  "loading": "Loading...",
  "error": "Error",
  "success": "Success",
  "warning": "Warning",
  "info": "Information",
  "yes": "Yes",
  "no": "No",
  "ok": "OK",
  "close": "Close",
  "back": "Back",
  "next": "Next",
  "previous": "Previous",
  "finish": "Finish",
  "settings": "Settings",
  "profile": "Profile",
  "about": "About",
  "help": "Help",
  "contact": "Contact",
  "privacy": "Privacy",
  "terms": "Terms",
  
  "error_general": "An error occurred. Please try again.",
  "error_network": "Network error. Please check your connection.",
  "error_timeout": "Request timeout. Please try again.",
  "error_unauthorized": "Unauthorized access.",
  "error_forbidden": "Access forbidden.",
  "error_not_found": "Resource not found.",
  "error_server_error": "Server error. Please try again later.",
  "error_validation_failed": "Validation failed.",
  
  "validation_required": "This field is required.",
  "validation_email_invalid": "Please enter a valid email address.",
  "validation_password_too_short": "Password must be at least 8 characters.",
  "validation_password_mismatch": "Passwords do not match.",
  "validation_phone_invalid": "Please enter a valid phone number.",
  
  "counter_section": "Counter",
  "button_pressed_zero": "You haven't pressed the button yet.",
  "button_pressed_one": "You have pressed the button {count} time.",
  "button_pressed_other": "You have pressed the button {count} times.",
  
  "datetime_section": "Date & Time",
  "current_date": "Current Date",
  "current_time": "Current Time",
  "current_datetime": "Current Date & Time",
  "relative_time": "Relative Time",
  
  "number_section": "Numbers",
  "number": "Number",
  "currency": "Currency",
  "percent": "Percentage",
  
  "language_section": "Language",
  "current_language": "Current Language",
  "native_name": "Native Name",
  "text_direction": "Text Direction",
  "select_language": "Select Language",
  "language_changed": "Language changed to {language}",
  
  "check_updates": "Check Updates",
  "download_translations": "Download Translations",
  "clear_cache": "Clear Cache",
  "checking_updates": "Checking for updates...",
  "updates_available_zero": "No updates available.",
  "updates_available_one": "{count} update available.",
  "updates_available_other": "{count} updates available.",
  "no_updates_available": "No updates available.",
  "check_updates_failed": "Failed to check for updates.",
  "downloading_translations": "Downloading translations...",
  "download_completed": "Download completed.",
  "download_failed": "Download failed.",
  "download_error": "Download error.",
  "cache_cleared": "Cache cleared.",
  "clear_cache_failed": "Failed to clear cache.",
  "download": "Download",
  "increment": "Increment",
  
  "days_ago_zero": "Today",
  "days_ago_one": "{count} day ago",
  "days_ago_other": "{count} days ago",
  "hours_ago_zero": "Now",
  "hours_ago_one": "{count} hour ago",
  "hours_ago_other": "{count} hours ago",
  "minutes_ago_zero": "Now",
  "minutes_ago_one": "{count} minute ago",
  "minutes_ago_other": "{count} minutes ago",
  "just_now": "Just now"
 }
 ```

#### 中文简体资源文件
```json
// assets/l10n/zh_CN.json
{
  "app_name": "Flutter 企业级应用",
  "welcome": "欢迎",
  "welcome_message": "欢迎使用 {app_name}！此应用演示了企业级国际化功能。",
  "login": "登录",
  "logout": "退出登录",
  "email": "邮箱",
  "password": "密码",
  "confirm_password": "确认密码",
  "register": "注册",
  "forgot_password": "忘记密码",
  "reset_password": "重置密码",
  "save": "保存",
  "cancel": "取消",
  "delete": "删除",
  "edit": "编辑",
  "add": "添加",
  "search": "搜索",
  "filter": "筛选",
  "sort": "排序",
  "refresh": "刷新",
  "loading": "加载中...",
  "error": "错误",
  "success": "成功",
  "warning": "警告",
  "info": "信息",
  "yes": "是",
  "no": "否",
  "ok": "确定",
  "close": "关闭",
  "back": "返回",
  "next": "下一步",
  "previous": "上一步",
  "finish": "完成",
  "settings": "设置",
  "profile": "个人资料",
  "about": "关于",
  "help": "帮助",
  "contact": "联系我们",
  "privacy": "隐私政策",
  "terms": "服务条款",
  
  "error_general": "发生错误，请重试。",
  "error_network": "网络错误，请检查网络连接。",
  "error_timeout": "请求超时，请重试。",
  "error_unauthorized": "未授权访问。",
  "error_forbidden": "访问被禁止。",
  "error_not_found": "资源未找到。",
  "error_server_error": "服务器错误，请稍后重试。",
  "error_validation_failed": "验证失败。",
  
  "validation_required": "此字段为必填项。",
  "validation_email_invalid": "请输入有效的邮箱地址。",
  "validation_password_too_short": "密码至少需要8个字符。",
  "validation_password_mismatch": "密码不匹配。",
  "validation_phone_invalid": "请输入有效的手机号码。",
  
  "counter_section": "计数器",
  "button_pressed_zero": "您还没有按过按钮。",
  "button_pressed_one": "您按了 {count} 次按钮。",
  "button_pressed_other": "您按了 {count} 次按钮。",
  
  "datetime_section": "日期时间",
  "current_date": "当前日期",
  "current_time": "当前时间",
  "current_datetime": "当前日期时间",
  "relative_time": "相对时间",
  
  "number_section": "数字",
  "number": "数字",
  "currency": "货币",
  "percent": "百分比",
  
  "language_section": "语言",
  "current_language": "当前语言",
  "native_name": "本地名称",
  "text_direction": "文本方向",
  "select_language": "选择语言",
  "language_changed": "语言已切换为 {language}",
  
  "check_updates": "检查更新",
  "download_translations": "下载翻译",
  "clear_cache": "清除缓存",
  "checking_updates": "正在检查更新...",
  "updates_available_zero": "没有可用更新。",
  "updates_available_one": "有 {count} 个更新可用。",
  "updates_available_other": "有 {count} 个更新可用。",
  "no_updates_available": "没有可用更新。",
  "check_updates_failed": "检查更新失败。",
  "downloading_translations": "正在下载翻译...",
  "download_completed": "下载完成。",
  "download_failed": "下载失败。",
  "download_error": "下载错误。",
  "cache_cleared": "缓存已清除。",
  "clear_cache_failed": "清除缓存失败。",
  "download": "下载",
  "increment": "增加",
  
  "days_ago_zero": "今天",
  "days_ago_one": "{count} 天前",
  "days_ago_other": "{count} 天前",
  "hours_ago_zero": "现在",
  "hours_ago_one": "{count} 小时前",
  "hours_ago_other": "{count} 小时前",
  "minutes_ago_zero": "现在",
  "minutes_ago_one": "{count} 分钟前",
  "minutes_ago_other": "{count} 分钟前",
  "just_now": "刚刚"
}
```

#### 阿拉伯语资源文件（RTL 示例）
```json
// assets/l10n/ar_SA.json
{
  "app_name": "تطبيق Flutter للمؤسسات",
  "welcome": "مرحباً",
  "welcome_message": "مرحباً بك في {app_name}! يوضح هذا التطبيق ميزات التدويل على مستوى المؤسسة.",
  "login": "تسجيل الدخول",
  "logout": "تسجيل الخروج",
  "email": "البريد الإلكتروني",
  "password": "كلمة المرور",
  "confirm_password": "تأكيد كلمة المرور",
  "register": "التسجيل",
  "forgot_password": "نسيت كلمة المرور",
  "reset_password": "إعادة تعيين كلمة المرور",
  "save": "حفظ",
  "cancel": "إلغاء",
  "delete": "حذف",
  "edit": "تحرير",
  "add": "إضافة",
  "search": "بحث",
  "filter": "تصفية",
  "sort": "ترتيب",
  "refresh": "تحديث",
  "loading": "جاري التحميل...",
  "error": "خطأ",
  "success": "نجح",
  "warning": "تحذير",
  "info": "معلومات",
  "yes": "نعم",
  "no": "لا",
  "ok": "موافق",
  "close": "إغلاق",
  "back": "رجوع",
  "next": "التالي",
  "previous": "السابق",
  "finish": "إنهاء",
  "settings": "الإعدادات",
  "profile": "الملف الشخصي",
  "about": "حول",
  "help": "مساعدة",
  "contact": "اتصل بنا",
  "privacy": "سياسة الخصوصية",
  "terms": "شروط الخدمة",
  
  "counter_section": "العداد",
  "button_pressed_zero": "لم تضغط على الزر بعد.",
  "button_pressed_one": "ضغطت على الزر {count} مرة.",
  "button_pressed_other": "ضغطت على الزر {count} مرة.",
  
  "datetime_section": "التاريخ والوقت",
  "current_date": "التاريخ الحالي",
  "current_time": "الوقت الحالي",
  "current_datetime": "التاريخ والوقت الحالي",
  "relative_time": "الوقت النسبي",
  
  "number_section": "الأرقام",
  "number": "رقم",
  "currency": "عملة",
  "percent": "نسبة مئوية",
  
  "language_section": "اللغة",
  "current_language": "اللغة الحالية",
  "native_name": "الاسم المحلي",
  "text_direction": "اتجاه النص",
  "select_language": "اختر اللغة",
  "language_changed": "تم تغيير اللغة إلى {language}",
  
  "days_ago_zero": "اليوم",
  "days_ago_one": "منذ {count} يوم",
  "days_ago_other": "منذ {count} أيام",
  "hours_ago_zero": "الآن",
  "hours_ago_one": "منذ {count} ساعة",
  "hours_ago_other": "منذ {count} ساعات",
  "minutes_ago_zero": "الآن",
  "minutes_ago_one": "منذ {count} دقيقة",
  "minutes_ago_other": "منذ {count} دقائق",
  "just_now": "الآن"
}
```

## 6. 构建配置

### pubspec.yaml 配置
```yaml
# pubspec.yaml
name: flutter_enterprise_app
description: A Flutter enterprise application with internationalization

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: ^0.18.1
  shared_preferences: ^2.2.2
  dio: ^5.3.2
  path_provider: ^2.1.1
  crypto: ^3.0.3
  injectable: ^2.3.2
  get_it: ^7.6.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.7
  injectable_generator: ^2.4.1

flutter:
  uses-material-design: true
  
  # 本地化资源
  assets:
    - assets/l10n/
  
  # 生成本地化代码
  generate: true

# 本地化生成配置
flutter_intl:
  enabled: true
  class_name: S
  main_locale: en
  arb_dir: assets/l10n
  output_dir: lib/generated
```

### l10n.yaml 配置
```yaml
# l10n.yaml
arb-dir: assets/l10n
template-arb-file: en_US.arb
output-localization-file: app_localizations.dart
output-class: AppLocalizations
output-dir: lib/l10n
preferred-supported-locales: ['en_US']
```

## 7. 最佳实践和建议

### 国际化开发指南

1. **文本外部化**
   - 所有用户可见的文本都应该外部化到本地化文件中
   - 避免在代码中硬编码文本字符串
   - 使用有意义的键名，便于维护和理解

2. **复数形式处理**
   - 正确处理不同语言的复数规则
   - 使用 ICU 消息格式处理复杂的复数情况
   - 考虑零值的特殊处理

3. **日期时间格式化**
   - 使用 `Intl` 包进行日期时间格式化
   - 考虑不同地区的日期时间显示习惯
   - 提供相对时间显示功能

4. **数字和货币格式化**
   - 根据地区设置正确的数字分隔符
   - 使用正确的货币符号和格式
   - 考虑不同地区的数字显示习惯

5. **RTL 支持**
   - 为阿拉伯语、希伯来语等 RTL 语言提供支持
   - 正确设置文本方向和布局方向
   - 测试 RTL 布局的用户界面

6. **图片和图标本地化**
   - 为不同地区提供合适的图片和图标
   - 考虑文化敏感性和本地化需求
   - 使用矢量图标以支持不同的文本方向

7. **性能优化**
   - 延迟加载本地化资源
   - 缓存翻译结果以提高性能
   - 压缩本地化文件以减少应用大小

8. **测试策略**
   - 为每种支持的语言编写测试
   - 测试文本长度变化对 UI 的影响
   - 验证 RTL 布局的正确性

### 维护和更新

1. **版本控制**
   - 为本地化文件建立版本控制机制
   - 跟踪翻译的更新和变更
   - 提供回滚机制

2. **翻译工作流**
   - 建立标准的翻译工作流程
   - 使用专业的翻译管理工具
   - 定期审核和更新翻译

3. **质量保证**
   - 实施翻译质量检查
   - 进行本地化测试
   - 收集用户反馈并持续改进

## 总结

本文档提供了 Flutter 企业级应用的完整国际化和本地化解决方案，包括：

- **基础架构**：完整的本地化服务和配置管理
- **动态资源**：支持远程下载和更新本地化资源
- **工具扩展**：便捷的扩展方法和验证工具
- **UI 组件**：可复用的语言选择器组件
- **资源文件**：多语言资源文件示例
- **最佳实践**：开发和维护指南

该解决方案遵循 Clean Architecture 原则，提供了健壮、可扩展且易于维护的国际化功能，能够满足企业级应用的复杂需求。通过合理的架构设计和完善的工具支持，开发团队可以高效地实现多语言支持，为全球用户提供优质的本地化体验。