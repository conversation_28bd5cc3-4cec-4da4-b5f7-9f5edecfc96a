# 模块化架构集成指南

本文档提供了在现有 Flutter 企业级应用模板中集成模块化架构的详细步骤和实现指南。

## 1. 概述

### 1.1 集成目标

- 实现功能模块的可选启用/禁用
- 保持代码的零侵入性设计
- 支持构建时的代码优化
- 提供完整的开发工具支持

### 1.2 集成原则

- **向后兼容**：现有代码无需大幅修改
- **渐进式集成**：可以逐步迁移现有模块
- **配置驱动**：通过配置文件控制功能
- **类型安全**：编译时验证配置正确性

## 2. 集成步骤

### 2.1 第一阶段：基础设施搭建

#### 步骤 1：创建配置文件

在项目根目录创建 `assets/config/app_config.yaml`：

```yaml
# 应用功能配置
features:
  # 认证模块
  authentication:
    enabled: true
    providers: ["email", "google", "apple"]
    session_timeout: 3600
    
  # 权限管理模块
  authorization:
    enabled: true
    depends_on: ["authentication"]
    role_based: true
    
  # 国际化模块
  internationalization:
    enabled: true
    default_locale: "en"
    supported_locales: ["en", "zh", "es"]
    
  # 分析统计模块
  analytics:
    enabled: false
    providers: ["firebase", "mixpanel"]
    
  # 性能监控模块
  performance:
    enabled: false
    crash_reporting: true
    performance_monitoring: true
    
  # 推送通知模块
  push_notifications:
    enabled: false
    providers: ["fcm", "apns"]

# 构建配置
build:
  tree_shaking: true
  code_generation: true
  optimization_level: "release"
```

#### 步骤 2：更新 pubspec.yaml

```yaml
name: flutter_template
description: Enterprise Flutter Application Template

dependencies:
  flutter:
    sdk: flutter
  
  # 核心依赖
  get_it: ^7.6.4
  injectable: ^2.3.2
  yaml: ^3.1.2
  
  # 条件依赖（根据配置启用）
  firebase_auth: ^4.15.3  # authentication
  google_sign_in: ^6.1.5   # authentication
  firebase_analytics: ^10.7.4  # analytics
  firebase_crashlytics: ^3.4.8  # performance
  firebase_messaging: ^14.7.10  # push_notifications

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.7
  injectable_generator: ^2.4.1
  json_annotation: ^4.8.1
  json_serializable: ^6.7.1

flutter:
  assets:
    - assets/config/
    - assets/images/
    - assets/fonts/
```

#### 步骤 3：创建功能配置管理器

更新 `lib/core/config/feature_config.dart`：

```dart
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:yaml/yaml.dart';

/// 功能配置管理器
class FeatureConfig {
  static FeatureConfig? _instance;
  static FeatureConfig get instance => _instance ??= FeatureConfig._();
  
  FeatureConfig._();
  
  Map<String, dynamic>? _config;
  Map<String, bool>? _featureStates;
  
  /// 初始化配置
  Future<void> initialize() async {
    try {
      final configString = await rootBundle.loadString('assets/config/app_config.yaml');
      final yamlDoc = loadYaml(configString);
      _config = Map<String, dynamic>.from(yamlDoc);
      
      // 解析功能状态
      _parseFeatureStates();
      
      // 验证依赖关系
      _validateDependencies();
      
      print('✅ Feature configuration loaded successfully');
    } catch (e) {
      print('❌ Failed to load feature configuration: $e');
      // 使用默认配置
      _useDefaultConfig();
    }
  }
  
  /// 解析功能状态
  void _parseFeatureStates() {
    _featureStates = {};
    final features = _config?['features'] as Map<String, dynamic>? ?? {};
    
    for (final entry in features.entries) {
      final featureName = entry.key;
      final featureConfig = entry.value as Map<String, dynamic>? ?? {};
      final enabled = featureConfig['enabled'] as bool? ?? false;
      
      _featureStates![featureName] = enabled;
    }
  }
  
  /// 验证模块依赖关系
  void _validateDependencies() {
    final features = _config?['features'] as Map<String, dynamic>? ?? {};
    
    for (final entry in features.entries) {
      final featureName = entry.key;
      final featureConfig = entry.value as Map<String, dynamic>? ?? {};
      final enabled = featureConfig['enabled'] as bool? ?? false;
      
      if (enabled) {
        final dependencies = featureConfig['depends_on'] as List<dynamic>? ?? [];
        
        for (final dependency in dependencies) {
          final dependencyName = dependency.toString();
          if (!isFeatureEnabled(dependencyName)) {
            throw Exception(
              'Feature "$featureName" depends on "$dependencyName" which is disabled. '
              'Please enable "$dependencyName" or disable "$featureName".'
            );
          }
        }
      }
    }
  }
  
  /// 使用默认配置
  void _useDefaultConfig() {
    _config = {
      'features': {
        'authentication': {'enabled': true},
        'authorization': {'enabled': true},
        'internationalization': {'enabled': true},
        'analytics': {'enabled': false},
        'performance': {'enabled': false},
        'push_notifications': {'enabled': false},
      }
    };
    _parseFeatureStates();
  }
  
  /// 检查功能是否启用
  bool isFeatureEnabled(String featureName) {
    return _featureStates?[featureName] ?? false;
  }
  
  /// 获取功能配置
  Map<String, dynamic>? getFeatureConfig(String featureName) {
    final features = _config?['features'] as Map<String, dynamic>? ?? {};
    return features[featureName] as Map<String, dynamic>?;
  }
  
  /// 获取应用配置
  Map<String, dynamic>? getAppConfig() {
    return _config;
  }
  
  /// 获取所有启用的功能
  List<String> getEnabledFeatures() {
    return _featureStates?.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList() ?? [];
  }
  
  /// 获取功能依赖关系
  List<String> getFeatureDependencies(String featureName) {
    final featureConfig = getFeatureConfig(featureName);
    final dependencies = featureConfig?['depends_on'] as List<dynamic>? ?? [];
    return dependencies.map((dep) => dep.toString()).toList();
  }
}

/// 功能常量定义
class Features {
  static const String authentication = 'authentication';
  static const String authorization = 'authorization';
  static const String internationalization = 'internationalization';
  static const String analytics = 'analytics';
  static const String performance = 'performance';
  static const String pushNotifications = 'push_notifications';
}
```

### 2.2 第二阶段：条件依赖注入

#### 步骤 4：创建条件依赖注入系统

创建 `lib/core/di/conditional_di.dart`：

```dart
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import '../config/feature_config.dart';

/// 条件依赖注入装饰器
class ConditionalModule {
  final String featureName;
  const ConditionalModule(this.featureName);
}

/// 条件依赖注入管理器
class ConditionalDI {
  static final GetIt _getIt = GetIt.instance;
  
  /// 条件注册服务
  static void registerConditional<T extends Object>(
    String featureName,
    T Function() factory, {
    T Function()? noOpFactory,
    String? instanceName,
  }) {
    final isEnabled = FeatureConfig.instance.isFeatureEnabled(featureName);
    
    if (isEnabled) {
      _getIt.registerLazySingleton<T>(
        factory,
        instanceName: instanceName,
      );
      print('✅ Registered $T for feature: $featureName');
    } else if (noOpFactory != null) {
      _getIt.registerLazySingleton<T>(
        noOpFactory,
        instanceName: instanceName,
      );
      print('⚠️ Registered NoOp $T for disabled feature: $featureName');
    } else {
      print('❌ No NoOp implementation for $T in feature: $featureName');
    }
  }
  
  /// 条件注册工厂
  static void registerConditionalFactory<T extends Object>(
    String featureName,
    T Function() factory, {
    T Function()? noOpFactory,
    String? instanceName,
  }) {
    final isEnabled = FeatureConfig.instance.isFeatureEnabled(featureName);
    
    if (isEnabled) {
      _getIt.registerFactory<T>(
        factory,
        instanceName: instanceName,
      );
    } else if (noOpFactory != null) {
      _getIt.registerFactory<T>(
        noOpFactory,
        instanceName: instanceName,
      );
    }
  }
  
  /// 获取服务实例
  static T get<T extends Object>({String? instanceName}) {
    return _getIt.get<T>(instanceName: instanceName);
  }
  
  /// 检查服务是否已注册
  static bool isRegistered<T extends Object>({String? instanceName}) {
    return _getIt.isRegistered<T>(instanceName: instanceName);
  }
  
  /// 重置所有注册
  static Future<void> reset() async {
    await _getIt.reset();
  }
}
```

### 2.3 第三阶段：模块实现

#### 步骤 5：认证模块示例

创建 `lib/features/auth/domain/services/i_auth_service.dart`：

```dart
/// 认证结果
class AuthResult {
  final bool success;
  final String? token;
  final String? error;
  final Map<String, dynamic>? userData;
  
  const AuthResult({
    required this.success,
    this.token,
    this.error,
    this.userData,
  });
  
  factory AuthResult.success({
    required String token,
    Map<String, dynamic>? userData,
  }) {
    return AuthResult(
      success: true,
      token: token,
      userData: userData,
    );
  }
  
  factory AuthResult.failure(String error) {
    return AuthResult(
      success: false,
      error: error,
    );
  }
}

/// 认证服务接口
/// 
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: Features.authentication
abstract class IAuthService {
  /// 用户登录
  Future<AuthResult> login(String email, String password);
  
  /// 用户注册
  Future<AuthResult> register(String email, String password);
  
  /// 用户登出
  Future<void> logout();
  
  /// 获取当前用户
  Future<Map<String, dynamic>?> getCurrentUser();
  
  /// 检查登录状态
  Future<bool> isLoggedIn();
  
  /// 刷新令牌
  Future<AuthResult> refreshToken();
}
```

创建 `lib/features/auth/data/services/auth_service.dart`：

```dart
import 'package:injectable/injectable.dart';
import '../../domain/services/i_auth_service.dart';
import '../../../../core/config/feature_config.dart';

/// 认证服务实现
@Injectable(as: IAuthService)
class AuthService implements IAuthService {
  @override
  Future<AuthResult> login(String email, String password) async {
    try {
      // 实际的认证逻辑
      // 这里可以集成 Firebase Auth、OAuth 等
      
      // 模拟认证过程
      await Future.delayed(const Duration(seconds: 1));
      
      if (email.isNotEmpty && password.isNotEmpty) {
        return AuthResult.success(
          token: 'mock_jwt_token_${DateTime.now().millisecondsSinceEpoch}',
          userData: {
            'email': email,
            'id': 'user_123',
            'name': 'John Doe',
          },
        );
      } else {
        return AuthResult.failure('Invalid email or password');
      }
    } catch (e) {
      return AuthResult.failure('Authentication failed: $e');
    }
  }
  
  @override
  Future<AuthResult> register(String email, String password) async {
    try {
      // 实际的注册逻辑
      await Future.delayed(const Duration(seconds: 1));
      
      return AuthResult.success(
        token: 'mock_jwt_token_${DateTime.now().millisecondsSinceEpoch}',
        userData: {
          'email': email,
          'id': 'user_${DateTime.now().millisecondsSinceEpoch}',
          'name': 'New User',
        },
      );
    } catch (e) {
      return AuthResult.failure('Registration failed: $e');
    }
  }
  
  @override
  Future<void> logout() async {
    // 实际的登出逻辑
    await Future.delayed(const Duration(milliseconds: 500));
  }
  
  @override
  Future<Map<String, dynamic>?> getCurrentUser() async {
    // 实际获取当前用户逻辑
    return {
      'email': '<EMAIL>',
      'id': 'user_123',
      'name': 'John Doe',
    };
  }
  
  @override
  Future<bool> isLoggedIn() async {
    // 实际检查登录状态逻辑
    return true;
  }
  
  @override
  Future<AuthResult> refreshToken() async {
    // 实际刷新令牌逻辑
    return AuthResult.success(
      token: 'refreshed_token_${DateTime.now().millisecondsSinceEpoch}',
    );
  }
}
```

创建 `lib/features/auth/data/services/noop_auth_service.dart`：

```dart
import 'package:injectable/injectable.dart';
import '../../domain/services/i_auth_service.dart';

/// NoOp认证服务实现
/// 
/// 当authentication模块禁用时使用的空实现
/// 所有认证操作返回失败状态，不执行实际认证逻辑
@Injectable(as: IAuthService)
class NoOpAuthService implements IAuthService {
  static const String _disabledMessage = 'Authentication feature is disabled';
  
  @override
  Future<AuthResult> login(String email, String password) async {
    // 返回认证失败，功能未启用
    return AuthResult.failure(_disabledMessage);
  }
  
  @override
  Future<AuthResult> register(String email, String password) async {
    // 返回注册失败，功能未启用
    return AuthResult.failure(_disabledMessage);
  }
  
  @override
  Future<void> logout() async {
    // 空实现，不执行任何操作
    return;
  }
  
  @override
  Future<Map<String, dynamic>?> getCurrentUser() async {
    // 返回null，表示没有当前用户
    return null;
  }
  
  @override
  Future<bool> isLoggedIn() async {
    // 始终返回false，表示未登录
    return false;
  }
  
  @override
  Future<AuthResult> refreshToken() async {
    // 返回刷新失败，功能未启用
    return AuthResult.failure(_disabledMessage);
  }
}
```

### 2.4 第四阶段：路由守卫

#### 步骤 6：创建功能路由守卫

创建 `lib/core/routing/feature_guard.dart`：

```dart
import 'package:flutter/material.dart';
import '../config/feature_config.dart';
import '../../shared/widgets/feature_disabled_page.dart';

/// 功能路由守卫
class FeatureGuard {
  /// 检查功能是否可访问
  static bool canAccess(String featureName) {
    return FeatureConfig.instance.isFeatureEnabled(featureName);
  }
  
  /// 创建受保护的路由
  static Route<T> createProtectedRoute<T extends Object?>(
    String featureName,
    Widget Function(BuildContext) builder, {
    RouteSettings? settings,
    Widget? disabledWidget,
  }) {
    return MaterialPageRoute<T>(
      settings: settings,
      builder: (context) {
        if (canAccess(featureName)) {
          return builder(context);
        } else {
          return disabledWidget ?? FeatureDisabledPage(
            featureName: featureName,
          );
        }
      },
    );
  }
  
  /// 路由守卫中间件
  static Widget guardedWidget(
    String featureName,
    Widget child, {
    Widget? fallback,
  }) {
    if (canAccess(featureName)) {
      return child;
    } else {
      return fallback ?? FeatureDisabledPage(
        featureName: featureName,
      );
    }
  }
}

/// 条件路由器
class ConditionalRouter {
  static final Map<String, Route Function(RouteSettings)> _routes = {};
  
  /// 注册条件路由
  static void registerRoute(
    String routeName,
    String featureName,
    Route Function(RouteSettings) routeBuilder,
  ) {
    _routes[routeName] = (settings) {
      if (FeatureGuard.canAccess(featureName)) {
        return routeBuilder(settings);
      } else {
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => FeatureDisabledPage(
            featureName: featureName,
          ),
        );
      }
    };
  }
  
  /// 生成路由
  static Route<dynamic>? generateRoute(RouteSettings settings) {
    final routeBuilder = _routes[settings.name];
    if (routeBuilder != null) {
      return routeBuilder(settings);
    }
    return null;
  }
  
  /// 获取所有注册的路由
  static Map<String, Route Function(RouteSettings)> get routes => _routes;
}
```

### 2.5 第五阶段：UI 条件渲染

#### 步骤 7：创建功能包装器组件

创建 `lib/shared/widgets/feature_wrapper.dart`：

```dart
import 'package:flutter/material.dart';
import '../../core/config/feature_config.dart';

/// 功能包装器组件
/// 
/// 根据功能配置条件性地渲染子组件
class FeatureWrapper extends StatelessWidget {
  final String featureName;
  final Widget child;
  final Widget? fallback;
  final bool showFallbackWhenDisabled;
  
  const FeatureWrapper({
    Key? key,
    required this.featureName,
    required this.child,
    this.fallback,
    this.showFallbackWhenDisabled = false,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final isEnabled = FeatureConfig.instance.isFeatureEnabled(featureName);
    
    if (isEnabled) {
      return child;
    } else {
      if (showFallbackWhenDisabled && fallback != null) {
        return fallback!;
      } else {
        return const SizedBox.shrink();
      }
    }
  }
}

/// 功能构建器
/// 
/// 提供更灵活的条件渲染控制
class FeatureBuilder extends StatelessWidget {
  final String featureName;
  final Widget Function(BuildContext context, bool isEnabled) builder;
  
  const FeatureBuilder({
    Key? key,
    required this.featureName,
    required this.builder,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final isEnabled = FeatureConfig.instance.isFeatureEnabled(featureName);
    return builder(context, isEnabled);
  }
}

/// 多功能条件组件
/// 
/// 支持多个功能的组合条件判断
class MultiFeatureWrapper extends StatelessWidget {
  final List<String> featureNames;
  final Widget child;
  final Widget? fallback;
  final bool requireAll; // true: 需要所有功能都启用, false: 任一功能启用即可
  
  const MultiFeatureWrapper({
    Key? key,
    required this.featureNames,
    required this.child,
    this.fallback,
    this.requireAll = true,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    bool shouldShow;
    
    if (requireAll) {
      // 所有功能都必须启用
      shouldShow = featureNames.every(
        (feature) => FeatureConfig.instance.isFeatureEnabled(feature),
      );
    } else {
      // 任一功能启用即可
      shouldShow = featureNames.any(
        (feature) => FeatureConfig.instance.isFeatureEnabled(feature),
      );
    }
    
    if (shouldShow) {
      return child;
    } else {
      return fallback ?? const SizedBox.shrink();
    }
  }
}
```

创建 `lib/shared/widgets/feature_disabled_page.dart`：

```dart
import 'package:flutter/material.dart';

/// 功能禁用页面
/// 
/// 当用户尝试访问已禁用的功能时显示
class FeatureDisabledPage extends StatelessWidget {
  final String featureName;
  final String? customMessage;
  final VoidCallback? onRetry;
  
  const FeatureDisabledPage({
    Key? key,
    required this.featureName,
    this.customMessage,
    this.onRetry,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('功能不可用'),
        backgroundColor: Colors.orange,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.block,
                size: 80,
                color: Colors.orange[300],
              ),
              const SizedBox(height: 24),
              Text(
                '功能暂不可用',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.orange[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                customMessage ?? '"$featureName" 功能当前已禁用。\n请联系管理员或检查应用配置。',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 32),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('返回'),
                  ),
                  if (onRetry != null) ..[
                    const SizedBox(width: 16),
                    ElevatedButton(
                      onPressed: onRetry,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                      ),
                      child: const Text('重试'),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

## 3. 应用初始化集成

### 步骤 8：更新主应用入口

更新 `lib/main.dart`：

```dart
import 'package:flutter/material.dart';
import 'core/config/feature_config.dart';
import 'core/di/conditional_di.dart';
import 'core/routing/feature_guard.dart';
import 'features/auth/domain/services/i_auth_service.dart';
import 'features/auth/data/services/auth_service.dart';
import 'features/auth/data/services/noop_auth_service.dart';
import 'shared/widgets/feature_wrapper.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化功能配置
  await FeatureConfig.instance.initialize();
  
  // 配置依赖注入
  await _configureDependencies();
  
  // 配置路由
  _configureRoutes();
  
  runApp(const MyApp());
}

/// 配置依赖注入
Future<void> _configureDependencies() async {
  // 注册认证服务
  ConditionalDI.registerConditional<IAuthService>(
    Features.authentication,
    () => AuthService(),
    noOpFactory: () => NoOpAuthService(),
  );
  
  // 可以继续注册其他服务...
  
  print('✅ Dependencies configured successfully');
}

/// 配置路由
void _configureRoutes() {
  // 注册受保护的路由
  ConditionalRouter.registerRoute(
    '/login',
    Features.authentication,
    (settings) => MaterialPageRoute(
      settings: settings,
      builder: (context) => const LoginPage(),
    ),
  );
  
  ConditionalRouter.registerRoute(
    '/profile',
    Features.authentication,
    (settings) => MaterialPageRoute(
      settings: settings,
      builder: (context) => const ProfilePage(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Enterprise Template',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const HomePage(),
      onGenerateRoute: ConditionalRouter.generateRoute,
    );
  }
}

class HomePage extends StatelessWidget {
  const HomePage({Key? key}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('企业级应用模板'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // 条件显示认证相关功能
          FeatureWrapper(
            featureName: Features.authentication,
            child: Card(
              child: ListTile(
                leading: const Icon(Icons.login),
                title: const Text('用户认证'),
                subtitle: const Text('登录、注册、用户管理'),
                onTap: () => Navigator.pushNamed(context, '/login'),
              ),
            ),
          ),
          
          // 条件显示分析功能
          FeatureWrapper(
            featureName: Features.analytics,
            child: Card(
              child: ListTile(
                leading: const Icon(Icons.analytics),
                title: const Text('数据分析'),
                subtitle: const Text('用户行为分析、统计报表'),
                onTap: () => Navigator.pushNamed(context, '/analytics'),
              ),
            ),
          ),
          
          // 使用功能构建器进行更复杂的条件渲染
          FeatureBuilder(
            featureName: Features.performance,
            builder: (context, isEnabled) {
              return Card(
                color: isEnabled ? null : Colors.grey[200],
                child: ListTile(
                  leading: Icon(
                    Icons.speed,
                    color: isEnabled ? null : Colors.grey,
                  ),
                  title: Text(
                    '性能监控',
                    style: TextStyle(
                      color: isEnabled ? null : Colors.grey,
                    ),
                  ),
                  subtitle: Text(
                    isEnabled ? '实时性能监控、崩溃报告' : '功能已禁用',
                    style: TextStyle(
                      color: isEnabled ? null : Colors.grey,
                    ),
                  ),
                  onTap: isEnabled 
                    ? () => Navigator.pushNamed(context, '/performance')
                    : null,
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

// 示例页面
class LoginPage extends StatelessWidget {
  const LoginPage({Key? key}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('登录')),
      body: const Center(
        child: Text('登录页面 - 认证功能已启用'),
      ),
    );
  }
}

class ProfilePage extends StatelessWidget {
  const ProfilePage({Key? key}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('个人资料')),
      body: const Center(
        child: Text('个人资料页面 - 认证功能已启用'),
      ),
    );
  }
}
```

## 4. 测试集成

### 步骤 9：创建功能配置测试

创建 `test/core/config/feature_config_test.dart`：

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';
import 'package:flutter_template/core/config/feature_config.dart';

void main() {
  group('FeatureConfig Tests', () {
    late FeatureConfig featureConfig;
    
    setUp(() {
      featureConfig = FeatureConfig.instance;
    });
    
    testWidgets('should load configuration from assets', (tester) async {
      // 模拟配置文件内容
      const configYaml = '''
features:
  authentication:
    enabled: true
  analytics:
    enabled: false
''';
      
      // 模拟资源加载
      tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(
        const MethodChannel('flutter/assets'),
        (MethodCall methodCall) async {
          if (methodCall.method == 'loadString' && 
              methodCall.arguments == 'assets/config/app_config.yaml') {
            return configYaml;
          }
          return null;
        },
      );
      
      await featureConfig.initialize();
      
      expect(featureConfig.isFeatureEnabled('authentication'), true);
      expect(featureConfig.isFeatureEnabled('analytics'), false);
    });
    
    test('should validate feature dependencies', () {
      // 测试依赖关系验证
      expect(
        () => featureConfig.initialize(),
        throwsA(isA<Exception>()),
      );
    });
    
    test('should return enabled features list', () {
      final enabledFeatures = featureConfig.getEnabledFeatures();
      expect(enabledFeatures, isA<List<String>>());
    });
  });
}
```

## 5. 开发工具集成

### 步骤 10：创建功能管理 CLI 工具

创建 `tool/feature_cli.dart`：

```dart
import 'dart:io';
import 'dart:convert';
import 'package:yaml/yaml.dart';

/// 功能管理 CLI 工具
class FeatureCLI {
  static const String configPath = 'assets/config/app_config.yaml';
  
  /// 显示所有功能状态
  static Future<void> listFeatures() async {
    try {
      final config = await _loadConfig();
      final features = config['features'] as Map<String, dynamic>? ?? {};
      
      print('\n📋 功能模块状态:');
      print('=' * 50);
      
      for (final entry in features.entries) {
        final featureName = entry.key;
        final featureConfig = entry.value as Map<String, dynamic>? ?? {};
        final enabled = featureConfig['enabled'] as bool? ?? false;
        final dependencies = featureConfig['depends_on'] as List<dynamic>? ?? [];
        
        final status = enabled ? '✅ 启用' : '❌ 禁用';
        print('$status $featureName');
        
        if (dependencies.isNotEmpty) {
          print('   依赖: ${dependencies.join(', ')}');
        }
      }
      print('');
    } catch (e) {
      print('❌ 读取配置失败: $e');
    }
  }
  
  /// 启用功能
  static Future<void> enableFeature(String featureName) async {
    await _updateFeature(featureName, true);
    print('✅ 已启用功能: $featureName');
  }
  
  /// 禁用功能
  static Future<void> disableFeature(String featureName) async {
    await _updateFeature(featureName, false);
    print('❌ 已禁用功能: $featureName');
  }
  
  /// 验证配置
  static Future<void> validateConfig() async {
    try {
      final config = await _loadConfig();
      final features = config['features'] as Map<String, dynamic>? ?? {};
      
      print('\n🔍 验证功能配置...');
      
      bool hasErrors = false;
      
      for (final entry in features.entries) {
        final featureName = entry.key;
        final featureConfig = entry.value as Map<String, dynamic>? ?? {};
        final enabled = featureConfig['enabled'] as bool? ?? false;
        final dependencies = featureConfig['depends_on'] as List<dynamic>? ?? [];
        
        if (enabled) {
          for (final dependency in dependencies) {
            final depName = dependency.toString();
            final depConfig = features[depName] as Map<String, dynamic>? ?? {};
            final depEnabled = depConfig['enabled'] as bool? ?? false;
            
            if (!depEnabled) {
              print('❌ 错误: $featureName 依赖 $depName，但 $depName 未启用');
              hasErrors = true;
            }
          }
        }
      }
      
      if (!hasErrors) {
        print('✅ 配置验证通过');
      }
    } catch (e) {
      print('❌ 配置验证失败: $e');
    }
  }
  
  /// 生成功能报告
  static Future<void> generateReport() async {
    try {
      final config = await _loadConfig();
      final features = config['features'] as Map<String, dynamic>? ?? {};
      
      final report = {
        'timestamp': DateTime.now().toIso8601String(),
        'total_features': features.length,
        'enabled_features': features.values
            .where((f) => (f as Map<String, dynamic>)['enabled'] == true)
            .length,
        'features': features,
      };
      
      final reportFile = File('feature_report.json');
      await reportFile.writeAsString(
        const JsonEncoder.withIndent('  ').convert(report),
      );
      
      print('✅ 功能报告已生成: feature_report.json');
    } catch (e) {
      print('❌ 生成报告失败: $e');
    }
  }
  
  /// 加载配置文件
  static Future<Map<String, dynamic>> _loadConfig() async {
    final file = File(configPath);
    if (!await file.exists()) {
      throw Exception('配置文件不存在: $configPath');
    }
    
    final content = await file.readAsString();
    final yamlDoc = loadYaml(content);
    return Map<String, dynamic>.from(yamlDoc);
  }
  
  /// 更新功能状态
  static Future<void> _updateFeature(String featureName, bool enabled) async {
    final config = await _loadConfig();
    final features = config['features'] as Map<String, dynamic>? ?? {};
    
    if (!features.containsKey(featureName)) {
      throw Exception('功能不存在: $featureName');
    }
    
    final featureConfig = features[featureName] as Map<String, dynamic>? ?? {};
    featureConfig['enabled'] = enabled;
    
    // 保存配置
    await _saveConfig(config);
  }
  
  /// 保存配置文件
  static Future<void> _saveConfig(Map<String, dynamic> config) async {
    // 这里简化处理，实际应该保持 YAML 格式
    final file = File(configPath);
    final yamlString = _mapToYaml(config);
    await file.writeAsString(yamlString);
  }
  
  /// 将 Map 转换为 YAML 字符串（简化版）
  static String _mapToYaml(Map<String, dynamic> map, [int indent = 0]) {
    final buffer = StringBuffer();
    final spaces = '  ' * indent;
    
    for (final entry in map.entries) {
      buffer.write('$spaces${entry.key}:');
      
      if (entry.value is Map) {
        buffer.writeln();
        buffer.write(_mapToYaml(entry.value as Map<String, dynamic>, indent + 1));
      } else if (entry.value is List) {
        buffer.writeln();
        for (final item in entry.value as List) {
          buffer.writeln('$spaces  - $item');
        }
      } else {
        buffer.writeln(' ${entry.value}');
      }
    }
    
    return buffer.toString();
  }
}

/// CLI 入口点
void main(List<String> arguments) async {
  if (arguments.isEmpty) {
    _printUsage();
    return;
  }
  
  final command = arguments[0];
  
  switch (command) {
    case 'list':
      await FeatureCLI.listFeatures();
      break;
    case 'enable':
      if (arguments.length < 2) {
        print('❌ 请指定要启用的功能名称');
        return;
      }
      await FeatureCLI.enableFeature(arguments[1]);
      break;
    case 'disable':
      if (arguments.length < 2) {
        print('❌ 请指定要禁用的功能名称');
        return;
      }
      await FeatureCLI.disableFeature(arguments[1]);
      break;
    case 'validate':
      await FeatureCLI.validateConfig();
      break;
    case 'report':
      await FeatureCLI.generateReport();
      break;
    default:
      print('❌ 未知命令: $command');
      _printUsage();
  }
}

void _printUsage() {
  print('''
🛠️  功能管理 CLI 工具

用法:
  dart tool/feature_cli.dart <command> [arguments]

命令:
  list                    显示所有功能状态
  enable <feature>        启用指定功能
  disable <feature>       禁用指定功能
  validate               验证功能配置
  report                 生成功能报告

示例:
  dart tool/feature_cli.dart list
  dart tool/feature_cli.dart enable authentication
  dart tool/feature_cli.dart disable analytics
''');
}
```

## 6. 总结

通过以上步骤，您已经成功在 Flutter 企业级应用模板中集成了完整的模块化架构。这个架构提供了：

### 6.1 核心特性

- ✅ **配置驱动**：通过 YAML 文件控制功能启用/禁用
- ✅ **零侵入性**：禁用模块不影响应用正常运行
- ✅ **类型安全**：编译时验证配置正确性
- ✅ **依赖管理**：自动处理模块间依赖关系
- ✅ **条件渲染**：UI 组件根据配置动态显示
- ✅ **路由守卫**：保护需要特定功能的页面
- ✅ **开发工具**：CLI 工具简化配置管理

### 6.2 使用优势

1. **灵活配置**：可以为不同客户或环境提供不同的功能组合
2. **代码复用**：同一套代码支持多种产品变体
3. **维护简单**：模块化设计降低维护复杂度
4. **性能优化**：未使用的模块不会影响应用性能
5. **测试友好**：支持各种功能组合的测试

### 6.3 下一步

- 根据项目需求添加更多可选模块
- 实现构建时代码生成和优化
- 集成 CI/CD 流程中的配置验证
- 添加更多开发工具和 IDE 插件支持

这个模块化架构为您的 Flutter 企业级应用提供了强大的灵活性和可维护性，让您能够轻松应对不同的业务需求和部署场景。