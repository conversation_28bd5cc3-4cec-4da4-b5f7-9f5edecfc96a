# 数据持久化层实现示例

## 1. 数据库配置与设计

### Drift数据库配置
```dart
// packages/core/core_database/lib/src/app_database.dart
import 'package:drift/drift.dart';
import 'package:drift_flutter/drift_flutter.dart';
import 'package:injectable/injectable.dart';
import 'tables/user_table.dart';
import 'tables/auth_table.dart';
import 'tables/cache_table.dart';
import 'tables/sync_queue_table.dart';
import 'daos/user_dao.dart';
import 'daos/auth_dao.dart';
import 'daos/cache_dao.dart';
import 'daos/sync_queue_dao.dart';

part 'app_database.g.dart';

@DriftDatabase(
  tables: [
    UserTable,
    AuthTable,
    CacheTable,
    SyncQueueTable,
  ],
  daos: [
    UserDao,
    AuthDao,
    CacheDao,
    SyncQueueDao,
  ],
)
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        await m.createAll();
        await _insertInitialData();
      },
      onUpgrade: (Migrator m, int from, int to) async {
        // 处理数据库升级
        if (from < 2) {
          // 版本1到版本2的升级逻辑
        }
      },
      beforeOpen: (details) async {
        // 启用外键约束
        await customStatement('PRAGMA foreign_keys = ON');
        
        // 设置WAL模式以提高并发性能
        await customStatement('PRAGMA journal_mode = WAL');
        
        // 设置同步模式
        await customStatement('PRAGMA synchronous = NORMAL');
        
        // 设置缓存大小
        await customStatement('PRAGMA cache_size = -2000');
      },
    );
  }

  /// 插入初始数据
  Future<void> _insertInitialData() async {
    // 可以在这里插入一些初始配置数据
  }

  /// 清空所有数据
  Future<void> clearAllData() async {
    await transaction(() async {
      await delete(userTable).go();
      await delete(authTable).go();
      await delete(cacheTable).go();
      await delete(syncQueueTable).go();
    });
  }

  /// 获取数据库大小
  Future<int> getDatabaseSize() async {
    final result = await customSelect(
      'SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()'
    ).getSingle();
    return result.data['size'] as int;
  }

  /// 优化数据库
  Future<void> vacuum() async {
    await customStatement('VACUUM');
  }
}

/// 创建数据库连接
QueryExecutor _openConnection() {
  return driftDatabase(
    name: 'app_database',
    web: DriftWebOptions(
      sqlite3Wasm: Uri.parse('sqlite3.wasm'),
      driftWorker: Uri.parse('drift_worker.js'),
    ),
  );
}

/// 数据库模块
@module
abstract class DatabaseModule {
  @singleton
  AppDatabase provideAppDatabase() => AppDatabase();
}
```

### 数据表定义

#### 用户表
```dart
// packages/core/core_database/lib/src/tables/user_table.dart
import 'package:drift/drift.dart';

@DataClassName('UserTableData')
class UserTable extends Table {
  @override
  String get tableName => 'users';

  TextColumn get id => text()();
  TextColumn get email => text()();
  TextColumn get username => text()();
  TextColumn get firstName => text().nullable()();
  TextColumn get lastName => text().nullable()();
  TextColumn get avatarUrl => text().nullable()();
  TextColumn get bio => text().nullable()();
  DateTimeColumn get birthDate => dateTime().nullable()();
  TextColumn get phoneNumber => text().nullable()();
  BoolColumn get isVerified => boolean().withDefault(const Constant(false))();
  BoolColumn get isActive => boolean().withDefault(const Constant(true))();
  IntColumn get followersCount => integer().withDefault(const Constant(0))();
  IntColumn get followingCount => integer().withDefault(const Constant(0))();
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get updatedAt => dateTime()();
  DateTimeColumn get lastSyncAt => dateTime().nullable()();

  @override
  Set<Column> get primaryKey => {id};

  @override
  List<Set<Column>> get uniqueKeys => [
    {email},
    {username},
  ];
}
```

#### 认证表
```dart
// packages/core/core_database/lib/src/tables/auth_table.dart
import 'package:drift/drift.dart';

@DataClassName('AuthTableData')
class AuthTable extends Table {
  @override
  String get tableName => 'auth_tokens';

  TextColumn get id => text()();
  TextColumn get userId => text().references(UserTable, #id, onDelete: KeyAction.cascade)();
  TextColumn get accessToken => text()();
  TextColumn get refreshToken => text()();
  TextColumn get tokenType => text().withDefault(const Constant('Bearer'))();
  DateTimeColumn get expiresAt => dateTime()();
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get updatedAt => dateTime()();

  @override
  Set<Column> get primaryKey => {id};
}
```

#### 缓存表
```dart
// packages/core/core_database/lib/src/tables/cache_table.dart
import 'package:drift/drift.dart';

@DataClassName('CacheTableData')
class CacheTable extends Table {
  @override
  String get tableName => 'cache_entries';

  TextColumn get key => text()();
  TextColumn get value => text()();
  TextColumn get type => text().withDefault(const Constant('string'))();
  DateTimeColumn get expiresAt => dateTime().nullable()();
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get accessedAt => dateTime()();
  IntColumn get accessCount => integer().withDefault(const Constant(0))();

  @override
  Set<Column> get primaryKey => {key};
}
```

#### 同步队列表
```dart
// packages/core/core_database/lib/src/tables/sync_queue_table.dart
import 'package:drift/drift.dart';

@DataClassName('SyncQueueTableData')
class SyncQueueTable extends Table {
  @override
  String get tableName => 'sync_queue';

  TextColumn get id => text()();
  TextColumn get operation => text()(); // CREATE, UPDATE, DELETE
  TextColumn get entityType => text()(); // user, post, comment, etc.
  TextColumn get entityId => text()();
  TextColumn get data => text().nullable()(); // JSON data
  IntColumn get retryCount => integer().withDefault(const Constant(0))();
  IntColumn get maxRetries => integer().withDefault(const Constant(3))();
  TextColumn get status => text().withDefault(const Constant('pending'))(); // pending, processing, completed, failed
  TextColumn get errorMessage => text().nullable()();
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get updatedAt => dateTime()();
  DateTimeColumn get scheduledAt => dateTime().nullable()();

  @override
  Set<Column> get primaryKey => {id};
}
```

## 2. DAO实现

### 用户DAO
```dart
// packages/core/core_database/lib/src/daos/user_dao.dart
import 'package:drift/drift.dart';
import 'package:injectable/injectable.dart';
import '../app_database.dart';
import '../tables/user_table.dart';

@DriftAccessor(tables: [UserTable])
class UserDao extends DatabaseAccessor<AppDatabase> with _$UserDaoMixin {
  UserDao(super.db);

  /// 获取所有用户
  Future<List<UserTableData>> getAllUsers() {
    return select(userTable).get();
  }

  /// 根据ID获取用户
  Future<UserTableData?> getUserById(String id) {
    return (select(userTable)..where((u) => u.id.equals(id))).getSingleOrNull();
  }

  /// 根据邮箱获取用户
  Future<UserTableData?> getUserByEmail(String email) {
    return (select(userTable)..where((u) => u.email.equals(email))).getSingleOrNull();
  }

  /// 根据用户名获取用户
  Future<UserTableData?> getUserByUsername(String username) {
    return (select(userTable)..where((u) => u.username.equals(username))).getSingleOrNull();
  }

  /// 搜索用户
  Future<List<UserTableData>> searchUsers(String query, {int? limit, int? offset}) {
    final searchQuery = select(userTable)
      ..where((u) => 
          u.username.contains(query) |
          u.firstName.contains(query) |
          u.lastName.contains(query) |
          u.email.contains(query)
      )
      ..orderBy([(u) => OrderingTerm.asc(u.username)]);
    
    if (limit != null) {
      searchQuery.limit(limit, offset: offset);
    }
    
    return searchQuery.get();
  }

  /// 获取活跃用户
  Future<List<UserTableData>> getActiveUsers({int? limit}) {
    final query = select(userTable)
      ..where((u) => u.isActive.equals(true))
      ..orderBy([(u) => OrderingTerm.desc(u.createdAt)]);
    
    if (limit != null) {
      query.limit(limit);
    }
    
    return query.get();
  }

  /// 获取已验证用户
  Future<List<UserTableData>> getVerifiedUsers({int? limit}) {
    final query = select(userTable)
      ..where((u) => u.isVerified.equals(true))
      ..orderBy([(u) => OrderingTerm.desc(u.followersCount)]);
    
    if (limit != null) {
      query.limit(limit);
    }
    
    return query.get();
  }

  /// 插入用户
  Future<int> insertUser(UserTableCompanion user) {
    return into(userTable).insert(user);
  }

  /// 插入或更新用户
  Future<int> insertOrUpdateUser(UserTableCompanion user) {
    return into(userTable).insertOnConflictUpdate(user);
  }

  /// 批量插入用户
  Future<void> insertUsers(List<UserTableCompanion> users) {
    return batch((batch) {
      batch.insertAllOnConflictUpdate(userTable, users);
    });
  }

  /// 更新用户
  Future<bool> updateUser(String id, UserTableCompanion user) async {
    final rowsAffected = await (update(userTable)
      ..where((u) => u.id.equals(id))
    ).write(user);
    return rowsAffected > 0;
  }

  /// 更新用户头像
  Future<bool> updateUserAvatar(String id, String avatarUrl) async {
    final rowsAffected = await (update(userTable)
      ..where((u) => u.id.equals(id))
    ).write(UserTableCompanion(
      avatarUrl: Value(avatarUrl),
      updatedAt: Value(DateTime.now()),
    ));
    return rowsAffected > 0;
  }

  /// 更新关注者数量
  Future<bool> updateFollowersCount(String id, int count) async {
    final rowsAffected = await (update(userTable)
      ..where((u) => u.id.equals(id))
    ).write(UserTableCompanion(
      followersCount: Value(count),
      updatedAt: Value(DateTime.now()),
    ));
    return rowsAffected > 0;
  }

  /// 更新关注数量
  Future<bool> updateFollowingCount(String id, int count) async {
    final rowsAffected = await (update(userTable)
      ..where((u) => u.id.equals(id))
    ).write(UserTableCompanion(
      followingCount: Value(count),
      updatedAt: Value(DateTime.now()),
    ));
    return rowsAffected > 0;
  }

  /// 标记用户为已验证
  Future<bool> markUserAsVerified(String id) async {
    final rowsAffected = await (update(userTable)
      ..where((u) => u.id.equals(id))
    ).write(UserTableCompanion(
      isVerified: const Value(true),
      updatedAt: Value(DateTime.now()),
    ));
    return rowsAffected > 0;
  }

  /// 停用用户
  Future<bool> deactivateUser(String id) async {
    final rowsAffected = await (update(userTable)
      ..where((u) => u.id.equals(id))
    ).write(UserTableCompanion(
      isActive: const Value(false),
      updatedAt: Value(DateTime.now()),
    ));
    return rowsAffected > 0;
  }

  /// 删除用户
  Future<bool> deleteUser(String id) async {
    final rowsAffected = await (delete(userTable)
      ..where((u) => u.id.equals(id))
    ).go();
    return rowsAffected > 0;
  }

  /// 获取用户统计信息
  Future<Map<String, int>> getUserStats() async {
    final totalUsers = await (selectOnly(userTable)
      ..addColumns([userTable.id.count()])
    ).getSingle();
    
    final activeUsers = await (selectOnly(userTable)
      ..addColumns([userTable.id.count()])
      ..where(userTable.isActive.equals(true))
    ).getSingle();
    
    final verifiedUsers = await (selectOnly(userTable)
      ..addColumns([userTable.id.count()])
      ..where(userTable.isVerified.equals(true))
    ).getSingle();

    return {
      'total': totalUsers.read(userTable.id.count()) ?? 0,
      'active': activeUsers.read(userTable.id.count()) ?? 0,
      'verified': verifiedUsers.read(userTable.id.count()) ?? 0,
    };
  }

  /// 监听用户变化
  Stream<List<UserTableData>> watchUsers() {
    return select(userTable).watch();
  }

  /// 监听特定用户变化
  Stream<UserTableData?> watchUser(String id) {
    return (select(userTable)..where((u) => u.id.equals(id))).watchSingleOrNull();
  }

  /// 清理过期数据
  Future<int> cleanupOldUsers(Duration maxAge) async {
    final cutoffDate = DateTime.now().subtract(maxAge);
    return await (delete(userTable)
      ..where((u) => 
          u.isActive.equals(false) & 
          u.updatedAt.isSmallerThanValue(cutoffDate)
      )
    ).go();
  }
}
```

### 认证DAO
```dart
// packages/core/core_database/lib/src/daos/auth_dao.dart
import 'package:drift/drift.dart';
import 'package:injectable/injectable.dart';
import '../app_database.dart';
import '../tables/auth_table.dart';
import '../tables/user_table.dart';

@DriftAccessor(tables: [AuthTable, UserTable])
class AuthDao extends DatabaseAccessor<AppDatabase> with _$AuthDaoMixin {
  AuthDao(super.db);

  /// 获取当前认证信息
  Future<AuthTableData?> getCurrentAuth() {
    return (select(authTable)
      ..orderBy([(a) => OrderingTerm.desc(a.createdAt)])
      ..limit(1)
    ).getSingleOrNull();
  }

  /// 根据用户ID获取认证信息
  Future<AuthTableData?> getAuthByUserId(String userId) {
    return (select(authTable)..where((a) => a.userId.equals(userId))).getSingleOrNull();
  }

  /// 根据访问令牌获取认证信息
  Future<AuthTableData?> getAuthByAccessToken(String accessToken) {
    return (select(authTable)..where((a) => a.accessToken.equals(accessToken))).getSingleOrNull();
  }

  /// 根据刷新令牌获取认证信息
  Future<AuthTableData?> getAuthByRefreshToken(String refreshToken) {
    return (select(authTable)..where((a) => a.refreshToken.equals(refreshToken))).getSingleOrNull();
  }

  /// 检查令牌是否过期
  Future<bool> isTokenExpired(String accessToken) async {
    final auth = await getAuthByAccessToken(accessToken);
    if (auth == null) return true;
    
    return DateTime.now().isAfter(auth.expiresAt);
  }

  /// 获取有效的认证信息
  Future<AuthTableData?> getValidAuth() async {
    final auth = await getCurrentAuth();
    if (auth == null) return null;
    
    if (DateTime.now().isBefore(auth.expiresAt)) {
      return auth;
    }
    
    return null;
  }

  /// 保存认证信息
  Future<int> saveAuth(AuthTableCompanion auth) async {
    // 先清除旧的认证信息
    await clearAuth();
    
    // 插入新的认证信息
    return into(authTable).insert(auth);
  }

  /// 更新访问令牌
  Future<bool> updateAccessToken(
    String userId,
    String newAccessToken,
    DateTime expiresAt,
  ) async {
    final rowsAffected = await (update(authTable)
      ..where((a) => a.userId.equals(userId))
    ).write(AuthTableCompanion(
      accessToken: Value(newAccessToken),
      expiresAt: Value(expiresAt),
      updatedAt: Value(DateTime.now()),
    ));
    return rowsAffected > 0;
  }

  /// 更新刷新令牌
  Future<bool> updateRefreshToken(
    String userId,
    String newRefreshToken,
  ) async {
    final rowsAffected = await (update(authTable)
      ..where((a) => a.userId.equals(userId))
    ).write(AuthTableCompanion(
      refreshToken: Value(newRefreshToken),
      updatedAt: Value(DateTime.now()),
    ));
    return rowsAffected > 0;
  }

  /// 更新完整的令牌信息
  Future<bool> updateTokens(
    String userId,
    String newAccessToken,
    String newRefreshToken,
    DateTime expiresAt,
  ) async {
    final rowsAffected = await (update(authTable)
      ..where((a) => a.userId.equals(userId))
    ).write(AuthTableCompanion(
      accessToken: Value(newAccessToken),
      refreshToken: Value(newRefreshToken),
      expiresAt: Value(expiresAt),
      updatedAt: Value(DateTime.now()),
    ));
    return rowsAffected > 0;
  }

  /// 清除认证信息
  Future<int> clearAuth() {
    return delete(authTable).go();
  }

  /// 清除特定用户的认证信息
  Future<int> clearAuthByUserId(String userId) {
    return (delete(authTable)..where((a) => a.userId.equals(userId))).go();
  }

  /// 获取认证用户信息
  Future<UserTableData?> getAuthenticatedUser() async {
    final auth = await getValidAuth();
    if (auth == null) return null;
    
    return await (select(userTable)..where((u) => u.id.equals(auth.userId))).getSingleOrNull();
  }

  /// 监听认证状态变化
  Stream<AuthTableData?> watchAuth() {
    return (select(authTable)
      ..orderBy([(a) => OrderingTerm.desc(a.createdAt)])
      ..limit(1)
    ).watchSingleOrNull();
  }

  /// 监听认证用户信息变化
  Stream<UserTableData?> watchAuthenticatedUser() {
    return watchAuth().asyncMap((auth) async {
      if (auth == null) return null;
      
      // 检查令牌是否过期
      if (DateTime.now().isAfter(auth.expiresAt)) {
        return null;
      }
      
      return await (select(userTable)..where((u) => u.id.equals(auth.userId))).getSingleOrNull();
    });
  }

  /// 清理过期的认证信息
  Future<int> cleanupExpiredAuth() {
    return (delete(authTable)
      ..where((a) => a.expiresAt.isSmallerThanValue(DateTime.now()))
    ).go();
  }
}
```

### 缓存DAO
```dart
// packages/core/core_database/lib/src/daos/cache_dao.dart
import 'package:drift/drift.dart';
import 'package:injectable/injectable.dart';
import 'dart:convert';
import '../app_database.dart';
import '../tables/cache_table.dart';

@DriftAccessor(tables: [CacheTable])
class CacheDao extends DatabaseAccessor<AppDatabase> with _$CacheDaoMixin {
  CacheDao(super.db);

  /// 获取缓存值
  Future<T?> get<T>(String key, T Function(String) fromJson) async {
    final entry = await (select(cacheTable)..where((c) => c.key.equals(key))).getSingleOrNull();
    
    if (entry == null) return null;
    
    // 检查是否过期
    if (entry.expiresAt != null && DateTime.now().isAfter(entry.expiresAt!)) {
      await delete(cacheTable).where((c) => c.key.equals(key)).go();
      return null;
    }
    
    // 更新访问时间和次数
    await _updateAccessInfo(key);
    
    try {
      return fromJson(entry.value);
    } catch (e) {
      // 解析失败，删除缓存项
      await delete(cacheTable).where((c) => c.key.equals(key)).go();
      return null;
    }
  }

  /// 获取字符串缓存
  Future<String?> getString(String key) async {
    return get<String>(key, (value) => value);
  }

  /// 获取整数缓存
  Future<int?> getInt(String key) async {
    return get<int>(key, (value) => int.parse(value));
  }

  /// 获取布尔缓存
  Future<bool?> getBool(String key) async {
    return get<bool>(key, (value) => value.toLowerCase() == 'true');
  }

  /// 获取JSON对象缓存
  Future<Map<String, dynamic>?> getJson(String key) async {
    return get<Map<String, dynamic>>(key, (value) => jsonDecode(value));
  }

  /// 获取JSON数组缓存
  Future<List<dynamic>?> getJsonList(String key) async {
    return get<List<dynamic>>(key, (value) => jsonDecode(value));
  }

  /// 设置缓存值
  Future<int> set<T>(
    String key,
    T value,
    String Function(T) toJson, {
    Duration? duration,
    String type = 'string',
  }) {
    final now = DateTime.now();
    final expiresAt = duration != null ? now.add(duration) : null;
    
    return into(cacheTable).insertOnConflictUpdate(
      CacheTableCompanion(
        key: Value(key),
        value: Value(toJson(value)),
        type: Value(type),
        expiresAt: Value(expiresAt),
        createdAt: Value(now),
        accessedAt: Value(now),
        accessCount: const Value(0),
      ),
    );
  }

  /// 设置字符串缓存
  Future<int> setString(String key, String value, {Duration? duration}) {
    return set<String>(key, value, (v) => v, duration: duration, type: 'string');
  }

  /// 设置整数缓存
  Future<int> setInt(String key, int value, {Duration? duration}) {
    return set<int>(key, value, (v) => v.toString(), duration: duration, type: 'int');
  }

  /// 设置布尔缓存
  Future<int> setBool(String key, bool value, {Duration? duration}) {
    return set<bool>(key, value, (v) => v.toString(), duration: duration, type: 'bool');
  }

  /// 设置JSON对象缓存
  Future<int> setJson(String key, Map<String, dynamic> value, {Duration? duration}) {
    return set<Map<String, dynamic>>(key, value, (v) => jsonEncode(v), duration: duration, type: 'json');
  }

  /// 设置JSON数组缓存
  Future<int> setJsonList(String key, List<dynamic> value, {Duration? duration}) {
    return set<List<dynamic>>(key, value, (v) => jsonEncode(v), duration: duration, type: 'json_list');
  }

  /// 检查缓存是否存在且未过期
  Future<bool> exists(String key) async {
    final entry = await (select(cacheTable)..where((c) => c.key.equals(key))).getSingleOrNull();
    
    if (entry == null) return false;
    
    if (entry.expiresAt != null && DateTime.now().isAfter(entry.expiresAt!)) {
      await delete(cacheTable).where((c) => c.key.equals(key)).go();
      return false;
    }
    
    return true;
  }

  /// 删除缓存
  Future<bool> remove(String key) async {
    final rowsAffected = await (delete(cacheTable)..where((c) => c.key.equals(key))).go();
    return rowsAffected > 0;
  }

  /// 删除多个缓存
  Future<int> removeMultiple(List<String> keys) {
    return (delete(cacheTable)..where((c) => c.key.isIn(keys))).go();
  }

  /// 删除匹配模式的缓存
  Future<int> removePattern(String pattern) {
    return (delete(cacheTable)..where((c) => c.key.like(pattern))).go();
  }

  /// 清空所有缓存
  Future<int> clear() {
    return delete(cacheTable).go();
  }

  /// 清理过期缓存
  Future<int> cleanupExpired() {
    return (delete(cacheTable)
      ..where((c) => c.expiresAt.isNotNull() & c.expiresAt.isSmallerThanValue(DateTime.now()))
    ).go();
  }

  /// 清理最少使用的缓存（LRU）
  Future<int> cleanupLRU(int maxEntries) async {
    final count = await (selectOnly(cacheTable)..addColumns([cacheTable.key.count()])).getSingle();
    final totalEntries = count.read(cacheTable.key.count()) ?? 0;
    
    if (totalEntries <= maxEntries) return 0;
    
    final entriesToDelete = totalEntries - maxEntries;
    
    // 获取最少访问的条目
    final lruEntries = await (select(cacheTable)
      ..orderBy([
        (c) => OrderingTerm.asc(c.accessCount),
        (c) => OrderingTerm.asc(c.accessedAt),
      ])
      ..limit(entriesToDelete)
    ).get();
    
    final keysToDelete = lruEntries.map((e) => e.key).toList();
    return removeMultiple(keysToDelete);
  }

  /// 获取缓存统计信息
  Future<Map<String, dynamic>> getStats() async {
    final totalEntries = await (selectOnly(cacheTable)
      ..addColumns([cacheTable.key.count()])
    ).getSingle();
    
    final expiredEntries = await (selectOnly(cacheTable)
      ..addColumns([cacheTable.key.count()])
      ..where(cacheTable.expiresAt.isNotNull() & 
              cacheTable.expiresAt.isSmallerThanValue(DateTime.now()))
    ).getSingle();
    
    final totalSize = await customSelect(
      'SELECT SUM(LENGTH(value)) as total_size FROM cache_entries'
    ).getSingle();
    
    return {
      'total_entries': totalEntries.read(cacheTable.key.count()) ?? 0,
      'expired_entries': expiredEntries.read(cacheTable.key.count()) ?? 0,
      'total_size_bytes': totalSize.data['total_size'] ?? 0,
    };
  }

  /// 更新访问信息
  Future<void> _updateAccessInfo(String key) async {
    await (update(cacheTable)..where((c) => c.key.equals(key))).write(
      CacheTableCompanion(
        accessedAt: Value(DateTime.now()),
        accessCount: Value(cacheTable.accessCount + const Constant(1)),
      ),
    );
  }

  /// 监听缓存变化
  Stream<List<CacheTableData>> watchCache() {
    return select(cacheTable).watch();
  }

  /// 监听特定缓存项变化
  Stream<CacheTableData?> watchCacheEntry(String key) {
    return (select(cacheTable)..where((c) => c.key.equals(key))).watchSingleOrNull();
  }
}
```

## 3. 存储服务实现

### 安全存储服务
```dart
// packages/core/core_storage/lib/src/secure_storage_service.dart
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:injectable/injectable.dart';
import 'dart:convert';

abstract class SecureStorageService {
  Future<void> write(String key, String value);
  Future<String?> read(String key);
  Future<void> delete(String key);
  Future<void> deleteAll();
  Future<bool> containsKey(String key);
  Future<Map<String, String>> readAll();
  
  // 类型安全的方法
  Future<void> writeJson(String key, Map<String, dynamic> value);
  Future<Map<String, dynamic>?> readJson(String key);
  Future<void> writeBool(String key, bool value);
  Future<bool?> readBool(String key);
  Future<void> writeInt(String key, int value);
  Future<int?> readInt(String key);
}

@Injectable(as: SecureStorageService)
class SecureStorageServiceImpl implements SecureStorageService {
  SecureStorageServiceImpl() : _storage = const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      keyCipherAlgorithm: KeyCipherAlgorithm.RSA_ECB_OAEPwithSHA_256andMGF1Padding,
      storageCipherAlgorithm: StorageCipherAlgorithm.AES_GCM_NoPadding,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
      synchronizable: false,
    ),
    lOptions: LinuxOptions(
      useSessionKeyring: true,
    ),
    wOptions: WebOptions(
      dbName: 'FlutterSecureStorage',
      publicKey: 'FlutterSecureStorage',
    ),
    mOptions: MacOsOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
      synchronizable: false,
    ),
  );

  final FlutterSecureStorage _storage;

  @override
  Future<void> write(String key, String value) async {
    try {
      await _storage.write(key: key, value: value);
    } catch (e) {
      throw StorageException('Failed to write to secure storage: $e');
    }
  }

  @override
  Future<String?> read(String key) async {
    try {
      return await _storage.read(key: key);
    } catch (e) {
      throw StorageException('Failed to read from secure storage: $e');
    }
  }

  @override
  Future<void> delete(String key) async {
    try {
      await _storage.delete(key: key);
    } catch (e) {
      throw StorageException('Failed to delete from secure storage: $e');
    }
  }

  @override
  Future<void> deleteAll() async {
    try {
      await _storage.deleteAll();
    } catch (e) {
      throw StorageException('Failed to delete all from secure storage: $e');
    }
  }

  @override
  Future<bool> containsKey(String key) async {
    try {
      return await _storage.containsKey(key: key);
    } catch (e) {
      throw StorageException('Failed to check key in secure storage: $e');
    }
  }

  @override
  Future<Map<String, String>> readAll() async {
    try {
      return await _storage.readAll();
    } catch (e) {
      throw StorageException('Failed to read all from secure storage: $e');
    }
  }

  @override
  Future<void> writeJson(String key, Map<String, dynamic> value) async {
    await write(key, jsonEncode(value));
  }

  @override
  Future<Map<String, dynamic>?> readJson(String key) async {
    final value = await read(key);
    if (value == null) return null;
    
    try {
      return jsonDecode(value) as Map<String, dynamic>;
    } catch (e) {
      throw StorageException('Failed to parse JSON from secure storage: $e');
    }
  }

  @override
  Future<void> writeBool(String key, bool value) async {
    await write(key, value.toString());
  }

  @override
  Future<bool?> readBool(String key) async {
    final value = await read(key);
    if (value == null) return null;
    
    return value.toLowerCase() == 'true';
  }

  @override
  Future<void> writeInt(String key, int value) async {
    await write(key, value.toString());
  }

  @override
  Future<int?> readInt(String key) async {
    final value = await read(key);
    if (value == null) return null;
    
    try {
      return int.parse(value);
    } catch (e) {
      throw StorageException('Failed to parse int from secure storage: $e');
    }
  }
}

/// 存储异常
class StorageException implements Exception {
  const StorageException(this.message);
  
  final String message;
  
  @override
  String toString() => 'StorageException: $message';
}
```

### 缓存存储服务
```dart
// packages/core/core_storage/lib/src/cache_storage_service.dart
import 'package:injectable/injectable.dart';
import 'package:core_database/core_database.dart';

abstract class CacheStorageService {
  Future<T?> get<T>(String key, T Function(String) fromJson);
  Future<void> set<T>(String key, T value, String Function(T) toJson, {Duration? duration});
  Future<bool> exists(String key);
  Future<void> remove(String key);
  Future<void> clear();
  Future<void> cleanupExpired();
  Future<Map<String, dynamic>> getStats();
}

@Injectable(as: CacheStorageService)
class CacheStorageServiceImpl implements CacheStorageService {
  CacheStorageServiceImpl(this._cacheDao);

  final CacheDao _cacheDao;

  @override
  Future<T?> get<T>(String key, T Function(String) fromJson) {
    return _cacheDao.get<T>(key, fromJson);
  }

  @override
  Future<void> set<T>(String key, T value, String Function(T) toJson, {Duration? duration}) async {
    await _cacheDao.set<T>(key, value, toJson, duration: duration);
  }

  @override
  Future<bool> exists(String key) {
    return _cacheDao.exists(key);
  }

  @override
  Future<void> remove(String key) async {
    await _cacheDao.remove(key);
  }

  @override
  Future<void> clear() async {
    await _cacheDao.clear();
  }

  @override
  Future<void> cleanupExpired() async {
    await _cacheDao.cleanupExpired();
  }

  @override
  Future<Map<String, dynamic>> getStats() {
    return _cacheDao.getStats();
  }

  /// 便捷方法
  Future<String?> getString(String key) {
    return _cacheDao.getString(key);
  }

  Future<void> setString(String key, String value, {Duration? duration}) {
    return _cacheDao.setString(key, value, duration: duration);
  }

  Future<int?> getInt(String key) {
    return _cacheDao.getInt(key);
  }

  Future<void> setInt(String key, int value, {Duration? duration}) {
    return _cacheDao.setInt(key, value, duration: duration);
  }

  Future<bool?> getBool(String key) {
    return _cacheDao.getBool(key);
  }

  Future<void> setBool(String key, bool value, {Duration? duration}) {
    return _cacheDao.setBool(key, value, duration: duration);
  }

  Future<Map<String, dynamic>?> getJson(String key) {
    return _cacheDao.getJson(key);
  }

  Future<void> setJson(String key, Map<String, dynamic> value, {Duration? duration}) {
    return _cacheDao.setJson(key, value, duration: duration);
  }
}
```

这个数据持久化层实现提供了：

1. **完整的数据库设计**：使用Drift ORM，包含用户、认证、缓存、同步队列等表
2. **高效的DAO实现**：提供CRUD操作、搜索、统计、监听等功能
3. **安全存储服务**：使用FlutterSecureStorage保护敏感数据
4. **智能缓存系统**：支持过期时间、LRU清理、统计信息
5. **数据库优化**：WAL模式、外键约束、索引优化
6. **类型安全**：强类型的数据访问接口
7. **响应式编程**：支持数据变化监听
8. **错误处理**：完善的异常处理机制

所有实现都遵循Clean Architecture原则和依赖注入模式。