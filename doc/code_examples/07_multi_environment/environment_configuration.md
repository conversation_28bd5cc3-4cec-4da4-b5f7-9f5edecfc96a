# 多环境配置实现示例

## 1. 环境配置基础架构

### 环境枚举定义
```dart
// packages/core/core_config/lib/src/environment.dart
enum Environment {
  development('dev'),
  staging('staging'),
  production('prod');

  const Environment(this.value);
  
  final String value;
  
  static Environment fromString(String value) {
    return Environment.values.firstWhere(
      (env) => env.value == value,
      orElse: () => Environment.development,
    );
  }
  
  bool get isDevelopment => this == Environment.development;
  bool get isStaging => this == Environment.staging;
  bool get isProduction => this == Environment.production;
  bool get isDebug => isDevelopment || isStaging;
}
```

### 应用配置基类
```dart
// packages/core/core_config/lib/src/app_config.dart
import 'package:injectable/injectable.dart';
import 'environment.dart';

abstract class AppConfig {
  Environment get environment;
  String get appName;
  String get appVersion;
  String get buildNumber;
  
  // API配置
  String get apiBaseUrl;
  String get apiVersion;
  Duration get apiTimeout;
  int get apiRetryCount;
  
  // 认证配置
  String get authClientId;
  String get authClientSecret;
  Duration get tokenRefreshThreshold;
  
  // 数据库配置
  String get databaseName;
  int get databaseVersion;
  Duration get cacheTimeout;
  
  // 日志配置
  bool get enableLogging;
  String get logLevel;
  bool get enableCrashReporting;
  
  // 分析配置
  bool get enableAnalytics;
  String? get analyticsKey;
  
  // 功能开关
  bool get enableBiometrics;
  bool get enablePushNotifications;
  bool get enableOfflineMode;
  bool get enableDarkMode;
  
  // 第三方服务配置
  Map<String, String> get thirdPartyKeys;
  
  // 调试配置
  bool get showDebugBanner;
  bool get enableInspector;
  bool get enablePerformanceOverlay;
}

@Injectable(as: AppConfig)
class AppConfigImpl implements AppConfig {
  AppConfigImpl({
    required this.environment,
    required this.appName,
    required this.appVersion,
    required this.buildNumber,
    required this.apiBaseUrl,
    required this.apiVersion,
    required this.apiTimeout,
    required this.apiRetryCount,
    required this.authClientId,
    required this.authClientSecret,
    required this.tokenRefreshThreshold,
    required this.databaseName,
    required this.databaseVersion,
    required this.cacheTimeout,
    required this.enableLogging,
    required this.logLevel,
    required this.enableCrashReporting,
    required this.enableAnalytics,
    this.analyticsKey,
    required this.enableBiometrics,
    required this.enablePushNotifications,
    required this.enableOfflineMode,
    required this.enableDarkMode,
    required this.thirdPartyKeys,
    required this.showDebugBanner,
    required this.enableInspector,
    required this.enablePerformanceOverlay,
  });

  @override
  final Environment environment;
  
  @override
  final String appName;
  
  @override
  final String appVersion;
  
  @override
  final String buildNumber;
  
  @override
  final String apiBaseUrl;
  
  @override
  final String apiVersion;
  
  @override
  final Duration apiTimeout;
  
  @override
  final int apiRetryCount;
  
  @override
  final String authClientId;
  
  @override
  final String authClientSecret;
  
  @override
  final Duration tokenRefreshThreshold;
  
  @override
  final String databaseName;
  
  @override
  final int databaseVersion;
  
  @override
  final Duration cacheTimeout;
  
  @override
  final bool enableLogging;
  
  @override
  final String logLevel;
  
  @override
  final bool enableCrashReporting;
  
  @override
  final bool enableAnalytics;
  
  @override
  final String? analyticsKey;
  
  @override
  final bool enableBiometrics;
  
  @override
  final bool enablePushNotifications;
  
  
  @override
  final bool enableOfflineMode;
  
  @override
  final bool enableDarkMode;
  
  @override
  final Map<String, String> thirdPartyKeys;
  
  @override
  final bool showDebugBanner;
  
  @override
  final bool enableInspector;
  
  @override
  final bool enablePerformanceOverlay;
}
```

### 环境配置工厂
```dart
// packages/core/core_config/lib/src/config_factory.dart
import 'package:injectable/injectable.dart';
import 'app_config.dart';
import 'environment.dart';

@module
abstract class ConfigModule {
  @singleton
  AppConfig provideAppConfig() {
    final environment = _getCurrentEnvironment();
    
    switch (environment) {
      case Environment.development:
        return _createDevelopmentConfig();
      case Environment.staging:
        return _createStagingConfig();
      case Environment.production:
        return _createProductionConfig();
    }
  }
  
  Environment _getCurrentEnvironment() {
    // 从环境变量或编译时常量获取环境
    const envString = String.fromEnvironment('ENVIRONMENT', defaultValue: 'dev');
    return Environment.fromString(envString);
  }
  
  AppConfig _createDevelopmentConfig() {
    return AppConfigImpl(
      environment: Environment.development,
      appName: 'MyApp Dev',
      appVersion: '1.0.0',
      buildNumber: '1',
      
      // API配置
      apiBaseUrl: 'https://dev-api.myapp.com',
      apiVersion: 'v1',
      apiTimeout: const Duration(seconds: 30),
      apiRetryCount: 3,
      
      // 认证配置
      authClientId: 'dev_client_id',
      authClientSecret: 'dev_client_secret',
      tokenRefreshThreshold: const Duration(minutes: 5),
      
      // 数据库配置
      databaseName: 'myapp_dev.db',
      databaseVersion: 1,
      cacheTimeout: const Duration(hours: 1),
      
      // 日志配置
      enableLogging: true,
      logLevel: 'debug',
      enableCrashReporting: false,
      
      // 分析配置
      enableAnalytics: false,
      analyticsKey: null,
      
      // 功能开关
      enableBiometrics: true,
      enablePushNotifications: true,
      enableOfflineMode: true,
      enableDarkMode: true,
      
      // 第三方服务配置
      thirdPartyKeys: {
        'google_maps': 'dev_google_maps_key',
        'firebase': 'dev_firebase_key',
        'stripe': 'dev_stripe_key',
      },
      
      // 调试配置
      showDebugBanner: true,
      enableInspector: true,
      enablePerformanceOverlay: false,
    );
  }
  
  AppConfig _createStagingConfig() {
    return AppConfigImpl(
      environment: Environment.staging,
      appName: 'MyApp Staging',
      appVersion: '1.0.0',
      buildNumber: '1',
      
      // API配置
      apiBaseUrl: 'https://staging-api.myapp.com',
      apiVersion: 'v1',
      apiTimeout: const Duration(seconds: 30),
      apiRetryCount: 3,
      
      // 认证配置
      authClientId: 'staging_client_id',
      authClientSecret: 'staging_client_secret',
      tokenRefreshThreshold: const Duration(minutes: 5),
      
      // 数据库配置
      databaseName: 'myapp_staging.db',
      databaseVersion: 1,
      cacheTimeout: const Duration(hours: 2),
      
      // 日志配置
      enableLogging: true,
      logLevel: 'info',
      enableCrashReporting: true,
      
      // 分析配置
      enableAnalytics: true,
      analyticsKey: 'staging_analytics_key',
      
      // 功能开关
      enableBiometrics: true,
      enablePushNotifications: true,
      enableOfflineMode: true,
      enableDarkMode: true,
      
      // 第三方服务配置
      thirdPartyKeys: {
        'google_maps': 'staging_google_maps_key',
        'firebase': 'staging_firebase_key',
        'stripe': 'staging_stripe_key',
      },
      
      // 调试配置
      showDebugBanner: false,
      enableInspector: false,
      enablePerformanceOverlay: false,
    );
  }
  
  AppConfig _createProductionConfig() {
    return AppConfigImpl(
      environment: Environment.production,
      appName: 'MyApp',
      appVersion: '1.0.0',
      buildNumber: '1',
      
      // API配置
      apiBaseUrl: 'https://api.myapp.com',
      apiVersion: 'v1',
      apiTimeout: const Duration(seconds: 30),
      apiRetryCount: 3,
      
      // 认证配置
      authClientId: 'prod_client_id',
      authClientSecret: 'prod_client_secret',
      tokenRefreshThreshold: const Duration(minutes: 5),
      
      // 数据库配置
      databaseName: 'myapp.db',
      databaseVersion: 1,
      cacheTimeout: const Duration(hours: 4),
      
      // 日志配置
      enableLogging: false,
      logLevel: 'error',
      enableCrashReporting: true,
      
      // 分析配置
      enableAnalytics: true,
      analyticsKey: 'prod_analytics_key',
      
      // 功能开关
      enableBiometrics: true,
      enablePushNotifications: true,
      enableOfflineMode: true,
      enableDarkMode: true,
      
      // 第三方服务配置
      thirdPartyKeys: {
        'google_maps': 'prod_google_maps_key',
        'firebase': 'prod_firebase_key',
        'stripe': 'prod_stripe_key',
      },
      
      // 调试配置
      showDebugBanner: false,
      enableInspector: false,
      enablePerformanceOverlay: false,
    );
  }
}
```

## 2. 功能开关管理

### 功能开关服务
```dart
// packages/core/core_config/lib/src/feature_flags_service.dart
import 'package:injectable/injectable.dart';
import 'package:core_storage/core_storage.dart';
import 'app_config.dart';

abstract class FeatureFlagsService {
  Future<bool> isEnabled(String feature);
  Future<void> setEnabled(String feature, bool enabled);
  Future<void> reset();
  Future<Map<String, bool>> getAllFlags();
  Stream<Map<String, bool>> watchFlags();
}

@Injectable(as: FeatureFlagsService)
class FeatureFlagsServiceImpl implements FeatureFlagsService {
  FeatureFlagsServiceImpl(
    this._appConfig,
    this._cacheStorage,
  );

  final AppConfig _appConfig;
  final CacheStorageService _cacheStorage;
  
  static const String _flagsKey = 'feature_flags';
  
  // 默认功能开关
  static const Map<String, bool> _defaultFlags = {
    'biometrics': true,
    'push_notifications': true,
    'offline_mode': true,
    'dark_mode': true,
    'analytics': false,
    'crash_reporting': false,
    'new_ui': false,
    'beta_features': false,
    'advanced_search': false,
    'social_login': true,
    'file_upload': true,
    'video_call': false,
    'live_chat': true,
    'notifications_sound': true,
    'auto_backup': false,
  };

  @override
  Future<bool> isEnabled(String feature) async {
    final flags = await _getFlags();
    
    // 检查环境特定的覆盖
    final envOverride = _getEnvironmentOverride(feature);
    if (envOverride != null) {
      return envOverride;
    }
    
    return flags[feature] ?? _defaultFlags[feature] ?? false;
  }

  @override
  Future<void> setEnabled(String feature, bool enabled) async {
    final flags = await _getFlags();
    flags[feature] = enabled;
    await _saveFlags(flags);
  }

  @override
  Future<void> reset() async {
    await _cacheStorage.remove(_flagsKey);
  }

  @override
  Future<Map<String, bool>> getAllFlags() async {
    final flags = await _getFlags();
    final result = <String, bool>{};
    
    // 合并默认标志和用户设置
    for (final entry in _defaultFlags.entries) {
      final envOverride = _getEnvironmentOverride(entry.key);
      if (envOverride != null) {
        result[entry.key] = envOverride;
      } else {
        result[entry.key] = flags[entry.key] ?? entry.value;
      }
    }
    
    // 添加用户自定义标志
    for (final entry in flags.entries) {
      if (!_defaultFlags.containsKey(entry.key)) {
        result[entry.key] = entry.value;
      }
    }
    
    return result;
  }

  @override
  Stream<Map<String, bool>> watchFlags() async* {
    yield await getAllFlags();
    
    // 这里可以实现实时监听，比如通过WebSocket或定期轮询
    // 简化实现，只返回当前状态
  }

  Future<Map<String, bool>> _getFlags() async {
    final flagsJson = await _cacheStorage.getJson(_flagsKey);
    if (flagsJson == null) return <String, bool>{};
    
    return flagsJson.map((key, value) => MapEntry(key, value as bool));
  }

  Future<void> _saveFlags(Map<String, bool> flags) async {
    await _cacheStorage.setJson(_flagsKey, flags);
  }

  bool? _getEnvironmentOverride(String feature) {
    // 环境特定的功能开关覆盖
    switch (_appConfig.environment) {
      case Environment.development:
        return _getDevelopmentOverride(feature);
      case Environment.staging:
        return _getStagingOverride(feature);
      case Environment.production:
        return _getProductionOverride(feature);
    }
  }

  bool? _getDevelopmentOverride(String feature) {
    switch (feature) {
      case 'analytics':
        return false; // 开发环境禁用分析
      case 'crash_reporting':
        return false; // 开发环境禁用崩溃报告
      case 'beta_features':
        return true; // 开发环境启用测试功能
      default:
        return null;
    }
  }

  bool? _getStagingOverride(String feature) {
    switch (feature) {
      case 'analytics':
        return true; // 测试环境启用分析
      case 'crash_reporting':
        return true; // 测试环境启用崩溃报告
      case 'beta_features':
        return true; // 测试环境启用测试功能
      default:
        return null;
    }
  }

  bool? _getProductionOverride(String feature) {
    switch (feature) {
      case 'beta_features':
        return false; // 生产环境禁用测试功能
      default:
        return null;
    }
  }
}
```

### 远程配置服务
```dart
// packages/core/core_config/lib/src/remote_config_service.dart
import 'package:injectable/injectable.dart';
import 'package:core_network/core_network.dart';
import 'package:core_storage/core_storage.dart';
import 'app_config.dart';

abstract class RemoteConfigService {
  Future<void> initialize();
  Future<void> fetchAndActivate();
  Future<T?> getValue<T>(String key, T Function(dynamic) parser);
  Future<String?> getString(String key);
  Future<int?> getInt(String key);
  Future<bool?> getBool(String key);
  Future<double?> getDouble(String key);
  Future<Map<String, dynamic>?> getJson(String key);
  Future<Map<String, dynamic>> getAllValues();
}

@Injectable(as: RemoteConfigService)
class RemoteConfigServiceImpl implements RemoteConfigService {
  RemoteConfigServiceImpl(
    this._appConfig,
    this._apiService,
    this._cacheStorage,
  );

  final AppConfig _appConfig;
  final ApiService _apiService;
  final CacheStorageService _cacheStorage;
  
  static const String _configCacheKey = 'remote_config';
  static const Duration _cacheTimeout = Duration(hours: 1);
  
  Map<String, dynamic>? _cachedConfig;
  DateTime? _lastFetchTime;

  @override
  Future<void> initialize() async {
    // 从本地缓存加载配置
    _cachedConfig = await _cacheStorage.getJson(_configCacheKey);
    
    // 如果没有缓存或缓存过期，尝试获取远程配置
    if (_cachedConfig == null || _isCacheExpired()) {
      try {
        await fetchAndActivate();
      } catch (e) {
        // 如果获取远程配置失败，使用默认配置
        _cachedConfig ??= _getDefaultConfig();
      }
    }
  }

  @override
  Future<void> fetchAndActivate() async {
    try {
      final response = await _apiService.get<Map<String, dynamic>>(
        '/config',
        queryParameters: {
          'environment': _appConfig.environment.value,
          'version': _appConfig.appVersion,
          'platform': _getPlatform(),
        },
        fromJson: (json) => json as Map<String, dynamic>,
      );
      
      if (response.isSuccess && response.data != null) {
        _cachedConfig = response.data!;
        _lastFetchTime = DateTime.now();
        
        // 保存到本地缓存
        await _cacheStorage.setJson(
          _configCacheKey,
          _cachedConfig!,
          duration: _cacheTimeout,
        );
      }
    } catch (e) {
      // 获取远程配置失败，继续使用缓存的配置
      rethrow;
    }
  }

  @override
  Future<T?> getValue<T>(String key, T Function(dynamic) parser) async {
    await _ensureInitialized();
    
    final value = _cachedConfig?[key];
    if (value == null) return null;
    
    try {
      return parser(value);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<String?> getString(String key) async {
    return getValue<String>(key, (value) => value.toString());
  }

  @override
  Future<int?> getInt(String key) async {
    return getValue<int>(key, (value) {
      if (value is int) return value;
      if (value is String) return int.parse(value);
      if (value is double) return value.toInt();
      throw FormatException('Cannot parse $value as int');
    });
  }

  @override
  Future<bool?> getBool(String key) async {
    return getValue<bool>(key, (value) {
      if (value is bool) return value;
      if (value is String) return value.toLowerCase() == 'true';
      if (value is int) return value != 0;
      throw FormatException('Cannot parse $value as bool');
    });
  }

  @override
  Future<double?> getDouble(String key) async {
    return getValue<double>(key, (value) {
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) return double.parse(value);
      throw FormatException('Cannot parse $value as double');
    });
  }

  @override
  Future<Map<String, dynamic>?> getJson(String key) async {
    return getValue<Map<String, dynamic>>(key, (value) {
      if (value is Map<String, dynamic>) return value;
      throw FormatException('Cannot parse $value as JSON object');
    });
  }

  @override
  Future<Map<String, dynamic>> getAllValues() async {
    await _ensureInitialized();
    return Map<String, dynamic>.from(_cachedConfig ?? {});
  }

  Future<void> _ensureInitialized() async {
    if (_cachedConfig == null) {
      await initialize();
    }
  }

  bool _isCacheExpired() {
    if (_lastFetchTime == null) return true;
    return DateTime.now().difference(_lastFetchTime!) > _cacheTimeout;
  }

  String _getPlatform() {
    // 这里可以使用 dart:io 或 universal_platform 来检测平台
    return 'flutter'; // 简化实现
  }

  Map<String, dynamic> _getDefaultConfig() {
    return {
      'api_timeout': 30000,
      'retry_count': 3,
      'cache_timeout': 3600000,
      'enable_analytics': _appConfig.environment.isProduction,
      'enable_crash_reporting': !_appConfig.environment.isDevelopment,
      'max_file_size': 10485760, // 10MB
      'supported_file_types': ['jpg', 'jpeg', 'png', 'gif', 'pdf'],
      'pagination_size': 20,
      'search_debounce': 500,
      'theme_colors': {
        'primary': '#2196F3',
        'secondary': '#FF9800',
        'error': '#F44336',
        'warning': '#FF5722',
        'success': '#4CAF50',
      },
      'feature_flags': {
        'new_ui': false,
        'beta_features': _appConfig.environment.isDevelopment,
        'advanced_search': true,
        'social_login': true,
      },
    };
  }
}
```

## 3. 环境切换工具

### 环境切换器
```dart
// packages/core/core_config/lib/src/environment_switcher.dart
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:core_storage/core_storage.dart';
import 'environment.dart';
import 'app_config.dart';

abstract class EnvironmentSwitcher {
  Future<Environment> getCurrentEnvironment();
  Future<void> switchEnvironment(Environment environment);
  Future<List<Environment>> getAvailableEnvironments();
  Stream<Environment> watchEnvironment();
}

@Injectable(as: EnvironmentSwitcher)
class EnvironmentSwitcherImpl implements EnvironmentSwitcher {
  EnvironmentSwitcherImpl(
    this._secureStorage,
    this._appConfig,
  );

  final SecureStorageService _secureStorage;
  final AppConfig _appConfig;
  
  static const String _environmentKey = 'selected_environment';

  @override
  Future<Environment> getCurrentEnvironment() async {
    final envString = await _secureStorage.read(_environmentKey);
    if (envString != null) {
      return Environment.fromString(envString);
    }
    return _appConfig.environment;
  }

  @override
  Future<void> switchEnvironment(Environment environment) async {
    await _secureStorage.write(_environmentKey, environment.value);
    
    // 这里可以触发应用重启或重新初始化
    // 在实际应用中，可能需要重启应用来应用新的环境配置
  }

  @override
  Future<List<Environment>> getAvailableEnvironments() async {
    // 在生产环境中，可能只允许切换到生产环境
    if (_appConfig.environment.isProduction) {
      return [Environment.production];
    }
    
    // 在开发和测试环境中，允许切换到所有环境
    return Environment.values;
  }

  @override
  Stream<Environment> watchEnvironment() async* {
    yield await getCurrentEnvironment();
    
    // 这里可以实现监听存储变化
    // 简化实现，只返回当前环境
  }
}

/// 环境切换对话框
class EnvironmentSwitcherDialog extends StatefulWidget {
  const EnvironmentSwitcherDialog({
    super.key,
    required this.environmentSwitcher,
    required this.onEnvironmentChanged,
  });

  final EnvironmentSwitcher environmentSwitcher;
  final VoidCallback onEnvironmentChanged;

  @override
  State<EnvironmentSwitcherDialog> createState() => _EnvironmentSwitcherDialogState();
}

class _EnvironmentSwitcherDialogState extends State<EnvironmentSwitcherDialog> {
  Environment? _currentEnvironment;
  List<Environment> _availableEnvironments = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadEnvironments();
  }

  Future<void> _loadEnvironments() async {
    try {
      final current = await widget.environmentSwitcher.getCurrentEnvironment();
      final available = await widget.environmentSwitcher.getAvailableEnvironments();
      
      setState(() {
        _currentEnvironment = current;
        _availableEnvironments = available;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _switchEnvironment(Environment environment) async {
    try {
      await widget.environmentSwitcher.switchEnvironment(environment);
      
      setState(() {
        _currentEnvironment = environment;
      });
      
      widget.onEnvironmentChanged();
      
      if (mounted) {
        Navigator.of(context).pop();
        
        // 显示重启提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('环境已切换到 ${_getEnvironmentDisplayName(environment)}，请重启应用'),
            action: SnackBarAction(
              label: '重启',
              onPressed: () {
                // 这里可以实现应用重启逻辑
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('切换环境失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _getEnvironmentDisplayName(Environment environment) {
    switch (environment) {
      case Environment.development:
        return '开发环境';
      case Environment.staging:
        return '测试环境';
      case Environment.production:
        return '生产环境';
    }
  }

  Color _getEnvironmentColor(Environment environment) {
    switch (environment) {
      case Environment.development:
        return Colors.green;
      case Environment.staging:
        return Colors.orange;
      case Environment.production:
        return Colors.red;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('切换环境'),
      content: _isLoading
          ? const SizedBox(
              height: 100,
              child: Center(
                child: CircularProgressIndicator(),
              ),
            )
          : Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '当前环境: ${_currentEnvironment != null ? _getEnvironmentDisplayName(_currentEnvironment!) : "未知"}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 16),
                ..._availableEnvironments.map(
                  (environment) => ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getEnvironmentColor(environment),
                      child: Text(
                        environment.value.substring(0, 1).toUpperCase(),
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                    title: Text(_getEnvironmentDisplayName(environment)),
                    subtitle: Text(environment.value),
                    trailing: _currentEnvironment == environment
                        ? const Icon(Icons.check, color: Colors.green)
                        : null,
                    onTap: _currentEnvironment != environment
                        ? () => _switchEnvironment(environment)
                        : null,
                  ),
                ),
              ],
            ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
      ],
    );
  }
}
```

## 4. 构建配置

### Dart定义文件
```dart
// lib/main_dev.dart
import 'package:flutter/material.dart';
import 'package:core_config/core_config.dart';
import 'main_common.dart';

void main() {
  runAppWithEnvironment(Environment.development);
}
```

```dart
// lib/main_staging.dart
import 'package:flutter/material.dart';
import 'package:core_config/core_config.dart';
import 'main_common.dart';

void main() {
  runAppWithEnvironment(Environment.staging);
}
```

```dart
// lib/main_prod.dart
import 'package:flutter/material.dart';
import 'package:core_config/core_config.dart';
import 'main_common.dart';

void main() {
  runAppWithEnvironment(Environment.production);
}
```

```dart
// lib/main_common.dart
import 'package:flutter/material.dart';
import 'package:core_config/core_config.dart';
import 'package:core_di/core_di.dart';
import 'app.dart';

void runAppWithEnvironment(Environment environment) async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 配置依赖注入
  await configureDependencies(environment);
  
  // 获取应用配置
  final appConfig = getIt<AppConfig>();
  
  // 运行应用
  runApp(MyApp(config: appConfig));
}
```

### 构建脚本
```yaml
# build_scripts/build_config.yaml
configurations:
  development:
    entry_point: lib/main_dev.dart
    build_name: "1.0.0-dev"
    build_number: "1"
    dart_defines:
      ENVIRONMENT: "dev"
      API_BASE_URL: "https://dev-api.myapp.com"
      ENABLE_LOGGING: "true"
      ENABLE_ANALYTICS: "false"
    
  staging:
    entry_point: lib/main_staging.dart
    build_name: "1.0.0-staging"
    build_number: "1"
    dart_defines:
      ENVIRONMENT: "staging"
      API_BASE_URL: "https://staging-api.myapp.com"
      ENABLE_LOGGING: "true"
      ENABLE_ANALYTICS: "true"
    
  production:
    entry_point: lib/main_prod.dart
    build_name: "1.0.0"
    build_number: "1"
    dart_defines:
      ENVIRONMENT: "prod"
      API_BASE_URL: "https://api.myapp.com"
      ENABLE_LOGGING: "false"
      ENABLE_ANALYTICS: "true"
```

```bash
#!/bin/bash
# build_scripts/build.sh

set -e

# 默认配置
CONFIG="development"
PLATFORM="android"
BUILD_TYPE="apk"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --config)
      CONFIG="$2"
      shift 2
      ;;
    --platform)
      PLATFORM="$2"
      shift 2
      ;;
    --type)
      BUILD_TYPE="$2"
      shift 2
      ;;
    *)
      echo "未知参数: $1"
      exit 1
      ;;
  esac
done

echo "构建配置: $CONFIG"
echo "平台: $PLATFORM"
echo "构建类型: $BUILD_TYPE"

# 读取配置
case $CONFIG in
  "development")
    ENTRY_POINT="lib/main_dev.dart"
    BUILD_NAME="1.0.0-dev"
    BUILD_NUMBER="1"
    DART_DEFINES="--dart-define=ENVIRONMENT=dev --dart-define=API_BASE_URL=https://dev-api.myapp.com --dart-define=ENABLE_LOGGING=true --dart-define=ENABLE_ANALYTICS=false"
    ;;
  "staging")
    ENTRY_POINT="lib/main_staging.dart"
    BUILD_NAME="1.0.0-staging"
    BUILD_NUMBER="1"
    DART_DEFINES="--dart-define=ENVIRONMENT=staging --dart-define=API_BASE_URL=https://staging-api.myapp.com --dart-define=ENABLE_LOGGING=true --dart-define=ENABLE_ANALYTICS=true"
    ;;
  "production")
    ENTRY_POINT="lib/main_prod.dart"
    BUILD_NAME="1.0.0"
    BUILD_NUMBER="1"
    DART_DEFINES="--dart-define=ENVIRONMENT=prod --dart-define=API_BASE_URL=https://api.myapp.com --dart-define=ENABLE_LOGGING=false --dart-define=ENABLE_ANALYTICS=true"
    ;;
  *)
    echo "不支持的配置: $CONFIG"
    exit 1
    ;;
esac

# 清理
flutter clean
flutter pub get

# 构建
case $PLATFORM in
  "android")
    case $BUILD_TYPE in
      "apk")
        flutter build apk \
          --target="$ENTRY_POINT" \
          --build-name="$BUILD_NAME" \
          --build-number="$BUILD_NUMBER" \
          $DART_DEFINES
        ;;
      "appbundle")
        flutter build appbundle \
          --target="$ENTRY_POINT" \
          --build-name="$BUILD_NAME" \
          --build-number="$BUILD_NUMBER" \
          $DART_DEFINES
        ;;
      *)
        echo "不支持的Android构建类型: $BUILD_TYPE"
        exit 1
        ;;
    esac
    ;;
  "ios")
    flutter build ios \
      --target="$ENTRY_POINT" \
      --build-name="$BUILD_NAME" \
      --build-number="$BUILD_NUMBER" \
      $DART_DEFINES
    ;;
  "web")
    flutter build web \
      --target="$ENTRY_POINT" \
      --build-name="$BUILD_NAME" \
      --build-number="$BUILD_NUMBER" \
      $DART_DEFINES
    ;;
  *)
    echo "不支持的平台: $PLATFORM"
    exit 1
    ;;
esac

echo "构建完成!"
```

### 使用示例
```bash
# 构建开发版本
./build_scripts/build.sh --config development --platform android --type apk

# 构建测试版本
./build_scripts/build.sh --config staging --platform android --type appbundle

# 构建生产版本
./build_scripts/build.sh --config production --platform ios

# 构建Web版本
./build_scripts/build.sh --config production --platform web
```

这个多环境配置实现提供了：

1. **完整的环境管理**：开发、测试、生产环境的完整配置
2. **功能开关系统**：灵活的功能开关管理和环境特定覆盖
3. **远程配置服务**：支持从服务器获取配置并本地缓存
4. **环境切换工具**：开发时可以动态切换环境
5. **构建自动化**：自动化的多环境构建脚本
6. **类型安全**：强类型的配置访问接口
7. **缓存机制**：配置的本地缓存和过期管理
8. **错误处理**：完善的错误处理和降级机制

所有实现都遵循Clean Architecture原则和依赖注入模式。