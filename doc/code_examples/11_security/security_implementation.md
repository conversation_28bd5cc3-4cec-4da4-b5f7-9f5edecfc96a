# Flutter 企业级应用 - 安全和权限管理实现

本文档展示了 Flutter 企业级应用中安全和权限管理的详细实现示例，包括认证安全、数据加密、权限控制、安全存储、网络安全等核心功能。

## 1. 认证安全

### JWT Token 管理
```dart
// packages/core/core_security/lib/src/auth/jwt_manager.dart
import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:injectable/injectable.dart';

/// JWT Token 管理器
@singleton
class JwtManager {
  static const String _issuer = 'flutter_app';
  static const Duration _defaultExpiry = Duration(hours: 24);
  static const Duration _refreshThreshold = Duration(minutes: 15);

  /// 生成 JWT Token
  String generateToken({
    required String userId,
    required Map<String, dynamic> claims,
    Duration? expiry,
    String? secret,
  }) {
    final now = DateTime.now();
    final exp = now.add(expiry ?? _defaultExpiry);
    
    final header = {
      'alg': 'HS256',
      'typ': 'JWT',
    };
    
    final payload = {
      'iss': _issuer,
      'sub': userId,
      'iat': now.millisecondsSinceEpoch ~/ 1000,
      'exp': exp.millisecondsSinceEpoch ~/ 1000,
      'jti': _generateJti(),
      ...claims,
    };
    
    final headerEncoded = _base64UrlEncode(utf8.encode(jsonEncode(header)));
    final payloadEncoded = _base64UrlEncode(utf8.encode(jsonEncode(payload)));
    
    final signature = _generateSignature(
      '$headerEncoded.$payloadEncoded',
      secret ?? _getDefaultSecret(),
    );
    
    return '$headerEncoded.$payloadEncoded.$signature';
  }
  
  /// 验证 JWT Token
  JwtValidationResult validateToken(String token, {String? secret}) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) {
        return JwtValidationResult.invalid('Invalid token format');
      }
      
      final header = jsonDecode(utf8.decode(_base64UrlDecode(parts[0])));
      final payload = jsonDecode(utf8.decode(_base64UrlDecode(parts[1])));
      final signature = parts[2];
      
      // 验证签名
      final expectedSignature = _generateSignature(
        '${parts[0]}.${parts[1]}',
        secret ?? _getDefaultSecret(),
      );
      
      if (signature != expectedSignature) {
        return JwtValidationResult.invalid('Invalid signature');
      }
      
      // 验证过期时间
      final exp = payload['exp'] as int;
      final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      
      if (exp < now) {
        return JwtValidationResult.expired();
      }
      
      // 检查是否需要刷新
      final needsRefresh = (exp - now) < _refreshThreshold.inSeconds;
      
      return JwtValidationResult.valid(
        payload: payload,
        needsRefresh: needsRefresh,
      );
    } catch (e) {
      return JwtValidationResult.invalid('Token parsing error: $e');
    }
  }
  
  /// 刷新 Token
  String? refreshToken(String token, {String? secret}) {
    final validation = validateToken(token, secret: secret);
    
    if (validation.isValid || validation.isExpired) {
      final payload = validation.payload;
      if (payload != null) {
        return generateToken(
          userId: payload['sub'],
          claims: Map<String, dynamic>.from(payload)
            ..removeWhere((key, value) => 
              ['iss', 'sub', 'iat', 'exp', 'jti'].contains(key)),
        );
      }
    }
    
    return null;
  }
  
  /// 提取用户信息
  Map<String, dynamic>? extractUserInfo(String token) {
    final validation = validateToken(token);
    return validation.isValid ? validation.payload : null;
  }
  
  String _generateSignature(String data, String secret) {
    final key = utf8.encode(secret);
    final bytes = utf8.encode(data);
    final hmac = Hmac(sha256, key);
    final digest = hmac.convert(bytes);
    return _base64UrlEncode(digest.bytes);
  }
  
  String _base64UrlEncode(List<int> bytes) {
    return base64Url.encode(bytes).replaceAll('=', '');
  }
  
  Uint8List _base64UrlDecode(String str) {
    String normalized = str.replaceAll('-', '+').replaceAll('_', '/');
    while (normalized.length % 4 != 0) {
      normalized += '=';
    }
    return base64.decode(normalized);
  }
  
  String _generateJti() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = List.generate(8, (i) => timestamp.hashCode + i);
    return sha256.convert(random).toString().substring(0, 16);
  }
  
  String _getDefaultSecret() {
    // 在实际应用中，这应该从安全的配置中获取
    return 'your-secret-key-should-be-very-long-and-secure';
  }
}

/// JWT 验证结果
class JwtValidationResult {
  final bool isValid;
  final bool isExpired;
  final String? error;
  final Map<String, dynamic>? payload;
  final bool needsRefresh;
  
  const JwtValidationResult._(
    this.isValid,
    this.isExpired,
    this.error,
    this.payload,
    this.needsRefresh,
  );
  
  factory JwtValidationResult.valid({
    required Map<String, dynamic> payload,
    bool needsRefresh = false,
  }) {
    return JwtValidationResult._(true, false, null, payload, needsRefresh);
  }
  
  factory JwtValidationResult.expired() {
    return JwtValidationResult._(false, true, 'Token expired', null, false);
  }
  
  factory JwtValidationResult.invalid(String error) {
    return JwtValidationResult._(false, false, error, null, false);
  }
}
```

### 生物识别认证
```dart
// packages/core/core_security/lib/src/auth/biometric_auth.dart
import 'package:local_auth/local_auth.dart';
import 'package:injectable/injectable.dart';

/// 生物识别认证服务
@singleton
class BiometricAuthService {
  final LocalAuthentication _localAuth = LocalAuthentication();
  
  /// 检查生物识别是否可用
  Future<BiometricAvailability> checkAvailability() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      if (!isAvailable) {
        return BiometricAvailability.notAvailable;
      }
      
      final availableBiometrics = await _localAuth.getAvailableBiometrics();
      if (availableBiometrics.isEmpty) {
        return BiometricAvailability.notEnrolled;
      }
      
      return BiometricAvailability.available;
    } catch (e) {
      return BiometricAvailability.error;
    }
  }
  
  /// 获取可用的生物识别类型
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      final biometrics = await _localAuth.getAvailableBiometrics();
      return biometrics;
    } catch (e) {
      return [];
    }
  }
  
  /// 执行生物识别认证
  Future<BiometricAuthResult> authenticate({
    String? localizedFallbackTitle,
    String? localizedReason,
    bool biometricOnly = false,
    bool stickyAuth = false,
  }) async {
    try {
      final availability = await checkAvailability();
      if (availability != BiometricAvailability.available) {
        return BiometricAuthResult.unavailable(availability);
      }
      
      final isAuthenticated = await _localAuth.authenticate(
        localizedReason: localizedReason ?? 'Please authenticate to continue',
        options: AuthenticationOptions(
          biometricOnly: biometricOnly,
          stickyAuth: stickyAuth,
        ),
      );
      
      if (isAuthenticated) {
        return BiometricAuthResult.success();
      } else {
        return BiometricAuthResult.cancelled();
      }
    } catch (e) {
      return BiometricAuthResult.error(e.toString());
    }
  }
  
  /// 停止认证
  Future<void> stopAuthentication() async {
    try {
      await _localAuth.stopAuthentication();
    } catch (e) {
      // 忽略停止认证的错误
    }
  }
}

/// 生物识别可用性
enum BiometricAvailability {
  available,
  notAvailable,
  notEnrolled,
  error,
}

/// 生物识别认证结果
class BiometricAuthResult {
  final bool isSuccess;
  final bool isCancelled;
  final String? error;
  final BiometricAvailability? availability;
  
  const BiometricAuthResult._(
    this.isSuccess,
    this.isCancelled,
    this.error,
    this.availability,
  );
  
  factory BiometricAuthResult.success() {
    return BiometricAuthResult._(true, false, null, null);
  }
  
  factory BiometricAuthResult.cancelled() {
    return BiometricAuthResult._(false, true, null, null);
  }
  
  factory BiometricAuthResult.error(String error) {
    return BiometricAuthResult._(false, false, error, null);
  }
  
  factory BiometricAuthResult.unavailable(BiometricAvailability availability) {
    return BiometricAuthResult._(false, false, null, availability);
  }
}
```

## 2. 数据加密

### 加密服务
```dart
// packages/core/core_security/lib/src/encryption/encryption_service.dart
import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:injectable/injectable.dart';

/// 数据加密服务
@singleton
class EncryptionService {
  late final Encrypter _aesEncrypter;
  late final Encrypter _rsaEncrypter;
  late final Key _aesKey;
  late final RSAKeyPair _rsaKeyPair;
  
  EncryptionService() {
    _initializeEncryption();
  }
  
  void _initializeEncryption() {
    // 初始化 AES 加密
    _aesKey = Key.fromSecureRandom(32);
    _aesEncrypter = Encrypter(AES(_aesKey));
    
    // 初始化 RSA 加密
    _rsaKeyPair = RSAKeyPair.fromRandom();
    _rsaEncrypter = Encrypter(RSA(publicKey: _rsaKeyPair.publicKey));
  }
  
  /// AES 加密
  EncryptionResult encryptAES(String plainText, {String? customKey}) {
    try {
      final key = customKey != null ? Key.fromBase64(customKey) : _aesKey;
      final encrypter = Encrypter(AES(key));
      final iv = IV.fromSecureRandom(16);
      
      final encrypted = encrypter.encrypt(plainText, iv: iv);
      
      return EncryptionResult.success(
        encryptedData: encrypted.base64,
        iv: iv.base64,
        key: key.base64,
      );
    } catch (e) {
      return EncryptionResult.error('AES encryption failed: $e');
    }
  }
  
  /// AES 解密
  DecryptionResult decryptAES(
    String encryptedData,
    String ivString,
    {String? customKey}
  ) {
    try {
      final key = customKey != null ? Key.fromBase64(customKey) : _aesKey;
      final encrypter = Encrypter(AES(key));
      final iv = IV.fromBase64(ivString);
      final encrypted = Encrypted.fromBase64(encryptedData);
      
      final decrypted = encrypter.decrypt(encrypted, iv: iv);
      
      return DecryptionResult.success(decrypted);
    } catch (e) {
      return DecryptionResult.error('AES decryption failed: $e');
    }
  }
  
  /// RSA 加密
  EncryptionResult encryptRSA(String plainText, {RSAPublicKey? publicKey}) {
    try {
      final key = publicKey ?? _rsaKeyPair.publicKey;
      final encrypter = Encrypter(RSA(publicKey: key));
      
      final encrypted = encrypter.encrypt(plainText);
      
      return EncryptionResult.success(
        encryptedData: encrypted.base64,
      );
    } catch (e) {
      return EncryptionResult.error('RSA encryption failed: $e');
    }
  }
  
  /// RSA 解密
  DecryptionResult decryptRSA(String encryptedData, {RSAPrivateKey? privateKey}) {
    try {
      final key = privateKey ?? _rsaKeyPair.privateKey;
      final encrypter = Encrypter(RSA(privateKey: key));
      final encrypted = Encrypted.fromBase64(encryptedData);
      
      final decrypted = encrypter.decrypt(encrypted);
      
      return DecryptionResult.success(decrypted);
    } catch (e) {
      return DecryptionResult.error('RSA decryption failed: $e');
    }
  }
  
  /// 生成哈希
  String generateHash(String input, {HashAlgorithm algorithm = HashAlgorithm.sha256}) {
    final bytes = utf8.encode(input);
    
    switch (algorithm) {
      case HashAlgorithm.md5:
        return md5.convert(bytes).toString();
      case HashAlgorithm.sha1:
        return sha1.convert(bytes).toString();
      case HashAlgorithm.sha256:
        return sha256.convert(bytes).toString();
      case HashAlgorithm.sha512:
        return sha512.convert(bytes).toString();
    }
  }
  
  /// 生成加盐哈希
  SaltedHashResult generateSaltedHash(String input, {String? customSalt}) {
    final salt = customSalt ?? _generateSalt();
    final saltedInput = input + salt;
    final hash = generateHash(saltedInput);
    
    return SaltedHashResult(
      hash: hash,
      salt: salt,
    );
  }
  
  /// 验证加盐哈希
  bool verifySaltedHash(String input, String hash, String salt) {
    final saltedInput = input + salt;
    final computedHash = generateHash(saltedInput);
    return computedHash == hash;
  }
  
  /// 生成随机密钥
  String generateRandomKey({int length = 32}) {
    final random = Random.secure();
    final bytes = List<int>.generate(length, (i) => random.nextInt(256));
    return base64.encode(bytes);
  }
  
  /// 生成随机盐值
  String _generateSalt({int length = 16}) {
    final random = Random.secure();
    final bytes = List<int>.generate(length, (i) => random.nextInt(256));
    return base64.encode(bytes);
  }
  
  /// 获取公钥
  String getPublicKey() {
    return _rsaKeyPair.publicKey.toString();
  }
  
  /// 获取私钥
  String getPrivateKey() {
    return _rsaKeyPair.privateKey.toString();
  }
}

/// 哈希算法
enum HashAlgorithm {
  md5,
  sha1,
  sha256,
  sha512,
}

/// 加密结果
class EncryptionResult {
  final bool isSuccess;
  final String? encryptedData;
  final String? iv;
  final String? key;
  final String? error;
  
  const EncryptionResult._(
    this.isSuccess,
    this.encryptedData,
    this.iv,
    this.key,
    this.error,
  );
  
  factory EncryptionResult.success({
    required String encryptedData,
    String? iv,
    String? key,
  }) {
    return EncryptionResult._(true, encryptedData, iv, key, null);
  }
  
  factory EncryptionResult.error(String error) {
    return EncryptionResult._(false, null, null, null, error);
  }
}

/// 解密结果
class DecryptionResult {
  final bool isSuccess;
  final String? decryptedData;
  final String? error;
  
  const DecryptionResult._(this.isSuccess, this.decryptedData, this.error);
  
  factory DecryptionResult.success(String decryptedData) {
    return DecryptionResult._(true, decryptedData, null);
  }
  
  factory DecryptionResult.error(String error) {
    return DecryptionResult._(false, null, error);
  }
}

/// 加盐哈希结果
class SaltedHashResult {
  final String hash;
  final String salt;
  
  const SaltedHashResult({
    required this.hash,
    required this.salt,
  });
}
```

### 密钥管理
```dart
// packages/core/core_security/lib/src/encryption/key_manager.dart
import 'dart:convert';
import 'dart:math';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:injectable/injectable.dart';
import 'encryption_service.dart';

/// 密钥管理器
@singleton
class KeyManager {
  static const String _masterKeyKey = 'master_key';
  static const String _keyVersionKey = 'key_version';
  static const String _keyRotationDateKey = 'key_rotation_date';
  
  final FlutterSecureStorage _secureStorage;
  final EncryptionService _encryptionService;
  
  String? _cachedMasterKey;
  int _currentKeyVersion = 1;
  
  KeyManager(
    this._secureStorage,
    this._encryptionService,
  );
  
  /// 初始化密钥管理器
  Future<void> initialize() async {
    await _loadOrGenerateMasterKey();
    await _loadKeyVersion();
    await _checkKeyRotation();
  }
  
  /// 获取主密钥
  Future<String> getMasterKey() async {
    if (_cachedMasterKey != null) {
      return _cachedMasterKey!;
    }
    
    return await _loadOrGenerateMasterKey();
  }
  
  /// 生成应用密钥
  Future<String> generateAppKey(String keyId) async {
    final masterKey = await getMasterKey();
    final keyData = '$keyId:$_currentKeyVersion:${DateTime.now().millisecondsSinceEpoch}';
    
    final hash = _encryptionService.generateHash('$masterKey:$keyData');
    return base64.encode(hash.codeUnits.take(32).toList());
  }
  
  /// 存储加密密钥
  Future<void> storeEncryptedKey(String keyId, String key) async {
    final masterKey = await getMasterKey();
    final encryptionResult = _encryptionService.encryptAES(key, customKey: masterKey);
    
    if (encryptionResult.isSuccess) {
      final keyData = {
        'encrypted': encryptionResult.encryptedData!,
        'iv': encryptionResult.iv!,
        'version': _currentKeyVersion,
        'created': DateTime.now().toIso8601String(),
      };
      
      await _secureStorage.write(
        key: 'app_key_$keyId',
        value: jsonEncode(keyData),
      );
    }
  }
  
  /// 获取解密密钥
  Future<String?> getDecryptedKey(String keyId) async {
    final keyDataJson = await _secureStorage.read(key: 'app_key_$keyId');
    if (keyDataJson == null) return null;
    
    try {
      final keyData = jsonDecode(keyDataJson);
      final masterKey = await getMasterKey();
      
      final decryptionResult = _encryptionService.decryptAES(
        keyData['encrypted'],
        keyData['iv'],
        customKey: masterKey,
      );
      
      return decryptionResult.isSuccess ? decryptionResult.decryptedData : null;
    } catch (e) {
      return null;
    }
  }
  
  /// 轮换密钥
  Future<void> rotateKeys() async {
    _currentKeyVersion++;
    await _secureStorage.write(
      key: _keyVersionKey,
      value: _currentKeyVersion.toString(),
    );
    
    await _secureStorage.write(
      key: _keyRotationDateKey,
      value: DateTime.now().toIso8601String(),
    );
    
    // 生成新的主密钥
    _cachedMasterKey = null;
    await _generateNewMasterKey();
  }
  
  /// 删除密钥
  Future<void> deleteKey(String keyId) async {
    await _secureStorage.delete(key: 'app_key_$keyId');
  }
  
  /// 清除所有密钥
  Future<void> clearAllKeys() async {
    await _secureStorage.deleteAll();
    _cachedMasterKey = null;
    _currentKeyVersion = 1;
  }
  
  /// 获取密钥信息
  Future<KeyInfo?> getKeyInfo(String keyId) async {
    final keyDataJson = await _secureStorage.read(key: 'app_key_$keyId');
    if (keyDataJson == null) return null;
    
    try {
      final keyData = jsonDecode(keyDataJson);
      return KeyInfo(
        keyId: keyId,
        version: keyData['version'],
        created: DateTime.parse(keyData['created']),
      );
    } catch (e) {
      return null;
    }
  }
  
  /// 列出所有密钥
  Future<List<KeyInfo>> listAllKeys() async {
    final allKeys = await _secureStorage.readAll();
    final keyInfos = <KeyInfo>[];
    
    for (final entry in allKeys.entries) {
      if (entry.key.startsWith('app_key_')) {
        final keyId = entry.key.substring(8); // 移除 'app_key_' 前缀
        final keyInfo = await getKeyInfo(keyId);
        if (keyInfo != null) {
          keyInfos.add(keyInfo);
        }
      }
    }
    
    return keyInfos;
  }
  
  Future<String> _loadOrGenerateMasterKey() async {
    if (_cachedMasterKey != null) {
      return _cachedMasterKey!;
    }
    
    String? masterKey = await _secureStorage.read(key: _masterKeyKey);
    
    if (masterKey == null) {
      masterKey = await _generateNewMasterKey();
    }
    
    _cachedMasterKey = masterKey;
    return masterKey;
  }
  
  Future<String> _generateNewMasterKey() async {
    final masterKey = _encryptionService.generateRandomKey(length: 32);
    await _secureStorage.write(key: _masterKeyKey, value: masterKey);
    _cachedMasterKey = masterKey;
    return masterKey;
  }
  
  Future<void> _loadKeyVersion() async {
    final versionStr = await _secureStorage.read(key: _keyVersionKey);
    if (versionStr != null) {
      _currentKeyVersion = int.tryParse(versionStr) ?? 1;
    }
  }
  
  Future<void> _checkKeyRotation() async {
    final rotationDateStr = await _secureStorage.read(key: _keyRotationDateKey);
    if (rotationDateStr != null) {
      final rotationDate = DateTime.parse(rotationDateStr);
      final daysSinceRotation = DateTime.now().difference(rotationDate).inDays;
      
      // 如果超过 90 天，建议轮换密钥
      if (daysSinceRotation > 90) {
        // 这里可以发送通知或自动轮换
      }
    }
  }
}

/// 密钥信息
class KeyInfo {
  final String keyId;
  final int version;
  final DateTime created;
  
  const KeyInfo({
    required this.keyId,
    required this.version,
    required this.created,
  });
  
  Map<String, dynamic> toJson() => {
    'keyId': keyId,
    'version': version,
    'created': created.toIso8601String(),
  };
}
```

## 3. 权限控制

### 权限管理器
```dart
// packages/core/core_security/lib/src/permissions/permission_manager.dart
import 'package:permission_handler/permission_handler.dart';
import 'package:injectable/injectable.dart';

/// 权限管理器
@singleton
class PermissionManager {
  final Map<Permission, PermissionStatus> _cachedStatuses = {};
  
  /// 检查单个权限
  Future<PermissionStatus> checkPermission(Permission permission) async {
    final status = await permission.status;
    _cachedStatuses[permission] = status;
    return status;
  }
  
  /// 检查多个权限
  Future<Map<Permission, PermissionStatus>> checkPermissions(
    List<Permission> permissions,
  ) async {
    final statuses = await permissions.request();
    _cachedStatuses.addAll(statuses);
    return statuses;
  }
  
  /// 请求单个权限
  Future<PermissionRequestResult> requestPermission(
    Permission permission, {
    String? rationale,
  }) async {
    // 首先检查当前状态
    final currentStatus = await checkPermission(permission);
    
    if (currentStatus.isGranted) {
      return PermissionRequestResult.granted();
    }
    
    if (currentStatus.isPermanentlyDenied) {
      return PermissionRequestResult.permanentlyDenied(
        'Permission permanently denied. Please enable it in settings.',
      );
    }
    
    // 如果需要显示说明
    if (currentStatus.isDenied && rationale != null) {
      // 这里可以显示权限说明对话框
      // 实际实现中应该通过回调或事件来处理
    }
    
    // 请求权限
    final status = await permission.request();
    _cachedStatuses[permission] = status;
    
    if (status.isGranted) {
      return PermissionRequestResult.granted();
    } else if (status.isPermanentlyDenied) {
      return PermissionRequestResult.permanentlyDenied(
        'Permission permanently denied. Please enable it in settings.',
      );
    } else {
      return PermissionRequestResult.denied('Permission denied by user.');
    }
  }
  
  /// 请求多个权限
  Future<Map<Permission, PermissionRequestResult>> requestPermissions(
    List<Permission> permissions, {
    Map<Permission, String>? rationales,
  }) async {
    final results = <Permission, PermissionRequestResult>{};
    
    for (final permission in permissions) {
      final rationale = rationales?[permission];
      results[permission] = await requestPermission(
        permission,
        rationale: rationale,
      );
    }
    
    return results;
  }
  
  /// 打开应用设置
  Future<bool> openAppSettings() async {
    return await openAppSettings();
  }
  
  /// 检查权限是否已授予
  bool isPermissionGranted(Permission permission) {
    final status = _cachedStatuses[permission];
    return status?.isGranted ?? false;
  }
  
  /// 检查所有权限是否已授予
  bool areAllPermissionsGranted(List<Permission> permissions) {
    return permissions.every((permission) => isPermissionGranted(permission));
  }
  
  /// 获取缓存的权限状态
  PermissionStatus? getCachedPermissionStatus(Permission permission) {
    return _cachedStatuses[permission];
  }
  
  /// 清除权限缓存
  void clearCache() {
    _cachedStatuses.clear();
  }
  
  /// 获取权限组
  static List<Permission> getCameraPermissions() {
    return [Permission.camera];
  }
  
  static List<Permission> getStoragePermissions() {
    return [Permission.storage, Permission.manageExternalStorage];
  }
  
  static List<Permission> getLocationPermissions() {
    return [Permission.location, Permission.locationWhenInUse];
  }
  
  static List<Permission> getContactsPermissions() {
    return [Permission.contacts];
  }
  
  static List<Permission> getNotificationPermissions() {
    return [Permission.notification];
  }
}

/// 权限请求结果
class PermissionRequestResult {
  final PermissionResultType type;
  final String? message;
  
  const PermissionRequestResult._(this.type, this.message);
  
  factory PermissionRequestResult.granted() {
    return PermissionRequestResult._(PermissionResultType.granted, null);
  }
  
  factory PermissionRequestResult.denied(String message) {
    return PermissionRequestResult._(PermissionResultType.denied, message);
  }
  
  factory PermissionRequestResult.permanentlyDenied(String message) {
    return PermissionRequestResult._(
      PermissionResultType.permanentlyDenied,
      message,
    );
  }
  
  bool get isGranted => type == PermissionResultType.granted;
  bool get isDenied => type == PermissionResultType.denied;
  bool get isPermanentlyDenied => type == PermissionResultType.permanentlyDenied;
}

enum PermissionResultType {
  granted,
  denied,
  permanentlyDenied,
}
```

### 角色权限控制
```dart
// packages/core/core_security/lib/src/permissions/role_permission_manager.dart
import 'package:injectable/injectable.dart';

/// 角色权限管理器
@singleton
class RolePermissionManager {
  final Map<String, Role> _roles = {};
  final Map<String, List<String>> _userRoles = {};
  
  /// 定义角色
  void defineRole(Role role) {
    _roles[role.name] = role;
  }
  
  /// 分配用户角色
  void assignUserRole(String userId, String roleName) {
    if (!_roles.containsKey(roleName)) {
      throw ArgumentError('Role $roleName does not exist');
    }
    
    _userRoles.putIfAbsent(userId, () => []).add(roleName);
  }
  
  /// 移除用户角色
  void removeUserRole(String userId, String roleName) {
    _userRoles[userId]?.remove(roleName);
  }
  
  /// 检查用户是否有特定权限
  bool hasPermission(String userId, String permission) {
    final userRoles = _userRoles[userId] ?? [];
    
    for (final roleName in userRoles) {
      final role = _roles[roleName];
      if (role != null && role.hasPermission(permission)) {
        return true;
      }
    }
    
    return false;
  }
  
  /// 检查用户是否有角色
  bool hasRole(String userId, String roleName) {
    return _userRoles[userId]?.contains(roleName) ?? false;
  }
  
  /// 检查用户是否有任一角色
  bool hasAnyRole(String userId, List<String> roleNames) {
    final userRoles = _userRoles[userId] ?? [];
    return roleNames.any((role) => userRoles.contains(role));
  }
  
  /// 检查用户是否有所有角色
  bool hasAllRoles(String userId, List<String> roleNames) {
    final userRoles = _userRoles[userId] ?? [];
    return roleNames.every((role) => userRoles.contains(role));
  }
  
  /// 获取用户所有权限
  Set<String> getUserPermissions(String userId) {
    final userRoles = _userRoles[userId] ?? [];
    final permissions = <String>{};
    
    for (final roleName in userRoles) {
      final role = _roles[roleName];
      if (role != null) {
        permissions.addAll(role.permissions);
      }
    }
    
    return permissions;
  }
  
  /// 获取用户角色
  List<String> getUserRoles(String userId) {
    return List.from(_userRoles[userId] ?? []);
  }
  
  /// 获取角色信息
  Role? getRole(String roleName) {
    return _roles[roleName];
  }
  
  /// 获取所有角色
  List<Role> getAllRoles() {
    return _roles.values.toList();
  }
  
  /// 清除用户所有角色
  void clearUserRoles(String userId) {
    _userRoles.remove(userId);
  }
  
  /// 初始化默认角色
  void initializeDefaultRoles() {
    // 管理员角色
    defineRole(Role(
      name: 'admin',
      displayName: 'Administrator',
      description: 'Full system access',
      permissions: {
        'user.create',
        'user.read',
        'user.update',
        'user.delete',
        'role.manage',
        'system.configure',
        'data.export',
        'audit.view',
      },
    ));
    
    // 用户角色
    defineRole(Role(
      name: 'user',
      displayName: 'User',
      description: 'Standard user access',
      permissions: {
        'profile.read',
        'profile.update',
        'content.read',
        'content.create',
      },
    ));
    
    // 访客角色
    defineRole(Role(
      name: 'guest',
      displayName: 'Guest',
      description: 'Limited read-only access',
      permissions: {
        'content.read',
      },
    ));
    
    // 版主角色
    defineRole(Role(
      name: 'moderator',
      displayName: 'Moderator',
      description: 'Content moderation access',
      permissions: {
        'profile.read',
        'profile.update',
        'content.read',
        'content.create',
        'content.moderate',
        'user.moderate',
      },
    ));
  }
}

/// 角色定义
class Role {
  final String name;
  final String displayName;
  final String description;
  final Set<String> permissions;
  final DateTime? expiresAt;
  
  const Role({
    required this.name,
    required this.displayName,
    required this.description,
    required this.permissions,
    this.expiresAt,
  });
  
  /// 检查是否有特定权限
  bool hasPermission(String permission) {
    if (expiresAt != null && DateTime.now().isAfter(expiresAt!)) {
      return false;
    }
    return permissions.contains(permission);
  }
  
  /// 检查角色是否过期
  bool get isExpired {
    return expiresAt != null && DateTime.now().isAfter(expiresAt!);
  }
  
  Map<String, dynamic> toJson() => {
    'name': name,
    'displayName': displayName,
    'description': description,
    'permissions': permissions.toList(),
    'expiresAt': expiresAt?.toIso8601String(),
  };
  
  factory Role.fromJson(Map<String, dynamic> json) {
    return Role(
      name: json['name'],
      displayName: json['displayName'],
      description: json['description'],
      permissions: Set<String>.from(json['permissions']),
      expiresAt: json['expiresAt'] != null 
        ? DateTime.parse(json['expiresAt']) 
        : null,
    );
  }
}
```

## 4. 安全存储

### 安全存储服务
```dart
// packages/core/core_security/lib/src/storage/secure_storage_service.dart
import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:injectable/injectable.dart';
import '../encryption/encryption_service.dart';

/// 安全存储服务
@singleton
class SecureStorageService {
  static const String _encryptionKeyPrefix = 'enc_';
  static const String _metadataPrefix = 'meta_';
  
  final FlutterSecureStorage _secureStorage;
  final EncryptionService _encryptionService;
  
  SecureStorageService(
    this._secureStorage,
    this._encryptionService,
  );
  
  /// 存储加密数据
  Future<bool> storeEncrypted(
    String key,
    String value, {
    Duration? ttl,
    bool compress = false,
  }) async {
    try {
      // 生成专用密钥
      final encryptionKey = _encryptionService.generateRandomKey();
      
      // 压缩数据（如果需要）
      String dataToEncrypt = value;
      if (compress) {
        // 这里可以添加压缩逻辑
        // dataToEncrypt = compress(value);
      }
      
      // 加密数据
      final encryptionResult = _encryptionService.encryptAES(
        dataToEncrypt,
        customKey: encryptionKey,
      );
      
      if (!encryptionResult.isSuccess) {
        return false;
      }
      
      // 存储加密数据
      await _secureStorage.write(
        key: key,
        value: encryptionResult.encryptedData!,
      );
      
      // 存储加密密钥
      await _secureStorage.write(
        key: '$_encryptionKeyPrefix$key',
        value: encryptionKey,
      );
      
      // 存储 IV
      await _secureStorage.write(
        key: 'iv_$key',
        value: encryptionResult.iv!,
      );
      
      // 存储元数据
      final metadata = StorageMetadata(
        createdAt: DateTime.now(),
        expiresAt: ttl != null ? DateTime.now().add(ttl) : null,
        compressed: compress,
      );
      
      await _secureStorage.write(
        key: '$_metadataPrefix$key',
        value: jsonEncode(metadata.toJson()),
      );
      
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// 读取加密数据
  Future<String?> readEncrypted(String key) async {
    try {
      // 检查元数据
      final metadataJson = await _secureStorage.read(key: '$_metadataPrefix$key');
      if (metadataJson != null) {
        final metadata = StorageMetadata.fromJson(jsonDecode(metadataJson));
        
        // 检查是否过期
        if (metadata.expiresAt != null && 
            DateTime.now().isAfter(metadata.expiresAt!)) {
          await deleteEncrypted(key);
          return null;
        }
      }
      
      // 读取加密数据
      final encryptedData = await _secureStorage.read(key: key);
      if (encryptedData == null) return null;
      
      // 读取加密密钥
      final encryptionKey = await _secureStorage.read(key: '$_encryptionKeyPrefix$key');
      if (encryptionKey == null) return null;
      
      // 读取 IV
      final iv = await _secureStorage.read(key: 'iv_$key');
      if (iv == null) return null;
      
      // 解密数据
      final decryptionResult = _encryptionService.decryptAES(
        encryptedData,
        iv,
        customKey: encryptionKey,
      );
      
      if (!decryptionResult.isSuccess) {
        return null;
      }
      
      String result = decryptionResult.decryptedData!;
      
      // 解压缩数据（如果需要）
      if (metadataJson != null) {
        final metadata = StorageMetadata.fromJson(jsonDecode(metadataJson));
        if (metadata.compressed) {
          // 这里可以添加解压缩逻辑
          // result = decompress(result);
        }
      }
      
      return result;
    } catch (e) {
      return null;
    }
  }
  
  /// 删除加密数据
  Future<bool> deleteEncrypted(String key) async {
    try {
      await _secureStorage.delete(key: key);
      await _secureStorage.delete(key: '$_encryptionKeyPrefix$key');
      await _secureStorage.delete(key: 'iv_$key');
      await _secureStorage.delete(key: '$_metadataPrefix$key');
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// 存储 JSON 对象
  Future<bool> storeJsonEncrypted(
    String key,
    Map<String, dynamic> value, {
    Duration? ttl,
  }) async {
    return await storeEncrypted(
      key,
      jsonEncode(value),
      ttl: ttl,
      compress: true,
    );
  }
  
  /// 读取 JSON 对象
  Future<Map<String, dynamic>?> readJsonEncrypted(String key) async {
    final jsonString = await readEncrypted(key);
    if (jsonString == null) return null;
    
    try {
      return jsonDecode(jsonString);
    } catch (e) {
      return null;
    }
  }
  
  /// 检查密钥是否存在
  Future<bool> containsKey(String key) async {
    return await _secureStorage.containsKey(key: key);
  }
  
  /// 获取所有密钥
  Future<List<String>> getAllKeys() async {
    final allKeys = await _secureStorage.readAll();
    return allKeys.keys
        .where((key) => !key.startsWith(_encryptionKeyPrefix) &&
                       !key.startsWith('iv_') &&
                       !key.startsWith(_metadataPrefix))
        .toList();
  }
  
  /// 清理过期数据
  Future<void> cleanupExpiredData() async {
    final allKeys = await _secureStorage.readAll();
    final now = DateTime.now();
    
    for (final key in allKeys.keys) {
      if (key.startsWith(_metadataPrefix)) {
        final dataKey = key.substring(_metadataPrefix.length);
        try {
          final metadata = StorageMetadata.fromJson(jsonDecode(allKeys[key]!));
          if (metadata.expiresAt != null && now.isAfter(metadata.expiresAt!)) {
            await deleteEncrypted(dataKey);
          }
        } catch (e) {
          // 忽略解析错误
        }
      }
    }
  }
  
  /// 获取存储统计信息
  Future<StorageStatistics> getStatistics() async {
    final allKeys = await getAllKeys();
    int totalSize = 0;
    int expiredCount = 0;
    final now = DateTime.now();
    
    for (final key in allKeys) {
      final data = await _secureStorage.read(key: key);
      if (data != null) {
        totalSize += data.length;
      }
      
      final metadataJson = await _secureStorage.read(key: '$_metadataPrefix$key');
      if (metadataJson != null) {
        try {
          final metadata = StorageMetadata.fromJson(jsonDecode(metadataJson));
          if (metadata.expiresAt != null && now.isAfter(metadata.expiresAt!)) {
            expiredCount++;
          }
        } catch (e) {
          // 忽略解析错误
        }
      }
    }
    
    return StorageStatistics(
      totalKeys: allKeys.length,
      totalSize: totalSize,
      expiredKeys: expiredCount,
    );
  }
}

/// 存储元数据
class StorageMetadata {
  final DateTime createdAt;
  final DateTime? expiresAt;
  final bool compressed;
  
  const StorageMetadata({
    required this.createdAt,
    this.expiresAt,
    this.compressed = false,
  });
  
  Map<String, dynamic> toJson() => {
    'createdAt': createdAt.toIso8601String(),
    'expiresAt': expiresAt?.toIso8601String(),
    'compressed': compressed,
  };
  
  factory StorageMetadata.fromJson(Map<String, dynamic> json) {
    return StorageMetadata(
      createdAt: DateTime.parse(json['createdAt']),
      expiresAt: json['expiresAt'] != null 
        ? DateTime.parse(json['expiresAt']) 
        : null,
      compressed: json['compressed'] ?? false,
    );
  }
}

/// 存储统计信息
class StorageStatistics {
  final int totalKeys;
  final int totalSize;
  final int expiredKeys;
  
  const StorageStatistics({
    required this.totalKeys,
    required this.totalSize,
    required this.expiredKeys,
  });
  
  String get formattedSize {
    if (totalSize < 1024) return '${totalSize}B';
    if (totalSize < 1024 * 1024) return '${(totalSize / 1024).toStringAsFixed(1)}KB';
    return '${(totalSize / (1024 * 1024)).toStringAsFixed(1)}MB';
  }
}
```

## 5. 网络安全

### SSL/TLS 证书验证
```dart
// packages/core/core_security/lib/src/network/certificate_validator.dart
import 'dart:io';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:injectable/injectable.dart';

/// SSL/TLS 证书验证器
@singleton
class CertificateValidator {
  final Set<String> _pinnedCertificates = {};
  final Set<String> _trustedDomains = {};
  
  /// 添加证书固定
  void addCertificatePin(String domain, String certificateHash) {
    _pinnedCertificates.add('$domain:$certificateHash');
  }
  
  /// 添加受信任域名
  void addTrustedDomain(String domain) {
    _trustedDomains.add(domain);
  }
  
  /// 验证证书
  bool validateCertificate(X509Certificate certificate, String host) {
    try {
      // 检查证书是否过期
      if (_isCertificateExpired(certificate)) {
        return false;
      }
      
      // 检查主机名匹配
      if (!_isHostnameValid(certificate, host)) {
        return false;
      }
      
      // 检查证书固定
      if (_pinnedCertificates.isNotEmpty) {
        return _validateCertificatePin(certificate, host);
      }
      
      // 检查受信任域名
      if (_trustedDomains.contains(host)) {
        return true;
      }
      
      // 默认验证
      return _performDefaultValidation(certificate);
    } catch (e) {
      return false;
    }
  }
  
  /// 生成证书指纹
  String generateCertificateFingerprint(X509Certificate certificate) {
    final der = certificate.der;
    final digest = sha256.convert(der);
    return digest.toString();
  }
  
  /// 验证证书链
  bool validateCertificateChain(List<X509Certificate> chain) {
    if (chain.isEmpty) return false;
    
    for (int i = 0; i < chain.length - 1; i++) {
      final current = chain[i];
      final issuer = chain[i + 1];
      
      if (!_verifyCertificateSignature(current, issuer)) {
        return false;
      }
    }
    
    return true;
  }
  
  bool _isCertificateExpired(X509Certificate certificate) {
    final now = DateTime.now();
    return now.isBefore(certificate.startValidity) || 
           now.isAfter(certificate.endValidity);
  }
  
  bool _isHostnameValid(X509Certificate certificate, String host) {
    // 检查 Subject Alternative Names
    final sans = _extractSubjectAlternativeNames(certificate);
    for (final san in sans) {
      if (_matchesHostname(san, host)) {
        return true;
      }
    }
    
    // 检查 Common Name
    final commonName = _extractCommonName(certificate);
    if (commonName != null && _matchesHostname(commonName, host)) {
      return true;
    }
    
    return false;
  }
  
  bool _validateCertificatePin(X509Certificate certificate, String host) {
    final fingerprint = generateCertificateFingerprint(certificate);
    return _pinnedCertificates.contains('$host:$fingerprint');
  }
  
  bool _performDefaultValidation(X509Certificate certificate) {
    // 执行基本的证书验证
    // 这里可以添加更多的验证逻辑
    return true;
  }
  
  bool _verifyCertificateSignature(
    X509Certificate certificate,
    X509Certificate issuer,
  ) {
    // 验证证书签名
    // 这里需要实现实际的签名验证逻辑
    return true;
  }
  
  List<String> _extractSubjectAlternativeNames(X509Certificate certificate) {
    // 提取 Subject Alternative Names
    // 这里需要解析证书的 SAN 扩展
    return [];
  }
  
  String? _extractCommonName(X509Certificate certificate) {
    // 提取 Common Name
    final subject = certificate.subject;
    final cnMatch = RegExp(r'CN=([^,]+)').firstMatch(subject);
    return cnMatch?.group(1);
  }
  
  bool _matchesHostname(String pattern, String hostname) {
    if (pattern == hostname) return true;
    
    // 支持通配符匹配
    if (pattern.startsWith('*.')) {
      final domain = pattern.substring(2);
      return hostname.endsWith('.$domain') || hostname == domain;
    }
    
    return false;
  }
}
```

### 网络请求安全拦截器
```dart
// packages/core/core_security/lib/src/network/security_interceptor.dart
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'certificate_validator.dart';
import '../encryption/encryption_service.dart';

/// 网络安全拦截器
@singleton
class SecurityInterceptor extends Interceptor {
  final CertificateValidator _certificateValidator;
  final EncryptionService _encryptionService;
  
  SecurityInterceptor(
    this._certificateValidator,
    this._encryptionService,
  );
  
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // 添加安全头
    _addSecurityHeaders(options);
    
    // 验证请求 URL
    if (!_isUrlSecure(options.uri)) {
      handler.reject(
        DioException(
          requestOptions: options,
          error: 'Insecure URL not allowed',
          type: DioExceptionType.badRequest,
        ),
      );
      return;
    }
    
    // 加密敏感数据
    _encryptSensitiveData(options);
    
    super.onRequest(options, handler);
  }
  
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // 验证响应
    if (!_validateResponse(response)) {
      handler.reject(
        DioException(
          requestOptions: response.requestOptions,
          response: response,
          error: 'Invalid response',
          type: DioExceptionType.badResponse,
        ),
      );
      return;
    }
    
    // 解密响应数据
    _decryptResponseData(response);
    
    super.onResponse(response, handler);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // 记录安全相关错误
    _logSecurityError(err);
    
    super.onError(err, handler);
  }
  
  void _addSecurityHeaders(RequestOptions options) {
    // 添加 CSRF 保护
    options.headers['X-Requested-With'] = 'XMLHttpRequest';
    
    // 添加内容类型验证
    if (options.data != null && !options.headers.containsKey('Content-Type')) {
      options.headers['Content-Type'] = 'application/json';
    }
    
    // 添加缓存控制
    options.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate';
    
    // 添加安全策略
    options.headers['X-Content-Type-Options'] = 'nosniff';
    options.headers['X-Frame-Options'] = 'DENY';
  }
  
  bool _isUrlSecure(Uri uri) {
    // 检查是否使用 HTTPS
    if (uri.scheme != 'https') {
      return false;
    }
    
    // 检查域名白名单
    final allowedDomains = [
      'api.example.com',
      'secure.example.com',
    ];
    
    return allowedDomains.any((domain) => uri.host.endsWith(domain));
  }
  
  void _encryptSensitiveData(RequestOptions options) {
    if (options.data is Map<String, dynamic>) {
      final data = options.data as Map<String, dynamic>;
      final sensitiveFields = ['password', 'token', 'secret', 'key'];
      
      for (final field in sensitiveFields) {
        if (data.containsKey(field)) {
          final value = data[field]?.toString();
          if (value != null) {
            final encryptionResult = _encryptionService.encryptAES(value);
            if (encryptionResult.isSuccess) {
              data[field] = {
                'encrypted': encryptionResult.encryptedData,
                'iv': encryptionResult.iv,
              };
            }
          }
        }
      }
    }
  }
  
  bool _validateResponse(Response response) {
    // 验证响应状态码
    if (response.statusCode == null || response.statusCode! < 200 || response.statusCode! >= 300) {
      return false;
    }
    
    // 验证响应头
    final contentType = response.headers.value('content-type');
    if (contentType != null && !contentType.startsWith('application/json')) {
      // 根据需要调整内容类型验证
    }
    
    // 验证响应大小
    final contentLength = response.headers.value('content-length');
    if (contentLength != null) {
      final length = int.tryParse(contentLength);
      if (length != null && length > 10 * 1024 * 1024) { // 10MB 限制
        return false;
      }
    }
    
    return true;
  }
  
  void _decryptResponseData(Response response) {
    if (response.data is Map<String, dynamic>) {
      final data = response.data as Map<String, dynamic>;
      _decryptMapData(data);
    } else if (response.data is List) {
      final list = response.data as List;
      for (final item in list) {
        if (item is Map<String, dynamic>) {
          _decryptMapData(item);
        }
      }
    }
  }
  
  void _decryptMapData(Map<String, dynamic> data) {
    for (final entry in data.entries.toList()) {
      if (entry.value is Map<String, dynamic>) {
        final valueMap = entry.value as Map<String, dynamic>;
        if (valueMap.containsKey('encrypted') && valueMap.containsKey('iv')) {
          final decryptionResult = _encryptionService.decryptAES(
            valueMap['encrypted'],
            valueMap['iv'],
          );
          if (decryptionResult.isSuccess) {
            data[entry.key] = decryptionResult.decryptedData;
          }
        } else {
          _decryptMapData(valueMap);
        }
      }
    }
  }
  
  void _logSecurityError(DioException error) {
    // 记录安全相关的错误
    // 这里可以集成到日志系统或安全监控系统
    print('Security Error: ${error.message}');
  }
}
```

## 6. 安全审计

### 安全审计服务
```dart
// packages/core/core_security/lib/src/audit/security_audit_service.dart
import 'dart:convert';
import 'package:injectable/injectable.dart';

/// 安全审计服务
@singleton
class SecurityAuditService {
  final List<SecurityEvent> _events = [];
  final int _maxEvents = 1000;
  
  /// 记录安全事件
  void logSecurityEvent(SecurityEvent event) {
    _events.add(event);
    
    // 保持事件数量在限制内
    if (_events.length > _maxEvents) {
      _events.removeAt(0);
    }
    
    // 检查是否需要立即处理
    if (event.severity == SecurityEventSeverity.critical) {
      _handleCriticalEvent(event);
    }
  }
  
  /// 记录登录事件
  void logLoginEvent({
    required String userId,
    required bool success,
    String? ipAddress,
    String? userAgent,
    String? failureReason,
  }) {
    logSecurityEvent(SecurityEvent(
      type: SecurityEventType.authentication,
      action: success ? 'login_success' : 'login_failure',
      userId: userId,
      timestamp: DateTime.now(),
      severity: success ? SecurityEventSeverity.info : SecurityEventSeverity.warning,
      details: {
        'ip_address': ipAddress,
        'user_agent': userAgent,
        if (failureReason != null) 'failure_reason': failureReason,
      },
    ));
  }
  
  /// 记录权限事件
  void logPermissionEvent({
    required String userId,
    required String action,
    required String resource,
    required bool granted,
    String? reason,
  }) {
    logSecurityEvent(SecurityEvent(
      type: SecurityEventType.authorization,
      action: granted ? 'permission_granted' : 'permission_denied',
      userId: userId,
      timestamp: DateTime.now(),
      severity: granted ? SecurityEventSeverity.info : SecurityEventSeverity.warning,
      details: {
        'requested_action': action,
        'resource': resource,
        if (reason != null) 'reason': reason,
      },
    ));
  }
  
  /// 记录数据访问事件
  void logDataAccessEvent({
    required String userId,
    required String action,
    required String dataType,
    String? recordId,
    Map<String, dynamic>? metadata,
  }) {
    logSecurityEvent(SecurityEvent(
      type: SecurityEventType.dataAccess,
      action: action,
      userId: userId,
      timestamp: DateTime.now(),
      severity: SecurityEventSeverity.info,
      details: {
        'data_type': dataType,
        if (recordId != null) 'record_id': recordId,
        if (metadata != null) ...metadata,
      },
    ));
  }
  
  /// 记录安全违规事件
  void logSecurityViolation({
    required String userId,
    required String violation,
    required String description,
    SecurityEventSeverity severity = SecurityEventSeverity.high,
    Map<String, dynamic>? context,
  }) {
    logSecurityEvent(SecurityEvent(
      type: SecurityEventType.securityViolation,
      action: violation,
      userId: userId,
      timestamp: DateTime.now(),
      severity: severity,
      details: {
        'description': description,
        if (context != null) 'context': context,
      },
    ));
  }
  
  /// 获取安全事件
  List<SecurityEvent> getSecurityEvents({
    SecurityEventType? type,
    String? userId,
    DateTime? startTime,
    DateTime? endTime,
    SecurityEventSeverity? minSeverity,
    int? limit,
  }) {
    var filteredEvents = _events.where((event) {
      if (type != null && event.type != type) return false;
      if (userId != null && event.userId != userId) return false;
      if (startTime != null && event.timestamp.isBefore(startTime)) return false;
      if (endTime != null && event.timestamp.isAfter(endTime)) return false;
      if (minSeverity != null && event.severity.index < minSeverity.index) return false;
      return true;
    }).toList();
    
    // 按时间倒序排列
    filteredEvents.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    
    if (limit != null && filteredEvents.length > limit) {
      filteredEvents = filteredEvents.take(limit).toList();
    }
    
    return filteredEvents;
  }
  
  /// 获取安全统计信息
  SecurityStatistics getSecurityStatistics({
    DateTime? startTime,
    DateTime? endTime,
  }) {
    final events = getSecurityEvents(
      startTime: startTime,
      endTime: endTime,
    );
    
    final eventsByType = <SecurityEventType, int>{};
    final eventsBySeverity = <SecurityEventSeverity, int>{};
    final failedLogins = <String, int>{};
    
    for (final event in events) {
      eventsByType[event.type] = (eventsByType[event.type] ?? 0) + 1;
      eventsBySeverity[event.severity] = (eventsBySeverity[event.severity] ?? 0) + 1;
      
      if (event.type == SecurityEventType.authentication && 
          event.action == 'login_failure') {
        failedLogins[event.userId] = (failedLogins[event.userId] ?? 0) + 1;
      }
    }
    
    return SecurityStatistics(
      totalEvents: events.length,
      eventsByType: eventsByType,
      eventsBySeverity: eventsBySeverity,
      topFailedLoginUsers: failedLogins.entries
          .toList()
          ..sort((a, b) => b.value.compareTo(a.value)),
    );
  }
  
  /// 检测异常行为
  List<SecurityAlert> detectAnomalies() {
    final alerts = <SecurityAlert>[];
    final now = DateTime.now();
    final oneHourAgo = now.subtract(Duration(hours: 1));
    
    // 检测频繁登录失败
    final recentEvents = getSecurityEvents(
      type: SecurityEventType.authentication,
      startTime: oneHourAgo,
    );
    
    final failedLoginsByUser = <String, int>{};
    for (final event in recentEvents) {
      if (event.action == 'login_failure') {
        failedLoginsByUser[event.userId] = 
            (failedLoginsByUser[event.userId] ?? 0) + 1;
      }
    }
    
    for (final entry in failedLoginsByUser.entries) {
      if (entry.value >= 5) {
        alerts.add(SecurityAlert(
          type: SecurityAlertType.suspiciousActivity,
          severity: SecurityEventSeverity.high,
          title: 'Multiple Failed Login Attempts',
          description: 'User ${entry.key} has ${entry.value} failed login attempts in the last hour',
          userId: entry.key,
          timestamp: now,
        ));
      }
    }
    
    // 检测权限提升尝试
    final permissionEvents = getSecurityEvents(
      type: SecurityEventType.authorization,
      startTime: oneHourAgo,
    );
    
    final deniedPermissionsByUser = <String, int>{};
    for (final event in permissionEvents) {
      if (event.action == 'permission_denied') {
        deniedPermissionsByUser[event.userId] = 
            (deniedPermissionsByUser[event.userId] ?? 0) + 1;
      }
    }
    
    for (final entry in deniedPermissionsByUser.entries) {
      if (entry.value >= 10) {
        alerts.add(SecurityAlert(
          type: SecurityAlertType.privilegeEscalation,
          severity: SecurityEventSeverity.high,
          title: 'Multiple Permission Denials',
          description: 'User ${entry.key} has ${entry.value} permission denials in the last hour',
          userId: entry.key,
          timestamp: now,
        ));
      }
    }
    
    return alerts;
  }
  
  /// 导出审计日志
  String exportAuditLog({
    DateTime? startTime,
    DateTime? endTime,
    String format = 'json',
  }) {
    final events = getSecurityEvents(
      startTime: startTime,
      endTime: endTime,
    );
    
    switch (format.toLowerCase()) {
      case 'json':
        return jsonEncode(events.map((e) => e.toJson()).toList());
      case 'csv':
        return _exportToCsv(events);
      default:
        throw ArgumentError('Unsupported format: $format');
    }
  }
  
  void _handleCriticalEvent(SecurityEvent event) {
    // 处理关键安全事件
    // 可以发送通知、触发警报等
    print('CRITICAL SECURITY EVENT: ${event.action} by ${event.userId}');
  }
  
  String _exportToCsv(List<SecurityEvent> events) {
    final buffer = StringBuffer();
    buffer.writeln('Timestamp,Type,Action,UserId,Severity,Details');
    
    for (final event in events) {
      buffer.writeln([
        event.timestamp.toIso8601String(),
        event.type.toString(),
        event.action,
        event.userId,
        event.severity.toString(),
        jsonEncode(event.details),
      ].map((field) => '"${field.toString().replaceAll('"', '""')}"').join(','));
    }
    
    return buffer.toString();
  }
}

/// 安全事件
class SecurityEvent {
  final SecurityEventType type;
  final String action;
  final String userId;
  final DateTime timestamp;
  final SecurityEventSeverity severity;
  final Map<String, dynamic> details;
  
  const SecurityEvent({
    required this.type,
    required this.action,
    required this.userId,
    required this.timestamp,
    required this.severity,
    this.details = const {},
  });
  
  Map<String, dynamic> toJson() => {
    'type': type.toString(),
    'action': action,
    'userId': userId,
    'timestamp': timestamp.toIso8601String(),
    'severity': severity.toString(),
    'details': details,
  };
}

/// 安全事件类型
enum SecurityEventType {
  authentication,
  authorization,
  dataAccess,
  securityViolation,
  systemAccess,
}

/// 安全事件严重程度
enum SecurityEventSeverity {
  info,
  low,
  warning,
  high,
  critical,
}

/// 安全统计信息
class SecurityStatistics {
  final int totalEvents;
  final Map<SecurityEventType, int> eventsByType;
  final Map<SecurityEventSeverity, int> eventsBySeverity;
  final List<MapEntry<String, int>> topFailedLoginUsers;
  
  const SecurityStatistics({
    required this.totalEvents,
    required this.eventsByType,
    required this.eventsBySeverity,
    required this.topFailedLoginUsers,
  });
}

/// 安全警报
class SecurityAlert {
  final SecurityAlertType type;
  final SecurityEventSeverity severity;
  final String title;
  final String description;
  final String? userId;
  final DateTime timestamp;
  
  const SecurityAlert({
    required this.type,
    required this.severity,
    required this.title,
    required this.description,
    this.userId,
    required this.timestamp,
  });
}

/// 安全警报类型
enum SecurityAlertType {
  suspiciousActivity,
  privilegeEscalation,
  dataExfiltration,
  unauthorizedAccess,
}
```

## 7. 使用示例

### 安全服务集成示例
```dart
// lib/main.dart
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:core_security/core_security.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化安全服务
  await setupSecurityServices();
  
  runApp(MyApp());
}

Future<void> setupSecurityServices() async {
  final getIt = GetIt.instance;
  
  // 注册安全服务
  getIt.registerSingleton<EncryptionService>(EncryptionService());
  getIt.registerSingleton<JwtManager>(JwtManager());
  getIt.registerSingleton<BiometricAuthService>(BiometricAuthService());
  getIt.registerSingleton<PermissionManager>(PermissionManager());
  getIt.registerSingleton<RolePermissionManager>(RolePermissionManager());
  getIt.registerSingleton<SecurityAuditService>(SecurityAuditService());
  
  // 初始化角色权限
  final roleManager = getIt<RolePermissionManager>();
  roleManager.initializeDefaultRoles();
  
  // 设置证书固定
  final certificateValidator = CertificateValidator();
  certificateValidator.addCertificatePin(
    'api.example.com',
    'sha256-hash-of-certificate',
  );
  getIt.registerSingleton<CertificateValidator>(certificateValidator);
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Secure Flutter App',
      home: SecurityDemoPage(),
    );
  }
}

class SecurityDemoPage extends StatefulWidget {
  @override
  _SecurityDemoPageState createState() => _SecurityDemoPageState();
}

class _SecurityDemoPageState extends State<SecurityDemoPage> {
  final _encryptionService = GetIt.instance<EncryptionService>();
  final _biometricAuth = GetIt.instance<BiometricAuthService>();
  final _permissionManager = GetIt.instance<PermissionManager>();
  final _auditService = GetIt.instance<SecurityAuditService>();
  
  String _encryptedText = '';
  String _decryptedText = '';
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Security Demo'),
        actions: [
          IconButton(
            icon: Icon(Icons.security),
            onPressed: _showSecurityReport,
          ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildEncryptionSection(),
            SizedBox(height: 20),
            _buildBiometricSection(),
            SizedBox(height: 20),
            _buildPermissionSection(),
            SizedBox(height: 20),
            _buildAuditSection(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildEncryptionSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Encryption Demo',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            TextField(
              decoration: InputDecoration(
                labelText: 'Text to encrypt',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                final result = _encryptionService.encryptAES(value);
                if (result.isSuccess) {
                  setState(() {
                    _encryptedText = result.encryptedData!;
                  });
                }
              },
            ),
            SizedBox(height: 10),
            Text('Encrypted: $_encryptedText'),
            SizedBox(height: 10),
            ElevatedButton(
              onPressed: _encryptedText.isNotEmpty ? _decryptText : null,
              child: Text('Decrypt'),
            ),
            if (_decryptedText.isNotEmpty)
              Text('Decrypted: $_decryptedText'),
          ],
        ),
      ),
    );
  }
  
  Widget _buildBiometricSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Biometric Authentication',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            ElevatedButton(
              onPressed: _authenticateWithBiometrics,
              child: Text('Authenticate'),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildPermissionSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Permissions',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            ElevatedButton(
              onPressed: _requestCameraPermission,
              child: Text('Request Camera Permission'),
            ),
            SizedBox(height: 10),
            ElevatedButton(
              onPressed: _requestLocationPermission,
              child: Text('Request Location Permission'),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildAuditSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Security Audit',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            ElevatedButton(
              onPressed: _simulateSecurityEvent,
              child: Text('Simulate Security Event'),
            ),
          ],
        ),
      ),
    );
  }
  
  void _decryptText() {
    // 这里需要实际的解密逻辑，包括 IV 和密钥
    // 为了演示，我们简化处理
    setState(() {
      _decryptedText = 'Decrypted text (demo)';
    });
  }
  
  void _authenticateWithBiometrics() async {
    final result = await _biometricAuth.authenticate(
      localizedReason: 'Please authenticate to access secure features',
    );
    
    if (result.isSuccess) {
      _auditService.logLoginEvent(
        userId: 'demo_user',
        success: true,
      );
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Authentication successful')),
      );
    } else {
      _auditService.logLoginEvent(
        userId: 'demo_user',
        success: false,
        failureReason: result.error ?? 'Authentication failed',
      );
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Authentication failed: ${result.error}')),
      );
    }
  }
  
  void _requestCameraPermission() async {
    final result = await _permissionManager.requestPermission(
      Permission.camera,
      rationale: 'Camera access is needed to take photos',
    );
    
    _auditService.logPermissionEvent(
      userId: 'demo_user',
      action: 'camera_access',
      resource: 'camera',
      granted: result.isGranted,
      reason: result.message,
    );
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          result.isGranted 
            ? 'Camera permission granted' 
            : 'Camera permission denied: ${result.message}',
        ),
      ),
    );
  }
  
  void _requestLocationPermission() async {
    final result = await _permissionManager.requestPermission(
      Permission.location,
      rationale: 'Location access is needed for location-based features',
    );
    
    _auditService.logPermissionEvent(
      userId: 'demo_user',
      action: 'location_access',
      resource: 'location',
      granted: result.isGranted,
      reason: result.message,
    );
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          result.isGranted 
            ? 'Location permission granted' 
            : 'Location permission denied: ${result.message}',
        ),
      ),
    );
  }
  
  void _simulateSecurityEvent() {
    _auditService.logSecurityViolation(
      userId: 'demo_user',
      violation: 'suspicious_activity',
      description: 'Simulated security violation for demo purposes',
      severity: SecurityEventSeverity.warning,
    );
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Security event logged')),
    );
  }
  
  void _showSecurityReport() {
    final statistics = _auditService.getSecurityStatistics();
    final alerts = _auditService.detectAnomalies();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Security Report'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Total Events: ${statistics.totalEvents}'),
              SizedBox(height: 10),
              Text('Events by Type:'),
              ...statistics.eventsByType.entries.map(
                (entry) => Text('  ${entry.key}: ${entry.value}'),
              ),
              SizedBox(height: 10),
              Text('Events by Severity:'),
              ...statistics.eventsBySeverity.entries.map(
                (entry) => Text('  ${entry.key}: ${entry.value}'),
              ),
              if (alerts.isNotEmpty) ..[
                SizedBox(height: 10),
                Text('Active Alerts:'),
                ...alerts.map(
                  (alert) => Text('  ${alert.title}: ${alert.description}'),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }
}
```

这个安全实现提供了完整的企业级安全解决方案，包括：

1. **认证安全**：JWT Token 管理、生物识别认证
2. **数据加密**：AES/RSA 加密、密钥管理、哈希算法
3. **权限控制**：系统权限管理、角色权限控制
4. **安全存储**：加密存储、TTL 支持、元数据管理
5. **网络安全**：证书验证、SSL/TLS 固定、安全拦截器
6. **安全审计**：事件记录、异常检测、统计分析
7. **集成示例**：完整的使用演示和最佳实践

该框架遵循 Clean Architecture 原则，支持依赖注入，易于扩展和维护，为企业级应用提供了全面的安全保障。
```