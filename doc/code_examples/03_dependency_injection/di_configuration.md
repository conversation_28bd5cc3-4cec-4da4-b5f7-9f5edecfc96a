# 依赖注入配置实现示例

## 1. 核心配置文件

### 主要依赖注入配置
```dart
// packages/core/core_di/lib/src/injection.dart
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:local_auth/local_auth.dart';
import 'package:core_network/core_network.dart';
import 'package:core_storage/core_storage.dart';
import 'package:core_platform/core_platform.dart';
import 'package:shared_models/shared_models.dart';
import 'injection.config.dart';

/// 全局依赖注入容器
final GetIt getIt = GetIt.instance;

/// 配置依赖注入
@InjectableInit(
  initializerName: 'init',
  preferRelativeImports: true,
  asExtension: true,
)
Future<void> configureDependencies({
  required String environment,
}) async {
  // 注册环境
  getIt.registerSingleton<String>(
    environment,
    instanceName: 'environment',
  );

  // 注册第三方依赖
  await _registerThirdPartyDependencies();

  // 初始化生成的依赖注入配置
  getIt.init(environment: environment);

  // 验证依赖注入配置
  await _validateDependencies();
}

/// 注册第三方依赖
Future<void> _registerThirdPartyDependencies() async {
  // SharedPreferences
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(sharedPreferences);

  // FlutterSecureStorage
  const secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );
  getIt.registerSingleton<FlutterSecureStorage>(secureStorage);

  // Connectivity
  getIt.registerSingleton<Connectivity>(Connectivity());

  // PackageInfo
  final packageInfo = await PackageInfo.fromPlatform();
  getIt.registerSingleton<PackageInfo>(packageInfo);

  // DeviceInfo
  getIt.registerSingleton<DeviceInfoPlugin>(DeviceInfoPlugin());

  // LocalAuthentication
  getIt.registerSingleton<LocalAuthentication>(LocalAuthentication());

  // Dio (网络客户端)
  getIt.registerSingleton<Dio>(_createDio());
}

/// 创建Dio实例
Dio _createDio() {
  final dio = Dio();
  
  // 基础配置
  dio.options = BaseOptions(
    connectTimeout: const Duration(seconds: 30),
    receiveTimeout: const Duration(seconds: 30),
    sendTimeout: const Duration(seconds: 30),
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  );

  // 添加拦截器（在生产环境中会通过依赖注入添加）
  if (kDebugMode) {
    dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: false,
        error: true,
      ),
    );
  }

  return dio;
}

/// 验证依赖注入配置
Future<void> _validateDependencies() async {
  try {
    // 验证核心服务
    getIt<NetworkService>();
    getIt<StorageService>();
    getIt<PlatformService>();
    
    // 验证环境配置
    final environment = getIt<String>(instanceName: 'environment');
    assert(environment.isNotEmpty, 'Environment must not be empty');
    
    print('✅ Dependency injection configured successfully for $environment');
  } catch (e) {
    print('❌ Dependency injection validation failed: $e');
    rethrow;
  }
}

/// 重置依赖注入（主要用于测试）
Future<void> resetDependencies() async {
  await getIt.reset();
}

/// 获取环境名称
String get currentEnvironment {
  return getIt<String>(instanceName: 'environment');
}

/// 检查是否为开发环境
bool get isDevelopment => currentEnvironment == Environment.dev;

/// 检查是否为测试环境
bool get isTest => currentEnvironment == Environment.test;

/// 检查是否为生产环境
bool get isProduction => currentEnvironment == Environment.prod;
```

### 环境配置
```dart
// packages/core/core_di/lib/src/environments.dart
import 'package:injectable/injectable.dart';

/// 环境常量
class Environment {
  static const String dev = 'dev';
  static const String test = 'test';
  static const String prod = 'prod';
}

/// 开发环境配置
@dev
@injectable
class DevEnvironmentConfig implements EnvironmentConfig {
  @override
  String get apiBaseUrl => 'https://api-dev.example.com';

  @override
  String get websocketUrl => 'wss://ws-dev.example.com';

  @override
  bool get enableLogging => true;

  @override
  bool get enableCrashlytics => false;

  @override
  bool get enableAnalytics => false;

  @override
  Duration get cacheTimeout => const Duration(minutes: 5);

  @override
  int get maxRetryAttempts => 3;

  @override
  Map<String, dynamic> get features => {
    'feature_new_ui': true,
    'feature_beta_api': true,
    'feature_debug_tools': true,
  };
}

/// 测试环境配置
@test
@injectable
class TestEnvironmentConfig implements EnvironmentConfig {
  @override
  String get apiBaseUrl => 'https://api-test.example.com';

  @override
  String get websocketUrl => 'wss://ws-test.example.com';

  @override
  bool get enableLogging => true;

  @override
  bool get enableCrashlytics => true;

  @override
  bool get enableAnalytics => false;

  @override
  Duration get cacheTimeout => const Duration(minutes: 10);

  @override
  int get maxRetryAttempts => 2;

  @override
  Map<String, dynamic> get features => {
    'feature_new_ui': true,
    'feature_beta_api': false,
    'feature_debug_tools': false,
  };
}

/// 生产环境配置
@prod
@injectable
class ProdEnvironmentConfig implements EnvironmentConfig {
  @override
  String get apiBaseUrl => 'https://api.example.com';

  @override
  String get websocketUrl => 'wss://ws.example.com';

  @override
  bool get enableLogging => false;

  @override
  bool get enableCrashlytics => true;

  @override
  bool get enableAnalytics => true;

  @override
  Duration get cacheTimeout => const Duration(hours: 1);

  @override
  int get maxRetryAttempts => 1;

  @override
  Map<String, dynamic> get features => {
    'feature_new_ui': false,
    'feature_beta_api': false,
    'feature_debug_tools': false,
  };
}

/// 环境配置接口
abstract class EnvironmentConfig {
  String get apiBaseUrl;
  String get websocketUrl;
  bool get enableLogging;
  bool get enableCrashlytics;
  bool get enableAnalytics;
  Duration get cacheTimeout;
  int get maxRetryAttempts;
  Map<String, dynamic> get features;
}
```

## 2. 模块化依赖注入

### 网络模块
```dart
// packages/core/core_network/lib/src/di/network_module.dart
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:get_it/get_it.dart';
import '../services/network_service.dart';
import '../interceptors/auth_interceptor.dart';
import '../interceptors/retry_interceptor.dart';
import '../interceptors/cache_interceptor.dart';
import '../interceptors/error_interceptor.dart';

/// 网络模块依赖注入
@module
abstract class NetworkModule {
  /// 注册网络服务
  @singleton
  NetworkService networkService(
    Dio dio,
    EnvironmentConfig config,
  ) {
    // 设置基础URL
    dio.options.baseUrl = config.apiBaseUrl;
    
    // 添加拦截器
    dio.interceptors.addAll([
      AuthInterceptor(),
      RetryInterceptor(maxRetries: config.maxRetryAttempts),
      CacheInterceptor(timeout: config.cacheTimeout),
      ErrorInterceptor(),
    ]);
    
    return NetworkServiceImpl(dio);
  }

  /// 注册认证拦截器
  @singleton
  AuthInterceptor authInterceptor(
    StorageService storageService,
  ) {
    return AuthInterceptor(storageService);
  }

  /// 注册重试拦截器
  @singleton
  RetryInterceptor retryInterceptor(
    EnvironmentConfig config,
  ) {
    return RetryInterceptor(
      maxRetries: config.maxRetryAttempts,
    );
  }

  /// 注册缓存拦截器
  @singleton
  CacheInterceptor cacheInterceptor(
    EnvironmentConfig config,
  ) {
    return CacheInterceptor(
      timeout: config.cacheTimeout,
    );
  }

  /// 注册错误拦截器
  @singleton
  ErrorInterceptor errorInterceptor() {
    return ErrorInterceptor();
  }
}
```

### 存储模块
```dart
// packages/core/core_storage/lib/src/di/storage_module.dart
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:drift/drift.dart';
import '../services/storage_service.dart';
import '../services/secure_storage_service.dart';
import '../services/cache_service.dart';
import '../database/app_database.dart';

/// 存储模块依赖注入
@module
abstract class StorageModule {
  /// 注册存储服务
  @singleton
  StorageService storageService(
    SharedPreferences sharedPreferences,
  ) {
    return StorageServiceImpl(sharedPreferences);
  }

  /// 注册安全存储服务
  @singleton
  SecureStorageService secureStorageService(
    FlutterSecureStorage secureStorage,
  ) {
    return SecureStorageServiceImpl(secureStorage);
  }

  /// 注册缓存服务
  @singleton
  CacheService cacheService(
    SharedPreferences sharedPreferences,
  ) {
    return CacheServiceImpl(sharedPreferences);
  }

  /// 注册数据库
  @singleton
  AppDatabase appDatabase() {
    return AppDatabase();
  }

  /// 注册用户DAO
  @singleton
  UserDao userDao(AppDatabase database) {
    return database.userDao;
  }

  /// 注册认证DAO
  @singleton
  AuthDao authDao(AppDatabase database) {
    return database.authDao;
  }
}
```

### 平台模块
```dart
// packages/core/core_platform/lib/src/di/platform_module.dart
import 'package:injectable/injectable.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:local_auth/local_auth.dart';
import 'dart:io';
import '../services/platform_service.dart';
import '../services/device_service.dart';
import '../services/connectivity_service.dart';
import '../services/biometric_service.dart';

/// 平台模块依赖注入
@module
abstract class PlatformModule {
  /// 注册平台服务
  @singleton
  PlatformService platformService(
    DeviceService deviceService,
    ConnectivityService connectivityService,
    BiometricService biometricService,
  ) {
    if (Platform.isIOS) {
      return IOSPlatformService(
        deviceService,
        connectivityService,
        biometricService,
      );
    } else if (Platform.isAndroid) {
      return AndroidPlatformService(
        deviceService,
        connectivityService,
        biometricService,
      );
    } else {
      throw UnsupportedError('Platform not supported');
    }
  }

  /// 注册设备服务
  @singleton
  DeviceService deviceService(
    DeviceInfoPlugin deviceInfo,
    PackageInfo packageInfo,
  ) {
    return DeviceServiceImpl(deviceInfo, packageInfo);
  }

  /// 注册连接服务
  @singleton
  ConnectivityService connectivityService(
    Connectivity connectivity,
  ) {
    return ConnectivityServiceImpl(connectivity);
  }

  /// 注册生物识别服务
  @singleton
  BiometricService biometricService(
    LocalAuthentication localAuth,
  ) {
    return BiometricServiceImpl(localAuth);
  }
}
```

## 3. 功能模块依赖注入

### 认证模块
```dart
// packages/features/feature_auth/lib/src/di/auth_module.dart
import 'package:injectable/injectable.dart';
import 'package:core_network/core_network.dart';
import 'package:core_storage/core_storage.dart';
import '../data/datasources/auth_remote_datasource.dart';
import '../data/datasources/auth_local_datasource.dart';
import '../data/repositories/auth_repository_impl.dart';
import '../domain/repositories/auth_repository.dart';
import '../domain/usecases/login_usecase.dart';
import '../domain/usecases/register_usecase.dart';
import '../domain/usecases/logout_usecase.dart';
import '../domain/usecases/refresh_token_usecase.dart';
import '../domain/usecases/check_auth_usecase.dart';
import '../domain/usecases/forgot_password_usecase.dart';
import '../domain/usecases/reset_password_usecase.dart';
import '../presentation/bloc/auth_bloc.dart';

/// 认证模块依赖注入
@module
abstract class AuthModule {
  // Data Sources
  @singleton
  AuthRemoteDataSource authRemoteDataSource(
    NetworkService networkService,
  ) {
    return AuthRemoteDataSourceImpl(networkService);
  }

  @singleton
  AuthLocalDataSource authLocalDataSource(
    SecureStorageService secureStorage,
    AuthDao authDao,
  ) {
    return AuthLocalDataSourceImpl(secureStorage, authDao);
  }

  // Repository
  @singleton
  AuthRepository authRepository(
    AuthRemoteDataSource remoteDataSource,
    AuthLocalDataSource localDataSource,
    ConnectivityService connectivityService,
  ) {
    return AuthRepositoryImpl(
      remoteDataSource,
      localDataSource,
      connectivityService,
    );
  }

  // Use Cases
  @singleton
  LoginUseCase loginUseCase(AuthRepository repository) {
    return LoginUseCase(repository);
  }

  @singleton
  RegisterUseCase registerUseCase(AuthRepository repository) {
    return RegisterUseCase(repository);
  }

  @singleton
  LogoutUseCase logoutUseCase(AuthRepository repository) {
    return LogoutUseCase(repository);
  }

  @singleton
  RefreshTokenUseCase refreshTokenUseCase(AuthRepository repository) {
    return RefreshTokenUseCase(repository);
  }

  @singleton
  CheckAuthUseCase checkAuthUseCase(AuthRepository repository) {
    return CheckAuthUseCase(repository);
  }

  @singleton
  ForgotPasswordUseCase forgotPasswordUseCase(AuthRepository repository) {
    return ForgotPasswordUseCase(repository);
  }

  @singleton
  ResetPasswordUseCase resetPasswordUseCase(AuthRepository repository) {
    return ResetPasswordUseCase(repository);
  }

  // BLoC
  @factory
  AuthBloc authBloc(
    LoginUseCase loginUseCase,
    RegisterUseCase registerUseCase,
    LogoutUseCase logoutUseCase,
    RefreshTokenUseCase refreshTokenUseCase,
    ForgotPasswordUseCase forgotPasswordUseCase,
    ResetPasswordUseCase resetPasswordUseCase,
    CheckAuthUseCase checkAuthUseCase,
  ) {
    return AuthBloc(
      loginUseCase,
      registerUseCase,
      logoutUseCase,
      refreshTokenUseCase,
      forgotPasswordUseCase,
      resetPasswordUseCase,
      checkAuthUseCase,
    );
  }
}
```

### 用户模块
```dart
// packages/features/feature_user/lib/src/di/user_module.dart
import 'package:injectable/injectable.dart';
import 'package:core_network/core_network.dart';
import 'package:core_storage/core_storage.dart';
import '../data/datasources/user_remote_datasource.dart';
import '../data/datasources/user_local_datasource.dart';
import '../data/repositories/user_repository_impl.dart';
import '../domain/repositories/user_repository.dart';
import '../domain/usecases/get_user_profile_usecase.dart';
import '../domain/usecases/update_user_profile_usecase.dart';
import '../domain/usecases/upload_avatar_usecase.dart';
import '../domain/usecases/change_password_usecase.dart';
import '../domain/usecases/delete_account_usecase.dart';
import '../presentation/bloc/user_bloc.dart';

/// 用户模块依赖注入
@module
abstract class UserModule {
  // Data Sources
  @singleton
  UserRemoteDataSource userRemoteDataSource(
    NetworkService networkService,
  ) {
    return UserRemoteDataSourceImpl(networkService);
  }

  @singleton
  UserLocalDataSource userLocalDataSource(
    UserDao userDao,
    CacheService cacheService,
  ) {
    return UserLocalDataSourceImpl(userDao, cacheService);
  }

  // Repository
  @singleton
  UserRepository userRepository(
    UserRemoteDataSource remoteDataSource,
    UserLocalDataSource localDataSource,
    ConnectivityService connectivityService,
  ) {
    return UserRepositoryImpl(
      remoteDataSource,
      localDataSource,
      connectivityService,
    );
  }

  // Use Cases
  @singleton
  GetUserProfileUseCase getUserProfileUseCase(UserRepository repository) {
    return GetUserProfileUseCase(repository);
  }

  @singleton
  UpdateUserProfileUseCase updateUserProfileUseCase(UserRepository repository) {
    return UpdateUserProfileUseCase(repository);
  }

  @singleton
  UploadAvatarUseCase uploadAvatarUseCase(UserRepository repository) {
    return UploadAvatarUseCase(repository);
  }

  @singleton
  ChangePasswordUseCase changePasswordUseCase(UserRepository repository) {
    return ChangePasswordUseCase(repository);
  }

  @singleton
  DeleteAccountUseCase deleteAccountUseCase(UserRepository repository) {
    return DeleteAccountUseCase(repository);
  }

  // BLoC
  @factory
  UserBloc userBloc(
    GetUserProfileUseCase getUserProfileUseCase,
    UpdateUserProfileUseCase updateUserProfileUseCase,
    UploadAvatarUseCase uploadAvatarUseCase,
    ChangePasswordUseCase changePasswordUseCase,
    DeleteAccountUseCase deleteAccountUseCase,
  ) {
    return UserBloc(
      getUserProfileUseCase,
      updateUserProfileUseCase,
      uploadAvatarUseCase,
      changePasswordUseCase,
      deleteAccountUseCase,
    );
  }
}
```

## 4. 测试配置

### 测试依赖注入
```dart
// packages/core/core_di/lib/src/test_injection.dart
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:mockito/mockito.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:dio/dio.dart';
import 'injection.dart';

/// 测试环境依赖注入配置
class TestDependencyInjection {
  static Future<void> configure() async {
    // 重置现有配置
    await getIt.reset();

    // 注册Mock依赖
    await _registerMockDependencies();

    // 配置测试环境
    await configureDependencies(environment: Environment.test);
  }

  /// 注册Mock依赖
  static Future<void> _registerMockDependencies() async {
    // Mock SharedPreferences
    SharedPreferences.setMockInitialValues({});
    final sharedPreferences = await SharedPreferences.getInstance();
    getIt.registerSingleton<SharedPreferences>(sharedPreferences);

    // Mock FlutterSecureStorage
    getIt.registerSingleton<FlutterSecureStorage>(
      MockFlutterSecureStorage(),
    );

    // Mock Dio
    getIt.registerSingleton<Dio>(MockDio());
  }
}

/// Mock FlutterSecureStorage
class MockFlutterSecureStorage extends Mock implements FlutterSecureStorage {
  final Map<String, String> _storage = {};

  @override
  Future<void> write({
    required String key,
    required String? value,
    IOSOptions? iOptions,
    AndroidOptions? aOptions,
    LinuxOptions? lOptions,
    WebOptions? webOptions,
    MacOsOptions? macOsOptions,
    WindowsOptions? windowsOptions,
  }) async {
    if (value != null) {
      _storage[key] = value;
    } else {
      _storage.remove(key);
    }
  }

  @override
  Future<String?> read({
    required String key,
    IOSOptions? iOptions,
    AndroidOptions? aOptions,
    LinuxOptions? lOptions,
    WebOptions? webOptions,
    MacOsOptions? macOsOptions,
    WindowsOptions? windowsOptions,
  }) async {
    return _storage[key];
  }

  @override
  Future<void> delete({
    required String key,
    IOSOptions? iOptions,
    AndroidOptions? aOptions,
    LinuxOptions? lOptions,
    WebOptions? webOptions,
    MacOsOptions? macOsOptions,
    WindowsOptions? windowsOptions,
  }) async {
    _storage.remove(key);
  }

  @override
  Future<void> deleteAll({
    IOSOptions? iOptions,
    AndroidOptions? aOptions,
    LinuxOptions? lOptions,
    WebOptions? webOptions,
    MacOsOptions? macOsOptions,
    WindowsOptions? windowsOptions,
  }) async {
    _storage.clear();
  }

  @override
  Future<Map<String, String>> readAll({
    IOSOptions? iOptions,
    AndroidOptions? aOptions,
    LinuxOptions? lOptions,
    WebOptions? webOptions,
    MacOsOptions? macOsOptions,
    WindowsOptions? windowsOptions,
  }) async {
    return Map.from(_storage);
  }

  @override
  Future<bool> containsKey({
    required String key,
    IOSOptions? iOptions,
    AndroidOptions? aOptions,
    LinuxOptions? lOptions,
    WebOptions? webOptions,
    MacOsOptions? macOsOptions,
    WindowsOptions? windowsOptions,
  }) async {
    return _storage.containsKey(key);
  }
}

/// Mock Dio
class MockDio extends Mock implements Dio {
  @override
  BaseOptions options = BaseOptions();

  @override
  Interceptors interceptors = Interceptors();

  @override
  Future<Response<T>> get<T>(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    // 返回模拟响应
    return Response<T>(
      data: {} as T,
      statusCode: 200,
      requestOptions: RequestOptions(path: path),
    );
  }

  @override
  Future<Response<T>> post<T>(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    // 返回模拟响应
    return Response<T>(
      data: {} as T,
      statusCode: 200,
      requestOptions: RequestOptions(path: path),
    );
  }
}
```

## 5. 应用启动配置

### 主应用启动
```dart
// lib/main.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:core_di/core_di.dart';
import 'package:ui_kit/ui_kit.dart';
import 'app.dart';
import 'app_bloc_observer.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 配置BLoC观察者
  Bloc.observer = AppBlocObserver();

  // 配置依赖注入
  await configureDependencies(
    environment: const String.fromEnvironment(
      'ENVIRONMENT',
      defaultValue: Environment.dev,
    ),
  );

  runApp(const MyApp());
}
```

### 应用BLoC观察者
```dart
// lib/app_bloc_observer.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:core_di/core_di.dart';

/// 应用BLoC观察者
class AppBlocObserver extends BlocObserver {
  @override
  void onCreate(BlocBase bloc) {
    super.onCreate(bloc);
    if (isDevelopment) {
      print('🟢 BLoC Created: ${bloc.runtimeType}');
    }
  }

  @override
  void onEvent(BlocBase bloc, Object? event) {
    super.onEvent(bloc, event);
    if (isDevelopment) {
      print('🔵 BLoC Event: ${bloc.runtimeType} - $event');
    }
  }

  @override
  void onTransition(BlocBase bloc, Transition transition) {
    super.onTransition(bloc, transition);
    if (isDevelopment) {
      print('🟡 BLoC Transition: ${bloc.runtimeType} - $transition');
    }
  }

  @override
  void onError(BlocBase bloc, Object error, StackTrace stackTrace) {
    super.onError(bloc, error, stackTrace);
    print('🔴 BLoC Error: ${bloc.runtimeType} - $error');
    
    // 在生产环境中，可以将错误发送到崩溃报告服务
    if (isProduction) {
      // CrashlyticsService.recordError(error, stackTrace);
    }
  }

  @override
  void onClose(BlocBase bloc) {
    super.onClose(bloc);
    if (isDevelopment) {
      print('🔴 BLoC Closed: ${bloc.runtimeType}');
    }
  }
}
```

这个依赖注入配置实现展示了：

1. **模块化设计**：按功能模块组织依赖注入
2. **环境配置**：支持多环境配置和特性开关
3. **第三方集成**：统一管理第三方库依赖
4. **测试支持**：提供测试环境的Mock配置
5. **生命周期管理**：正确的依赖创建和销毁
6. **错误处理**：依赖注入验证和错误报告

所有配置都遵循SOLID原则，确保代码的可维护性和可测试性。