# 测试框架实现示例

## 1. 测试基础架构

### 测试配置和工具类
```dart
// test/helpers/test_config.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:get_it/get_it.dart';
import 'package:core_config/core_config.dart';
import 'package:core_error/core_error.dart';
import 'package:core_network/core_network.dart';
import 'package:core_storage/core_storage.dart';

/// 测试环境配置
class TestConfig {
  static const String testApiBaseUrl = 'https://test-api.example.com';
  static const String testDatabaseName = 'test_database';
  static const Duration testTimeout = Duration(seconds: 5);
  
  static AppConfig get appConfig => TestAppConfig();
}

class TestAppConfig implements AppConfig {
  @override
  Environment get environment => Environment.test;
  
  @override
  String get apiBaseUrl => TestConfig.testApiBaseUrl;
  
  @override
  String get databaseName => TestConfig.testDatabaseName;
  
  @override
  bool get enableLogging => false;
  
  @override
  bool get enableCrashReporting => false;
  
  @override
  bool get enableAnalytics => false;
  
  @override
  String get logLevel => 'error';
  
  @override
  Duration get cacheTimeout => const Duration(minutes: 5);
  
  @override
  String get appVersion => '1.0.0-test';
  
  @override
  Map<String, bool> get featureFlags => {
    'new_ui': true,
    'beta_features': false,
  };
}

/// 测试工具类
class TestHelper {
  /// 设置测试环境
  static Future<void> setupTestEnvironment() async {
    // 重置GetIt
    if (GetIt.instance.isRegistered<AppConfig>()) {
      await GetIt.instance.reset();
    }
    
    // 注册测试配置
    GetIt.instance.registerSingleton<AppConfig>(TestConfig.appConfig);
  }
  
  /// 清理测试环境
  static Future<void> tearDownTestEnvironment() async {
    await GetIt.instance.reset();
  }
  
  /// 创建测试用的Future延迟
  static Future<T> delay<T>(T value, [Duration? duration]) async {
    await Future.delayed(duration ?? const Duration(milliseconds: 100));
    return value;
  }
  
  /// 创建测试用的Stream
  static Stream<T> createTestStream<T>(List<T> values, [Duration? interval]) async* {
    for (final value in values) {
      await Future.delayed(interval ?? const Duration(milliseconds: 50));
      yield value;
    }
  }
  
  /// 验证异步操作
  static Future<void> verifyAsync(Future<void> Function() operation) async {
    await operation();
    await Future.delayed(const Duration(milliseconds: 10));
  }
}

/// 测试数据工厂
class TestDataFactory {
  static UserEntity createUser({
    String? id,
    String? email,
    String? name,
    String? avatar,
    bool? isFollowing,
  }) {
    return UserEntity(
      id: id ?? 'user_${DateTime.now().millisecondsSinceEpoch}',
      email: email ?? '<EMAIL>',
      name: name ?? 'Test User',
      avatar: avatar,
      isFollowing: isFollowing ?? false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
  
  static AuthEntity createAuth({
    String? accessToken,
    String? refreshToken,
    UserEntity? user,
    DateTime? expiresAt,
  }) {
    return AuthEntity(
      accessToken: accessToken ?? 'test_access_token',
      refreshToken: refreshToken ?? 'test_refresh_token',
      user: user ?? createUser(),
      expiresAt: expiresAt ?? DateTime.now().add(const Duration(hours: 1)),
    );
  }
  
  static PaginatedResponse<T> createPaginatedResponse<T>({
    required List<T> data,
    int? page,
    int? perPage,
    int? total,
    bool? hasMore,
  }) {
    return PaginatedResponse<T>(
      data: data,
      page: page ?? 1,
      perPage: perPage ?? 20,
      total: total ?? data.length,
      hasMore: hasMore ?? false,
    );
  }
}
```

### Mock生成器配置
```dart
// test/helpers/mocks.dart
import 'package:mockito/annotations.dart';
import 'package:dio/dio.dart';
import 'package:core_network/core_network.dart';
import 'package:core_storage/core_storage.dart';
import 'package:core_error/core_error.dart';
import 'package:features_auth/features_auth.dart';
import 'package:features_user/features_user.dart';

// 生成Mock类的注解
@GenerateMocks([
  // Network
  Dio,
  ApiService,
  NetworkMonitor,
  
  // Storage
  SecureStorageService,
  CacheStorageService,
  AppDatabase,
  
  // Error Handling
  ErrorHandler,
  LoggerService,
  CrashReportingService,
  
  // Auth
  AuthRepository,
  AuthRemoteDataSource,
  AuthLocalDataSource,
  LoginUseCase,
  LogoutUseCase,
  RefreshTokenUseCase,
  
  // User
  UserRepository,
  UserRemoteDataSource,
  UserLocalDataSource,
  GetUserUseCase,
  GetUserListUseCase,
  FollowUserUseCase,
])
void main() {}
```

### 测试基类
```dart
// test/helpers/base_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:get_it/get_it.dart';
import 'test_config.dart';
import 'mocks.mocks.dart';

/// 单元测试基类
abstract class BaseUnitTest {
  late MockErrorHandler mockErrorHandler;
  late MockLoggerService mockLoggerService;
  
  @mustCallSuper
  void setUp() {
    TestHelper.setupTestEnvironment();
    
    // 创建通用Mock对象
    mockErrorHandler = MockErrorHandler();
    mockLoggerService = MockLoggerService();
    
    // 注册通用依赖
    GetIt.instance.registerSingleton<ErrorHandler>(mockErrorHandler);
    GetIt.instance.registerSingleton<LoggerService>(mockLoggerService);
    
    // 设置默认行为
    _setupDefaultMockBehavior();
  }
  
  @mustCallSuper
  void tearDown() {
    TestHelper.tearDownTestEnvironment();
  }
  
  void _setupDefaultMockBehavior() {
    // ErrorHandler默认行为
    when(mockErrorHandler.handleError(any, any))
        .thenReturn(const UnknownFailure(message: 'Test error'));
    
    // LoggerService默认行为
    when(mockLoggerService.debug(any, error: anyNamed('error'), stackTrace: anyNamed('stackTrace'), extra: anyNamed('extra')))
        .thenReturn(null);
    when(mockLoggerService.info(any, error: anyNamed('error'), stackTrace: anyNamed('stackTrace'), extra: anyNamed('extra')))
        .thenReturn(null);
    when(mockLoggerService.warning(any, error: anyNamed('error'), stackTrace: anyNamed('stackTrace'), extra: anyNamed('extra')))
        .thenReturn(null);
    when(mockLoggerService.error(any, error: anyNamed('error'), stackTrace: anyNamed('stackTrace'), extra: anyNamed('extra')))
        .thenReturn(null);
  }
}

/// Widget测试基类
abstract class BaseWidgetTest {
  late MockAuthBloc mockAuthBloc;
  late MockUserBloc mockUserBloc;
  
  @mustCallSuper
  void setUp() {
    TestHelper.setupTestEnvironment();
    
    // 创建BLoC Mock对象
    mockAuthBloc = MockAuthBloc();
    mockUserBloc = MockUserBloc();
    
    // 设置默认状态
    _setupDefaultBlocStates();
  }
  
  @mustCallSuper
  void tearDown() {
    TestHelper.tearDownTestEnvironment();
  }
  
  void _setupDefaultBlocStates() {
    // AuthBloc默认状态
    when(mockAuthBloc.state).thenReturn(const AuthInitial());
    when(mockAuthBloc.stream).thenAnswer((_) => Stream.value(const AuthInitial()));
    
    // UserBloc默认状态
    when(mockUserBloc.state).thenReturn(const UserInitial());
    when(mockUserBloc.stream).thenAnswer((_) => Stream.value(const UserInitial()));
  }
  
  /// 创建测试Widget包装器
  Widget createTestWidget(Widget child) {
    return MaterialApp(
      home: MultiBlocProvider(
        providers: [
          BlocProvider<AuthBloc>.value(value: mockAuthBloc),
          BlocProvider<UserBloc>.value(value: mockUserBloc),
        ],
        child: child,
      ),
    );
  }
}

/// 集成测试基类
abstract class BaseIntegrationTest {
  @mustCallSuper
  void setUp() {
    TestHelper.setupTestEnvironment();
  }
  
  @mustCallSuper
  void tearDown() {
    TestHelper.tearDownTestEnvironment();
  }
}
```

## 2. 单元测试示例

### UseCase测试
```dart
// test/features/auth/domain/usecases/login_usecase_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:dartz/dartz.dart';
import 'package:features_auth/features_auth.dart';
import '../../../helpers/base_test.dart';
import '../../../helpers/mocks.mocks.dart';
import '../../../helpers/test_config.dart';

void main() {
  group('LoginUseCase', () {
    late LoginUseCase useCase;
    late MockAuthRepository mockRepository;
    late MockErrorHandler mockErrorHandler;
    
    setUp(() {
      TestHelper.setupTestEnvironment();
      mockRepository = MockAuthRepository();
      mockErrorHandler = MockErrorHandler();
      useCase = LoginUseCase(mockRepository, mockErrorHandler);
    });
    
    tearDown(() {
      TestHelper.tearDownTestEnvironment();
    });
    
    group('call', () {
      const email = '<EMAIL>';
      const password = 'password123';
      final authEntity = TestDataFactory.createAuth();
      
      test('should return AuthEntity when login is successful', () async {
        // Arrange
        when(mockRepository.login(email, password))
            .thenAnswer((_) async => Right(authEntity));
        
        // Act
        final result = await useCase(const LoginParams(
          email: email,
          password: password,
        ));
        
        // Assert
        expect(result, Right(authEntity));
        verify(mockRepository.login(email, password)).called(1);
        verifyNoMoreInteractions(mockRepository);
      });
      
      test('should return Failure when login fails', () async {
        // Arrange
        const failure = AuthFailure(
          message: 'Invalid credentials',
          code: 'invalid_credentials',
        );
        when(mockRepository.login(email, password))
            .thenAnswer((_) async => const Left(failure));
        
        // Act
        final result = await useCase(const LoginParams(
          email: email,
          password: password,
        ));
        
        // Assert
        expect(result, const Left(failure));
        verify(mockRepository.login(email, password)).called(1);
      });
      
      test('should handle exceptions and return Failure', () async {
        // Arrange
        final exception = Exception('Network error');
        final expectedFailure = NetworkFailure(
          message: 'Network error',
          code: 'network_error',
        );
        
        when(mockRepository.login(email, password))
            .thenThrow(exception);
        when(mockErrorHandler.handleError(exception, any))
            .thenReturn(expectedFailure);
        
        // Act
        final result = await useCase(const LoginParams(
          email: email,
          password: password,
        ));
        
        // Assert
        expect(result, Left(expectedFailure));
        verify(mockRepository.login(email, password)).called(1);
        verify(mockErrorHandler.handleError(exception, any)).called(1);
      });
      
      test('should validate email format', () async {
        // Arrange
        const invalidEmail = 'invalid-email';
        
        // Act
        final result = await useCase(const LoginParams(
          email: invalidEmail,
          password: password,
        ));
        
        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (failure) {
            expect(failure, isA<ValidationFailure>());
            expect(failure.message, contains('email'));
          },
          (_) => fail('Should return validation failure'),
        );
        
        verifyNever(mockRepository.login(any, any));
      });
      
      test('should validate password length', () async {
        // Arrange
        const shortPassword = '123';
        
        // Act
        final result = await useCase(const LoginParams(
          email: email,
          password: shortPassword,
        ));
        
        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (failure) {
            expect(failure, isA<ValidationFailure>());
            expect(failure.message, contains('password'));
          },
          (_) => fail('Should return validation failure'),
        );
        
        verifyNever(mockRepository.login(any, any));
      });
    });
  });
}
```

### Repository测试
```dart
// test/features/auth/data/repositories/auth_repository_impl_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:dartz/dartz.dart';
import 'package:features_auth/features_auth.dart';
import '../../../helpers/base_test.dart';
import '../../../helpers/mocks.mocks.dart';
import '../../../helpers/test_config.dart';

void main() {
  group('AuthRepositoryImpl', () {
    late AuthRepositoryImpl repository;
    late MockAuthRemoteDataSource mockRemoteDataSource;
    late MockAuthLocalDataSource mockLocalDataSource;
    late MockNetworkMonitor mockNetworkMonitor;
    late MockErrorHandler mockErrorHandler;
    
    setUp(() {
      TestHelper.setupTestEnvironment();
      mockRemoteDataSource = MockAuthRemoteDataSource();
      mockLocalDataSource = MockAuthLocalDataSource();
      mockNetworkMonitor = MockNetworkMonitor();
      mockErrorHandler = MockErrorHandler();
      
      repository = AuthRepositoryImpl(
        mockRemoteDataSource,
        mockLocalDataSource,
        mockNetworkMonitor,
        mockErrorHandler,
      );
    });
    
    tearDown(() {
      TestHelper.tearDownTestEnvironment();
    });
    
    group('login', () {
      const email = '<EMAIL>';
      const password = 'password123';
      final authModel = AuthModel(
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
        user: UserModel(
          id: 'user_id',
          email: email,
          name: 'Test User',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
      );
      final authEntity = authModel.toEntity();
      
      test('should return AuthEntity when login is successful', () async {
        // Arrange
        when(mockNetworkMonitor.isConnected).thenReturn(true);
        when(mockRemoteDataSource.login(email, password))
            .thenAnswer((_) async => authModel);
        when(mockLocalDataSource.saveAuth(authModel))
            .thenAnswer((_) async {});
        
        // Act
        final result = await repository.login(email, password);
        
        // Assert
        expect(result, Right(authEntity));
        verify(mockRemoteDataSource.login(email, password)).called(1);
        verify(mockLocalDataSource.saveAuth(authModel)).called(1);
      });
      
      test('should return NetworkFailure when no internet connection', () async {
        // Arrange
        when(mockNetworkMonitor.isConnected).thenReturn(false);
        
        // Act
        final result = await repository.login(email, password);
        
        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (failure) => expect(failure, isA<NetworkFailure>()),
          (_) => fail('Should return NetworkFailure'),
        );
        
        verifyNever(mockRemoteDataSource.login(any, any));
        verifyNever(mockLocalDataSource.saveAuth(any));
      });
      
      test('should handle remote data source exceptions', () async {
        // Arrange
        final exception = Exception('Server error');
        final expectedFailure = ServerFailure(
          message: 'Server error',
          statusCode: 500,
        );
        
        when(mockNetworkMonitor.isConnected).thenReturn(true);
        when(mockRemoteDataSource.login(email, password))
            .thenThrow(exception);
        when(mockErrorHandler.handleError(exception, any))
            .thenReturn(expectedFailure);
        
        // Act
        final result = await repository.login(email, password);
        
        // Assert
        expect(result, Left(expectedFailure));
        verify(mockRemoteDataSource.login(email, password)).called(1);
        verify(mockErrorHandler.handleError(exception, any)).called(1);
        verifyNever(mockLocalDataSource.saveAuth(any));
      });
      
      test('should handle local data source save failure', () async {
        // Arrange
        final exception = Exception('Storage error');
        final expectedFailure = StorageFailure(
          message: 'Storage error',
          storageType: 'local',
        );
        
        when(mockNetworkMonitor.isConnected).thenReturn(true);
        when(mockRemoteDataSource.login(email, password))
            .thenAnswer((_) async => authModel);
        when(mockLocalDataSource.saveAuth(authModel))
            .thenThrow(exception);
        when(mockErrorHandler.handleError(exception, any))
            .thenReturn(expectedFailure);
        
        // Act
        final result = await repository.login(email, password);
        
        // Assert
        expect(result, Left(expectedFailure));
        verify(mockRemoteDataSource.login(email, password)).called(1);
        verify(mockLocalDataSource.saveAuth(authModel)).called(1);
        verify(mockErrorHandler.handleError(exception, any)).called(1);
      });
    });
    
    group('getCurrentAuth', () {
      final authModel = AuthModel(
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
        user: UserModel(
          id: 'user_id',
          email: '<EMAIL>',
          name: 'Test User',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
      );
      final authEntity = authModel.toEntity();
      
      test('should return AuthEntity when auth exists locally', () async {
        // Arrange
        when(mockLocalDataSource.getCurrentAuth())
            .thenAnswer((_) async => authModel);
        
        // Act
        final result = await repository.getCurrentAuth();
        
        // Assert
        expect(result, Right(authEntity));
        verify(mockLocalDataSource.getCurrentAuth()).called(1);
      });
      
      test('should return null when no auth exists locally', () async {
        // Arrange
        when(mockLocalDataSource.getCurrentAuth())
            .thenAnswer((_) async => null);
        
        // Act
        final result = await repository.getCurrentAuth();
        
        // Assert
        expect(result, const Right(null));
        verify(mockLocalDataSource.getCurrentAuth()).called(1);
      });
      
      test('should handle local data source exceptions', () async {
        // Arrange
        final exception = Exception('Storage error');
        final expectedFailure = StorageFailure(
          message: 'Storage error',
          storageType: 'local',
        );
        
        when(mockLocalDataSource.getCurrentAuth())
            .thenThrow(exception);
        when(mockErrorHandler.handleError(exception, any))
            .thenReturn(expectedFailure);
        
        // Act
        final result = await repository.getCurrentAuth();
        
        // Assert
        expect(result, Left(expectedFailure));
        verify(mockLocalDataSource.getCurrentAuth()).called(1);
        verify(mockErrorHandler.handleError(exception, any)).called(1);
      });
    });
  });
}
```

### BLoC测试
```dart
// test/features/auth/presentation/bloc/auth_bloc_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:features_auth/features_auth.dart';
import '../../../helpers/base_test.dart';
import '../../../helpers/mocks.mocks.dart';
import '../../../helpers/test_config.dart';

void main() {
  group('AuthBloc', () {
    late AuthBloc authBloc;
    late MockLoginUseCase mockLoginUseCase;
    late MockLogoutUseCase mockLogoutUseCase;
    late MockRefreshTokenUseCase mockRefreshTokenUseCase;
    late MockGetCurrentAuthUseCase mockGetCurrentAuthUseCase;
    
    setUp(() {
      TestHelper.setupTestEnvironment();
      mockLoginUseCase = MockLoginUseCase();
      mockLogoutUseCase = MockLogoutUseCase();
      mockRefreshTokenUseCase = MockRefreshTokenUseCase();
      mockGetCurrentAuthUseCase = MockGetCurrentAuthUseCase();
      
      authBloc = AuthBloc(
        mockLoginUseCase,
        mockLogoutUseCase,
        mockRefreshTokenUseCase,
        mockGetCurrentAuthUseCase,
      );
    });
    
    tearDown(() {
      authBloc.close();
      TestHelper.tearDownTestEnvironment();
    });
    
    test('initial state should be AuthInitial', () {
      expect(authBloc.state, const AuthInitial());
    });
    
    group('AuthCheckRequested', () {
      final authEntity = TestDataFactory.createAuth();
      
      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthAuthenticated] when auth check succeeds',
        build: () {
          when(mockGetCurrentAuthUseCase(NoParams()))
              .thenAnswer((_) async => Right(authEntity));
          return authBloc;
        },
        act: (bloc) => bloc.add(const AuthCheckRequested()),
        expect: () => [
          const AuthLoading(),
          AuthAuthenticated(authEntity),
        ],
        verify: (_) {
          verify(mockGetCurrentAuthUseCase(NoParams())).called(1);
        },
      );
      
      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthUnauthenticated] when no auth exists',
        build: () {
          when(mockGetCurrentAuthUseCase(NoParams()))
              .thenAnswer((_) async => const Right(null));
          return authBloc;
        },
        act: (bloc) => bloc.add(const AuthCheckRequested()),
        expect: () => [
          const AuthLoading(),
          const AuthUnauthenticated(),
        ],
      );
      
      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthError] when auth check fails',
        build: () {
          const failure = StorageFailure(
            message: 'Storage error',
            storageType: 'local',
          );
          when(mockGetCurrentAuthUseCase(NoParams()))
              .thenAnswer((_) async => const Left(failure));
          return authBloc;
        },
        act: (bloc) => bloc.add(const AuthCheckRequested()),
        expect: () => [
          const AuthLoading(),
          const AuthError(StorageFailure(
            message: 'Storage error',
            storageType: 'local',
          )),
        ],
      );
    });
    
    group('AuthLoginRequested', () {
      const email = '<EMAIL>';
      const password = 'password123';
      final authEntity = TestDataFactory.createAuth();
      
      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthAuthenticated] when login succeeds',
        build: () {
          when(mockLoginUseCase(const LoginParams(
            email: email,
            password: password,
          ))).thenAnswer((_) async => Right(authEntity));
          return authBloc;
        },
        act: (bloc) => bloc.add(const AuthLoginRequested(
          email: email,
          password: password,
        )),
        expect: () => [
          const AuthLoading(),
          AuthAuthenticated(authEntity),
        ],
        verify: (_) {
          verify(mockLoginUseCase(const LoginParams(
            email: email,
            password: password,
          ))).called(1);
        },
      );
      
      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthError] when login fails',
        build: () {
          const failure = AuthFailure(
            message: 'Invalid credentials',
            code: 'invalid_credentials',
          );
          when(mockLoginUseCase(const LoginParams(
            email: email,
            password: password,
          ))).thenAnswer((_) async => const Left(failure));
          return authBloc;
        },
        act: (bloc) => bloc.add(const AuthLoginRequested(
          email: email,
          password: password,
        )),
        expect: () => [
          const AuthLoading(),
          const AuthError(AuthFailure(
            message: 'Invalid credentials',
            code: 'invalid_credentials',
          )),
        ],
      );
    });
    
    group('AuthLogoutRequested', () {
      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthUnauthenticated] when logout succeeds',
        build: () {
          when(mockLogoutUseCase(NoParams()))
              .thenAnswer((_) async => const Right(null));
          return authBloc;
        },
        seed: () => AuthAuthenticated(TestDataFactory.createAuth()),
        act: (bloc) => bloc.add(const AuthLogoutRequested()),
        expect: () => [
          const AuthLoading(),
          const AuthUnauthenticated(),
        ],
        verify: (_) {
          verify(mockLogoutUseCase(NoParams())).called(1);
        },
      );
      
      blocTest<AuthBloc, AuthState>(
        'should emit [AuthLoading, AuthError] when logout fails',
        build: () {
          const failure = NetworkFailure(
            message: 'Network error',
            code: 'network_error',
          );
          when(mockLogoutUseCase(NoParams()))
              .thenAnswer((_) async => const Left(failure));
          return authBloc;
        },
        seed: () => AuthAuthenticated(TestDataFactory.createAuth()),
        act: (bloc) => bloc.add(const AuthLogoutRequested()),
        expect: () => [
          const AuthLoading(),
          const AuthError(NetworkFailure(
            message: 'Network error',
            code: 'network_error',
          )),
        ],
      );
    });
    
    group('AuthTokenRefreshRequested', () {
      final authEntity = TestDataFactory.createAuth();
      
      blocTest<AuthBloc, AuthState>(
        'should emit [AuthAuthenticated] when token refresh succeeds',
        build: () {
          when(mockRefreshTokenUseCase(NoParams()))
              .thenAnswer((_) async => Right(authEntity));
          return authBloc;
        },
        seed: () => AuthAuthenticated(TestDataFactory.createAuth()),
        act: (bloc) => bloc.add(const AuthTokenRefreshRequested()),
        expect: () => [
          AuthAuthenticated(authEntity),
        ],
        verify: (_) {
          verify(mockRefreshTokenUseCase(NoParams())).called(1);
        },
      );
      
      blocTest<AuthBloc, AuthState>(
        'should emit [AuthUnauthenticated] when token refresh fails',
        build: () {
          const failure = AuthFailure(
            message: 'Token expired',
            code: 'token_expired',
          );
          when(mockRefreshTokenUseCase(NoParams()))
              .thenAnswer((_) async => const Left(failure));
          return authBloc;
        },
        seed: () => AuthAuthenticated(TestDataFactory.createAuth()),
        act: (bloc) => bloc.add(const AuthTokenRefreshRequested()),
        expect: () => [
          const AuthUnauthenticated(),
        ],
      );
    });
  });
}
```

## 3. Widget测试示例

### 登录页面测试
```dart
// test/features/auth/presentation/pages/login_page_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';
import 'package:features_auth/features_auth.dart';
import '../../../helpers/base_test.dart';
import '../../../helpers/mocks.mocks.dart';
import '../../../helpers/test_config.dart';

void main() {
  group('LoginPage', () {
    late MockAuthBloc mockAuthBloc;
    
    setUp(() {
      TestHelper.setupTestEnvironment();
      mockAuthBloc = MockAuthBloc();
    });
    
    tearDown(() {
      TestHelper.tearDownTestEnvironment();
    });
    
    Widget createTestWidget() {
      return MaterialApp(
        home: BlocProvider<AuthBloc>.value(
          value: mockAuthBloc,
          child: const LoginPage(),
        ),
      );
    }
    
    testWidgets('should display login form elements', (tester) async {
      // Arrange
      when(mockAuthBloc.state).thenReturn(const AuthInitial());
      when(mockAuthBloc.stream).thenAnswer((_) => Stream.value(const AuthInitial()));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      
      // Assert
      expect(find.text('登录'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(2)); // Email and Password
      expect(find.text('邮箱'), findsOneWidget);
      expect(find.text('密码'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
      expect(find.text('登录'), findsNWidgets(2)); // Title and Button
    });
    
    testWidgets('should show loading indicator when state is AuthLoading', (tester) async {
      // Arrange
      when(mockAuthBloc.state).thenReturn(const AuthLoading());
      when(mockAuthBloc.stream).thenAnswer((_) => Stream.value(const AuthLoading()));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();
      
      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });
    
    testWidgets('should show error message when state is AuthError', (tester) async {
      // Arrange
      const failure = AuthFailure(
        message: 'Invalid credentials',
        code: 'invalid_credentials',
      );
      when(mockAuthBloc.state).thenReturn(const AuthError(failure));
      when(mockAuthBloc.stream).thenAnswer((_) => Stream.value(const AuthError(failure)));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();
      
      // Assert
      expect(find.text('用户名或密码错误'), findsOneWidget);
    });
    
    testWidgets('should trigger login event when login button is pressed', (tester) async {
      // Arrange
      when(mockAuthBloc.state).thenReturn(const AuthInitial());
      when(mockAuthBloc.stream).thenAnswer((_) => Stream.value(const AuthInitial()));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      
      // Enter email and password
      await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(const Key('password_field')), 'password123');
      
      // Tap login button
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pump();
      
      // Assert
      verify(mockAuthBloc.add(const AuthLoginRequested(
        email: '<EMAIL>',
        password: 'password123',
      ))).called(1);
    });
    
    testWidgets('should validate email format', (tester) async {
      // Arrange
      when(mockAuthBloc.state).thenReturn(const AuthInitial());
      when(mockAuthBloc.stream).thenAnswer((_) => Stream.value(const AuthInitial()));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      
      // Enter invalid email
      await tester.enterText(find.byKey(const Key('email_field')), 'invalid-email');
      await tester.enterText(find.byKey(const Key('password_field')), 'password123');
      
      // Tap login button
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pump();
      
      // Assert
      expect(find.text('请输入有效的邮箱地址'), findsOneWidget);
      verifyNever(mockAuthBloc.add(any));
    });
    
    testWidgets('should validate password length', (tester) async {
      // Arrange
      when(mockAuthBloc.state).thenReturn(const AuthInitial());
      when(mockAuthBloc.stream).thenAnswer((_) => Stream.value(const AuthInitial()));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      
      // Enter short password
      await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(const Key('password_field')), '123');
      
      // Tap login button
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pump();
      
      // Assert
      expect(find.text('密码长度至少为6位'), findsOneWidget);
      verifyNever(mockAuthBloc.add(any));
    });
    
    testWidgets('should navigate to register page when register link is tapped', (tester) async {
      // Arrange
      when(mockAuthBloc.state).thenReturn(const AuthInitial());
      when(mockAuthBloc.stream).thenAnswer((_) => Stream.value(const AuthInitial()));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      
      // Tap register link
      await tester.tap(find.byKey(const Key('register_link')));
      await tester.pumpAndSettle();
      
      // Assert
      expect(find.byType(RegisterPage), findsOneWidget);
    });
    
    testWidgets('should show/hide password when visibility icon is tapped', (tester) async {
      // Arrange
      when(mockAuthBloc.state).thenReturn(const AuthInitial());
      when(mockAuthBloc.stream).thenAnswer((_) => Stream.value(const AuthInitial()));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      
      // Enter password
      await tester.enterText(find.byKey(const Key('password_field')), 'password123');
      
      // Initially password should be obscured
      final passwordField = tester.widget<TextFormField>(find.byKey(const Key('password_field')));
      expect(passwordField.obscureText, true);
      
      // Tap visibility icon
      await tester.tap(find.byKey(const Key('password_visibility_toggle')));
      await tester.pump();
      
      // Password should now be visible
      final updatedPasswordField = tester.widget<TextFormField>(find.byKey(const Key('password_field')));
      expect(updatedPasswordField.obscureText, false);
    });
  });
}
```

### 用户列表Widget测试
```dart
// test/features/user/presentation/widgets/user_list_widget_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';
import 'package:features_user/features_user.dart';
import '../../../helpers/base_test.dart';
import '../../../helpers/mocks.mocks.dart';
import '../../../helpers/test_config.dart';

void main() {
  group('UserListWidget', () {
    late MockUserListBloc mockUserListBloc;
    
    setUp(() {
      TestHelper.setupTestEnvironment();
      mockUserListBloc = MockUserListBloc();
    });
    
    tearDown(() {
      TestHelper.tearDownTestEnvironment();
    });
    
    Widget createTestWidget() {
      return MaterialApp(
        home: Scaffold(
          body: BlocProvider<UserListBloc>.value(
            value: mockUserListBloc,
            child: const UserListWidget(),
          ),
        ),
      );
    }
    
    testWidgets('should display loading indicator when state is UserListLoading', (tester) async {
      // Arrange
      when(mockUserListBloc.state).thenReturn(const UserListLoading());
      when(mockUserListBloc.stream).thenAnswer((_) => Stream.value(const UserListLoading()));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      
      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });
    
    testWidgets('should display user list when state is UserListLoaded', (tester) async {
      // Arrange
      final users = [
        TestDataFactory.createUser(id: '1', name: 'User 1', email: '<EMAIL>'),
        TestDataFactory.createUser(id: '2', name: 'User 2', email: '<EMAIL>'),
        TestDataFactory.createUser(id: '3', name: 'User 3', email: '<EMAIL>'),
      ];
      
      when(mockUserListBloc.state).thenReturn(UserListLoaded(
        users: users,
        hasReachedMax: false,
      ));
      when(mockUserListBloc.stream).thenAnswer((_) => Stream.value(UserListLoaded(
        users: users,
        hasReachedMax: false,
      )));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      
      // Assert
      expect(find.byType(ListView), findsOneWidget);
      expect(find.byType(UserListItem), findsNWidgets(3));
      expect(find.text('User 1'), findsOneWidget);
      expect(find.text('User 2'), findsOneWidget);
      expect(find.text('User 3'), findsOneWidget);
    });
    
    testWidgets('should display error message when state is UserListError', (tester) async {
      // Arrange
      const failure = NetworkFailure(
        message: 'Network error',
        code: 'network_error',
      );
      when(mockUserListBloc.state).thenReturn(const UserListError(failure));
      when(mockUserListBloc.stream).thenAnswer((_) => Stream.value(const UserListError(failure)));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      
      // Assert
      expect(find.text('网络连接失败，请检查网络设置'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget); // Retry button
    });
    
    testWidgets('should trigger refresh when pull to refresh', (tester) async {
      // Arrange
      final users = [TestDataFactory.createUser()];
      when(mockUserListBloc.state).thenReturn(UserListLoaded(
        users: users,
        hasReachedMax: false,
      ));
      when(mockUserListBloc.stream).thenAnswer((_) => Stream.value(UserListLoaded(
        users: users,
        hasReachedMax: false,
      )));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      
      // Perform pull to refresh
      await tester.fling(find.byType(ListView), const Offset(0, 300), 1000);
      await tester.pump();
      
      // Assert
      verify(mockUserListBloc.add(const UserListRefreshRequested())).called(1);
    });
    
    testWidgets('should trigger load more when scrolled to bottom', (tester) async {
      // Arrange
      final users = List.generate(20, (index) => TestDataFactory.createUser(
        id: 'user_$index',
        name: 'User $index',
      ));
      
      when(mockUserListBloc.state).thenReturn(UserListLoaded(
        users: users,
        hasReachedMax: false,
      ));
      when(mockUserListBloc.stream).thenAnswer((_) => Stream.value(UserListLoaded(
        users: users,
        hasReachedMax: false,
      )));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      
      // Scroll to bottom
      await tester.scrollUntilVisible(
        find.text('User 19'),
        500.0,
        scrollable: find.byType(Scrollable),
      );
      
      // Assert
      verify(mockUserListBloc.add(const UserListLoadMoreRequested())).called(1);
    });
    
    testWidgets('should show loading indicator at bottom when loading more', (tester) async {
      // Arrange
      final users = List.generate(10, (index) => TestDataFactory.createUser(
        id: 'user_$index',
        name: 'User $index',
      ));
      
      when(mockUserListBloc.state).thenReturn(UserListLoadingMore(
        users: users,
      ));
      when(mockUserListBloc.stream).thenAnswer((_) => Stream.value(UserListLoadingMore(
        users: users,
      )));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      
      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.byType(UserListItem), findsNWidgets(10));
    });
    
    testWidgets('should not show loading indicator when reached max', (tester) async {
      // Arrange
      final users = List.generate(5, (index) => TestDataFactory.createUser(
        id: 'user_$index',
        name: 'User $index',
      ));
      
      when(mockUserListBloc.state).thenReturn(UserListLoaded(
        users: users,
        hasReachedMax: true,
      ));
      when(mockUserListBloc.stream).thenAnswer((_) => Stream.value(UserListLoaded(
        users: users,
        hasReachedMax: true,
      )));
      
      // Act
      await tester.pumpWidget(createTestWidget());
      
      // Scroll to bottom
      await tester.scrollUntilVisible(
        find.text('User 4'),
        500.0,
        scrollable: find.byType(Scrollable),
      );
      
      // Assert
      expect(find.byType(CircularProgressIndicator), findsNothing);
      verifyNever(mockUserListBloc.add(const UserListLoadMoreRequested()));
    });
  });
}
```

## 4. 集成测试示例

### 认证流程集成测试
```dart
// integration_test/auth_flow_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter_template/main.dart' as app;
import 'helpers/test_helper.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('Authentication Flow', () {
    setUp(() async {
      await IntegrationTestHelper.setupTestEnvironment();
    });
    
    tearDown(() async {
      await IntegrationTestHelper.tearDownTestEnvironment();
    });
    
    testWidgets('complete login flow', (tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();
      
      // Should show login page initially
      expect(find.text('登录'), findsOneWidget);
      expect(find.byKey(const Key('email_field')), findsOneWidget);
      expect(find.byKey(const Key('password_field')), findsOneWidget);
      
      // Enter valid credentials
      await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(const Key('password_field')), 'password123');
      
      // Tap login button
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pumpAndSettle();
      
      // Should navigate to home page after successful login
      expect(find.byKey(const Key('home_page')), findsOneWidget);
      expect(find.text('欢迎'), findsOneWidget);
    });
    
    testWidgets('login with invalid credentials', (tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();
      
      // Enter invalid credentials
      await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(const Key('password_field')), 'wrongpassword');
      
      // Tap login button
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pumpAndSettle();
      
      // Should show error message
      expect(find.text('用户名或密码错误'), findsOneWidget);
      
      // Should remain on login page
      expect(find.byKey(const Key('login_page')), findsOneWidget);
    });
    
    testWidgets('logout flow', (tester) async {
      // Start the app and login first
      app.main();
      await tester.pumpAndSettle();
      
      // Login
      await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(const Key('password_field')), 'password123');
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pumpAndSettle();
      
      // Should be on home page
      expect(find.byKey(const Key('home_page')), findsOneWidget);
      
      // Open drawer and tap logout
      await tester.tap(find.byIcon(Icons.menu));
      await tester.pumpAndSettle();
      
      await tester.tap(find.byKey(const Key('logout_button')));
      await tester.pumpAndSettle();
      
      // Should return to login page
      expect(find.byKey(const Key('login_page')), findsOneWidget);
    });
    
    testWidgets('registration flow', (tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();
      
      // Tap register link
      await tester.tap(find.byKey(const Key('register_link')));
      await tester.pumpAndSettle();
      
      // Should navigate to register page
      expect(find.byKey(const Key('register_page')), findsOneWidget);
      
      // Fill registration form
      await tester.enterText(find.byKey(const Key('name_field')), 'Test User');
      await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(const Key('password_field')), 'password123');
      await tester.enterText(find.byKey(const Key('confirm_password_field')), 'password123');
      
      // Tap register button
      await tester.tap(find.byKey(const Key('register_button')));
      await tester.pumpAndSettle();
      
      // Should navigate to home page after successful registration
      expect(find.byKey(const Key('home_page')), findsOneWidget);
    });
  });
}
```

### 集成测试辅助工具
```dart
// integration_test/helpers/test_helper.dart
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class IntegrationTestHelper {
  static Future<void> setupTestEnvironment() async {
    // 清理SharedPreferences
    SharedPreferences.setMockInitialValues({});
    
    // 设置测试用的方法通道
    _setupMethodChannels();
  }
  
  static Future<void> tearDownTestEnvironment() async {
    // 清理测试数据
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
    
    // 清理安全存储
    const secureStorage = FlutterSecureStorage();
    await secureStorage.deleteAll();
  }
  
  static void _setupMethodChannels() {
    // 模拟网络连接
    const MethodChannel('plugins.flutter.io/connectivity')
        .setMockMethodCallHandler((MethodCall methodCall) async {
      switch (methodCall.method) {
        case 'check':
          return 'wifi';
        case 'wifiName':
          return 'TestWiFi';
        default:
          return null;
      }
    });
    
    // 模拟设备信息
    const MethodChannel('plugins.flutter.io/device_info')
        .setMockMethodCallHandler((MethodCall methodCall) async {
      switch (methodCall.method) {
        case 'getAndroidDeviceInfo':
          return {
            'version': {'release': '11'},
            'brand': 'TestBrand',
            'model': 'TestModel',
          };
        case 'getIosDeviceInfo':
          return {
            'systemVersion': '15.0',
            'model': 'iPhone',
            'name': 'Test iPhone',
          };
        default:
          return null;
      }
    });
    
    // 模拟安全存储
    const MethodChannel('plugins.it_nomads.com/flutter_secure_storage')
        .setMockMethodCallHandler((MethodCall methodCall) async {
      final Map<String, String> storage = {};
      
      switch (methodCall.method) {
        case 'write':
          storage[methodCall.arguments['key']] = methodCall.arguments['value'];
          return null;
        case 'read':
          return storage[methodCall.arguments['key']];
        case 'delete':
          storage.remove(methodCall.arguments['key']);
          return null;
        case 'deleteAll':
          storage.clear();
          return null;
        case 'readAll':
          return storage;
        default:
          return null;
      }
    });
  }
}
```

这个测试框架实现提供了：

1. **完整的测试基础架构**：测试配置、工具类、Mock生成器、测试基类
2. **单元测试示例**：UseCase、Repository、BLoC的详细测试
3. **Widget测试示例**：页面和组件的UI测试
4. **集成测试示例**：完整的用户流程测试
5. **测试工具和辅助类**：数据工厂、测试助手、Mock设置
6. **测试最佳实践**：AAA模式、Mock使用、异步测试、状态验证

所有测试都遵循Clean Architecture原则，确保代码的可测试性和可维护性。