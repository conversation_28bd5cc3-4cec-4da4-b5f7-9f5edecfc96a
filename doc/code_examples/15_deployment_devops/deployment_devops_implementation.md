# Flutter 企业级应用部署和运维实现

本文档提供 Flutter 企业级应用部署和运维的完整实现示例，包括多环境配置、CI/CD 流水线、容器化部署、监控告警、性能优化等。

## 1. 多环境配置管理

### 1.1 环境配置结构

```dart
// lib/config/environment.dart
enum Environment {
  development,
  staging,
  production,
}

class EnvironmentConfig {
  static Environment _currentEnvironment = Environment.development;
  
  static Environment get currentEnvironment => _currentEnvironment;
  
  static void setEnvironment(Environment environment) {
    _currentEnvironment = environment;
  }
  
  static bool get isDevelopment => _currentEnvironment == Environment.development;
  static bool get isStaging => _currentEnvironment == Environment.staging;
  static bool get isProduction => _currentEnvironment == Environment.production;
  
  static String get environmentName {
    switch (_currentEnvironment) {
      case Environment.development:
        return 'development';
      case Environment.staging:
        return 'staging';
      case Environment.production:
        return 'production';
    }
  }
}

abstract class AppConfig {
  String get appName;
  String get apiBaseUrl;
  String get websocketUrl;
  String get sentryDsn;
  String get firebaseProjectId;
  String get amplitudeApiKey;
  bool get enableLogging;
  bool get enableCrashlytics;
  bool get enableAnalytics;
  Duration get apiTimeout;
  int get maxRetryAttempts;
  Map<String, dynamic> get featureFlags;
}

class DevelopmentConfig implements AppConfig {
  @override
  String get appName => 'MyApp Dev';
  
  @override
  String get apiBaseUrl => 'https://api-dev.example.com';
  
  @override
  String get websocketUrl => 'wss://ws-dev.example.com';
  
  @override
  String get sentryDsn => '';
  
  @override
  String get firebaseProjectId => 'myapp-dev';
  
  @override
  String get amplitudeApiKey => 'dev-amplitude-key';
  
  @override
  bool get enableLogging => true;
  
  @override
  bool get enableCrashlytics => false;
  
  @override
  bool get enableAnalytics => false;
  
  @override
  Duration get apiTimeout => const Duration(seconds: 30);
  
  @override
  int get maxRetryAttempts => 3;
  
  @override
  Map<String, dynamic> get featureFlags => {
    'newUserFlow': true,
    'betaFeatures': true,
    'debugMode': true,
  };
}

class StagingConfig implements AppConfig {
  @override
  String get appName => 'MyApp Staging';
  
  @override
  String get apiBaseUrl => 'https://api-staging.example.com';
  
  @override
  String get websocketUrl => 'wss://ws-staging.example.com';
  
  @override
  String get sentryDsn => 'https://<EMAIL>/project';
  
  @override
  String get firebaseProjectId => 'myapp-staging';
  
  @override
  String get amplitudeApiKey => 'staging-amplitude-key';
  
  @override
  bool get enableLogging => true;
  
  @override
  bool get enableCrashlytics => true;
  
  @override
  bool get enableAnalytics => true;
  
  @override
  Duration get apiTimeout => const Duration(seconds: 20);
  
  @override
  int get maxRetryAttempts => 2;
  
  @override
  Map<String, dynamic> get featureFlags => {
    'newUserFlow': true,
    'betaFeatures': false,
    'debugMode': false,
  };
}

class ProductionConfig implements AppConfig {
  @override
  String get appName => 'MyApp';
  
  @override
  String get apiBaseUrl => 'https://api.example.com';
  
  @override
  String get websocketUrl => 'wss://ws.example.com';
  
  @override
  String get sentryDsn => 'https://<EMAIL>/project';
  
  @override
  String get firebaseProjectId => 'myapp-production';
  
  @override
  String get amplitudeApiKey => 'production-amplitude-key';
  
  @override
  bool get enableLogging => false;
  
  @override
  bool get enableCrashlytics => true;
  
  @override
  bool get enableAnalytics => true;
  
  @override
  Duration get apiTimeout => const Duration(seconds: 15);
  
  @override
  int get maxRetryAttempts => 1;
  
  @override
  Map<String, dynamic> get featureFlags => {
    'newUserFlow': false,
    'betaFeatures': false,
    'debugMode': false,
  };
}

class ConfigManager {
  static AppConfig? _config;
  
  static AppConfig get config {
    if (_config == null) {
      throw Exception('Config not initialized. Call ConfigManager.initialize() first.');
    }
    return _config!;
  }
  
  static void initialize(Environment environment) {
    EnvironmentConfig.setEnvironment(environment);
    
    switch (environment) {
      case Environment.development:
        _config = DevelopmentConfig();
        break;
      case Environment.staging:
        _config = StagingConfig();
        break;
      case Environment.production:
        _config = ProductionConfig();
        break;
    }
  }
  
  static bool isFeatureEnabled(String featureName) {
    return config.featureFlags[featureName] ?? false;
  }
}
```

### 1.2 环境特定的入口文件

```dart
// lib/main_development.dart
import 'package:flutter/material.dart';
import 'config/environment.dart';
import 'app.dart';

void main() {
  ConfigManager.initialize(Environment.development);
  runApp(const MyApp());
}
```

```dart
// lib/main_staging.dart
import 'package:flutter/material.dart';
import 'config/environment.dart';
import 'app.dart';

void main() {
  ConfigManager.initialize(Environment.staging);
  runApp(const MyApp());
}
```

```dart
// lib/main_production.dart
import 'package:flutter/material.dart';
import 'config/environment.dart';
import 'app.dart';

void main() {
  ConfigManager.initialize(Environment.production);
  runApp(const MyApp());
}
```

### 1.3 构建脚本

```bash
#!/bin/bash
# scripts/build.sh

set -e

ENVIRONMENT=${1:-development}
PLATFORM=${2:-android}
BUILD_TYPE=${3:-debug}

echo "Building for environment: $ENVIRONMENT, platform: $PLATFORM, type: $BUILD_TYPE"

# 清理之前的构建
flutter clean
flutter pub get

# 根据环境设置不同的配置
case $ENVIRONMENT in
  "development")
    MAIN_FILE="lib/main_development.dart"
    APP_SUFFIX=".dev"
    ;;
  "staging")
    MAIN_FILE="lib/main_staging.dart"
    APP_SUFFIX=".staging"
    ;;
  "production")
    MAIN_FILE="lib/main_production.dart"
    APP_SUFFIX=""
    ;;
  *)
    echo "Unknown environment: $ENVIRONMENT"
    exit 1
    ;;
esac

# 构建应用
if [ "$PLATFORM" = "android" ]; then
  if [ "$BUILD_TYPE" = "release" ]; then
    flutter build apk --target=$MAIN_FILE --release
    flutter build appbundle --target=$MAIN_FILE --release
  else
    flutter build apk --target=$MAIN_FILE --debug
  fi
elif [ "$PLATFORM" = "ios" ]; then
  if [ "$BUILD_TYPE" = "release" ]; then
    flutter build ios --target=$MAIN_FILE --release --no-codesign
  else
    flutter build ios --target=$MAIN_FILE --debug --no-codesign
  fi
elif [ "$PLATFORM" = "web" ]; then
  flutter build web --target=$MAIN_FILE --release
else
  echo "Unknown platform: $PLATFORM"
  exit 1
fi

echo "Build completed successfully!"
```

## 2. CI/CD 流水线

### 2.1 GitHub Actions 配置

```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop, 'release/*' ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
        - development
        - staging
        - production

env:
  FLUTTER_VERSION: '3.16.0'
  JAVA_VERSION: '17'
  NODE_VERSION: '18'

jobs:
  # 代码质量检查
  code-quality:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
        
    - name: Install dependencies
      run: flutter pub get
      
    - name: Verify formatting
      run: dart format --output=none --set-exit-if-changed .
      
    - name: Analyze project source
      run: dart analyze --fatal-infos
      
    - name: Run tests
      run: flutter test --coverage
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: coverage/lcov.info
        
  # Android 构建
  build-android:
    needs: code-quality
    runs-on: ubuntu-latest
    strategy:
      matrix:
        environment: [development, staging, production]
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        
    - name: Setup Java
      uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: ${{ env.JAVA_VERSION }}
        
    - name: Setup Android SDK
      uses: android-actions/setup-android@v2
      
    - name: Install dependencies
      run: flutter pub get
      
    - name: Build APK
      run: |
        chmod +x scripts/build.sh
        ./scripts/build.sh ${{ matrix.environment }} android release
        
    - name: Sign APK
      if: matrix.environment == 'production'
      run: |
        echo "${{ secrets.ANDROID_KEYSTORE }}" | base64 --decode > android/app/keystore.jks
        flutter build apk --target=lib/main_production.dart --release
      env:
        KEYSTORE_PASSWORD: ${{ secrets.KEYSTORE_PASSWORD }}
        KEY_ALIAS: ${{ secrets.KEY_ALIAS }}
        KEY_PASSWORD: ${{ secrets.KEY_PASSWORD }}
        
    - name: Upload APK artifact
      uses: actions/upload-artifact@v3
      with:
        name: apk-${{ matrix.environment }}
        path: build/app/outputs/flutter-apk/*.apk
        
  # iOS 构建
  build-ios:
    needs: code-quality
    runs-on: macos-latest
    strategy:
      matrix:
        environment: [development, staging, production]
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        
    - name: Install dependencies
      run: flutter pub get
      
    - name: Build iOS
      run: |
        chmod +x scripts/build.sh
        ./scripts/build.sh ${{ matrix.environment }} ios release
        
    - name: Archive iOS app
      if: matrix.environment == 'production'
      run: |
        xcodebuild -workspace ios/Runner.xcworkspace \
          -scheme Runner \
          -configuration Release \
          -destination generic/platform=iOS \
          -archivePath build/ios/Runner.xcarchive \
          archive
          
    - name: Upload iOS artifact
      uses: actions/upload-artifact@v3
      with:
        name: ios-${{ matrix.environment }}
        path: build/ios/iphoneos/*.app
        
  # Web 构建
  build-web:
    needs: code-quality
    runs-on: ubuntu-latest
    strategy:
      matrix:
        environment: [development, staging, production]
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        
    - name: Install dependencies
      run: flutter pub get
      
    - name: Build Web
      run: |
        chmod +x scripts/build.sh
        ./scripts/build.sh ${{ matrix.environment }} web release
        
    - name: Upload Web artifact
      uses: actions/upload-artifact@v3
      with:
        name: web-${{ matrix.environment }}
        path: build/web/
        
  # 部署到 Firebase Hosting
  deploy-web:
    needs: build-web
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download Web artifact
      uses: actions/download-artifact@v3
      with:
        name: web-production
        path: build/web/
        
    - name: Deploy to Firebase
      uses: FirebaseExtended/action-hosting-deploy@v0
      with:
        repoToken: '${{ secrets.GITHUB_TOKEN }}'
        firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT }}'
        projectId: myapp-production
        channelId: live
        
  # 部署到 Google Play
  deploy-android:
    needs: build-android
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download APK artifact
      uses: actions/download-artifact@v3
      with:
        name: apk-production
        
    - name: Deploy to Google Play
      uses: r0adkll/upload-google-play@v1
      with:
        serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
        packageName: com.example.myapp
        releaseFiles: '*.apk'
        track: internal
        
  # 部署到 App Store
  deploy-ios:
    needs: build-ios
    runs-on: macos-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download iOS artifact
      uses: actions/download-artifact@v3
      with:
        name: ios-production
        
    - name: Deploy to App Store
      run: |
        xcrun altool --upload-app \
          --type ios \
          --file Runner.ipa \
          --username ${{ secrets.APPLE_ID }} \
          --password ${{ secrets.APPLE_APP_PASSWORD }}
```

### 2.2 GitLab CI 配置

```yaml
# .gitlab-ci.yml
stages:
  - test
  - build
  - deploy

variables:
  FLUTTER_VERSION: "3.16.0"
  DOCKER_DRIVER: overlay2

# 缓存配置
cache:
  key: "$CI_COMMIT_REF_SLUG"
  paths:
    - .pub-cache/
    - build/

# 测试阶段
test:
  stage: test
  image: cirrusci/flutter:$FLUTTER_VERSION
  before_script:
    - flutter pub get
  script:
    - dart format --output=none --set-exit-if-changed .
    - dart analyze --fatal-infos
    - flutter test --coverage
  coverage: '/lines......: \d+\.\d+%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura.xml
    paths:
      - coverage/
    expire_in: 1 week

# Android 构建
build:android:development:
  stage: build
  image: cirrusci/flutter:$FLUTTER_VERSION
  before_script:
    - flutter pub get
  script:
    - chmod +x scripts/build.sh
    - ./scripts/build.sh development android release
  artifacts:
    paths:
      - build/app/outputs/flutter-apk/*.apk
    expire_in: 1 week
  only:
    - develop

build:android:production:
  stage: build
  image: cirrusci/flutter:$FLUTTER_VERSION
  before_script:
    - flutter pub get
    - echo "$ANDROID_KEYSTORE" | base64 -d > android/app/keystore.jks
  script:
    - chmod +x scripts/build.sh
    - ./scripts/build.sh production android release
  artifacts:
    paths:
      - build/app/outputs/flutter-apk/*.apk
      - build/app/outputs/bundle/release/*.aab
    expire_in: 1 month
  only:
    - main

# Web 构建
build:web:production:
  stage: build
  image: cirrusci/flutter:$FLUTTER_VERSION
  before_script:
    - flutter pub get
  script:
    - chmod +x scripts/build.sh
    - ./scripts/build.sh production web release
  artifacts:
    paths:
      - build/web/
    expire_in: 1 month
  only:
    - main

# 部署到 Firebase
deploy:web:production:
  stage: deploy
  image: node:18
  dependencies:
    - build:web:production
  before_script:
    - npm install -g firebase-tools
  script:
    - firebase deploy --token $FIREBASE_TOKEN --project myapp-production
  only:
    - main

# 部署到 Google Play
deploy:android:production:
  stage: deploy
  image: ruby:3.0
  dependencies:
    - build:android:production
  before_script:
    - gem install fastlane
  script:
    - cd android
    - fastlane deploy_internal
  only:
    - main
```

## 3. 容器化部署

### 3.1 Docker 配置

```dockerfile
# Dockerfile.web
FROM nginx:alpine

# 复制构建产物
COPY build/web /usr/share/nginx/html

# 复制 nginx 配置
COPY docker/nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动 nginx
CMD ["nginx", "-g", "daemon off;"]
```

```nginx
# docker/nginx.conf
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # 启用 gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 缓存配置
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;
        
        # 处理 Flutter Web 路由
        location / {
            try_files $uri $uri/ /index.html;
        }
        
        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-src 'none';" always;
        
        # 健康检查
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
```

### 3.2 Docker Compose 配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  # Flutter Web 应用
  web:
    build:
      context: .
      dockerfile: Dockerfile.web
    ports:
      - "80:80"
    environment:
      - ENVIRONMENT=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.web.rule=Host(`myapp.example.com`)"
      - "traefik.http.routers.web.tls=true"
      - "traefik.http.routers.web.tls.certresolver=letsencrypt"
  
  # 反向代理
  traefik:
    image: traefik:v2.10
    command:
      - "--api.dashboard=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.letsencrypt.acme.tlschallenge=true"
      - "--certificatesresolvers.letsencrypt.acme.email=<EMAIL>"
      - "--certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json"
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - "./letsencrypt:/letsencrypt"
    restart: unless-stopped
  
  # 监控
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - "./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml"
      - "prometheus_data:/prometheus"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped
  
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - "grafana_data:/var/lib/grafana"
      - "./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards"
      - "./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources"
    restart: unless-stopped

volumes:
  prometheus_data:
  grafana_data:
```

### 3.3 Kubernetes 部署

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: myapp-production
  labels:
    name: myapp-production
---
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: myapp-web
  namespace: myapp-production
  labels:
    app: myapp-web
spec:
  replicas: 3
  selector:
    matchLabels:
      app: myapp-web
  template:
    metadata:
      labels:
        app: myapp-web
    spec:
      containers:
      - name: web
        image: myapp/web:latest
        ports:
        - containerPort: 80
        env:
        - name: ENVIRONMENT
          value: "production"
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
---
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: myapp-web-service
  namespace: myapp-production
spec:
  selector:
    app: myapp-web
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
  type: ClusterIP
---
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: myapp-web-ingress
  namespace: myapp-production
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - myapp.example.com
    secretName: myapp-tls
  rules:
  - host: myapp.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: myapp-web-service
            port:
              number: 80
---
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: myapp-web-hpa
  namespace: myapp-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: myapp-web
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## 4. 监控和告警

### 4.1 应用监控

```dart
// lib/monitoring/app_monitor.dart
import 'package:flutter/foundation.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_performance/firebase_performance.dart';

class AppMonitor {
  static bool _initialized = false;
  
  static Future<void> initialize() async {
    if (_initialized) return;
    
    // 初始化 Sentry
    if (ConfigManager.config.sentryDsn.isNotEmpty) {
      await SentryFlutter.init(
        (options) {
          options.dsn = ConfigManager.config.sentryDsn;
          options.environment = EnvironmentConfig.environmentName;
          options.tracesSampleRate = EnvironmentConfig.isProduction ? 0.1 : 1.0;
          options.profilesSampleRate = EnvironmentConfig.isProduction ? 0.1 : 1.0;
        },
      );
    }
    
    // 初始化 Firebase Crashlytics
    if (ConfigManager.config.enableCrashlytics) {
      FlutterError.onError = (errorDetails) {
        FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
      };
      
      PlatformDispatcher.instance.onError = (error, stack) {
        FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
        return true;
      };
    }
    
    _initialized = true;
  }
  
  static Future<void> recordError(
    dynamic error,
    StackTrace? stackTrace, {
    Map<String, dynamic>? extra,
    String? level,
  }) async {
    if (!_initialized) return;
    
    // 记录到 Sentry
    if (ConfigManager.config.sentryDsn.isNotEmpty) {
      await Sentry.captureException(
        error,
        stackTrace: stackTrace,
        withScope: (scope) {
          if (extra != null) {
            scope.setExtras(extra);
          }
          if (level != null) {
            scope.level = SentryLevel.fromName(level);
          }
        },
      );
    }
    
    // 记录到 Firebase Crashlytics
    if (ConfigManager.config.enableCrashlytics) {
      await FirebaseCrashlytics.instance.recordError(
        error,
        stackTrace,
        fatal: false,
      );
    }
  }
  
  static Future<void> recordCustomEvent(
    String name,
    Map<String, dynamic> parameters,
  ) async {
    if (!_initialized) return;
    
    // 记录到 Sentry
    if (ConfigManager.config.sentryDsn.isNotEmpty) {
      Sentry.addBreadcrumb(
        Breadcrumb(
          message: name,
          data: parameters,
          timestamp: DateTime.now(),
        ),
      );
    }
  }
  
  static Future<T> tracePerformance<T>(
    String operationName,
    Future<T> Function() operation, {
    Map<String, dynamic>? tags,
  }) async {
    // Firebase Performance 追踪
    final trace = FirebasePerformance.instance.newTrace(operationName);
    
    if (tags != null) {
      for (final entry in tags.entries) {
        trace.putAttribute(entry.key, entry.value.toString());
      }
    }
    
    await trace.start();
    
    try {
      final result = await operation();
      await trace.stop();
      return result;
    } catch (error, stackTrace) {
      await trace.stop();
      await recordError(error, stackTrace);
      rethrow;
    }
  }
  
  static void setUserContext({
    required String userId,
    String? email,
    String? username,
    Map<String, dynamic>? extra,
  }) {
    if (!_initialized) return;
    
    // 设置 Sentry 用户上下文
    if (ConfigManager.config.sentryDsn.isNotEmpty) {
      Sentry.configureScope((scope) {
        scope.setUser(SentryUser(
          id: userId,
          email: email,
          username: username,
          data: extra,
        ));
      });
    }
    
    // 设置 Firebase Crashlytics 用户标识
    if (ConfigManager.config.enableCrashlytics) {
      FirebaseCrashlytics.instance.setUserIdentifier(userId);
      if (email != null) {
        FirebaseCrashlytics.instance.setCustomKey('email', email);
      }
      if (username != null) {
        FirebaseCrashlytics.instance.setCustomKey('username', username);
      }
    }
  }
}
```

### 4.2 性能监控

```dart
// lib/monitoring/performance_monitor.dart
import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';

class PerformanceMonitor {
  static final _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();
  
  final Map<String, Stopwatch> _timers = {};
  final List<PerformanceMetric> _metrics = [];
  Timer? _reportTimer;
  
  void initialize() {
    // 定期上报性能指标
    _reportTimer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => _reportMetrics(),
    );
  }
  
  void dispose() {
    _reportTimer?.cancel();
  }
  
  void startTimer(String name) {
    _timers[name] = Stopwatch()..start();
  }
  
  void stopTimer(String name, {Map<String, dynamic>? attributes}) {
    final timer = _timers.remove(name);
    if (timer != null) {
      timer.stop();
      _recordMetric(
        name: name,
        value: timer.elapsedMilliseconds.toDouble(),
        unit: 'ms',
        type: MetricType.duration,
        attributes: attributes,
      );
    }
  }
  
  void recordMetric({
    required String name,
    required double value,
    String unit = '',
    MetricType type = MetricType.gauge,
    Map<String, dynamic>? attributes,
  }) {
    _recordMetric(
      name: name,
      value: value,
      unit: unit,
      type: type,
      attributes: attributes,
    );
  }
  
  void _recordMetric({
    required String name,
    required double value,
    required String unit,
    required MetricType type,
    Map<String, dynamic>? attributes,
  }) {
    final metric = PerformanceMetric(
      name: name,
      value: value,
      unit: unit,
      type: type,
      timestamp: DateTime.now(),
      attributes: attributes ?? {},
    );
    
    _metrics.add(metric);
    
    // 限制内存中的指标数量
    if (_metrics.length > 1000) {
      _metrics.removeRange(0, 500);
    }
  }
  
  Future<void> _reportMetrics() async {
    if (_metrics.isEmpty) return;
    
    try {
      final deviceInfo = await _getDeviceInfo();
      final appInfo = await _getAppInfo();
      
      final report = PerformanceReport(
        metrics: List.from(_metrics),
        deviceInfo: deviceInfo,
        appInfo: appInfo,
        timestamp: DateTime.now(),
      );
      
      // 发送到监控服务
      await _sendReport(report);
      
      // 清空已发送的指标
      _metrics.clear();
    } catch (error, stackTrace) {
      AppMonitor.recordError(error, stackTrace);
    }
  }
  
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    final deviceInfo = DeviceInfoPlugin();
    
    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      return {
        'platform': 'android',
        'model': androidInfo.model,
        'manufacturer': androidInfo.manufacturer,
        'version': androidInfo.version.release,
        'sdkInt': androidInfo.version.sdkInt,
      };
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      return {
        'platform': 'ios',
        'model': iosInfo.model,
        'name': iosInfo.name,
        'systemVersion': iosInfo.systemVersion,
      };
    } else {
      return {
        'platform': Platform.operatingSystem,
      };
    }
  }
  
  Future<Map<String, dynamic>> _getAppInfo() async {
    return {
      'environment': EnvironmentConfig.environmentName,
      'version': '1.0.0', // 从 package_info_plus 获取
      'buildNumber': '1', // 从 package_info_plus 获取
    };
  }
  
  Future<void> _sendReport(PerformanceReport report) async {
    // 发送到自定义监控服务
    // 这里可以集成 Prometheus、DataDog、New Relic 等
    
    if (kDebugMode) {
      print('Performance Report: ${report.toJson()}');
    }
  }
}

enum MetricType {
  counter,
  gauge,
  histogram,
  duration,
}

class PerformanceMetric {
  final String name;
  final double value;
  final String unit;
  final MetricType type;
  final DateTime timestamp;
  final Map<String, dynamic> attributes;
  
  PerformanceMetric({
    required this.name,
    required this.value,
    required this.unit,
    required this.type,
    required this.timestamp,
    required this.attributes,
  });
  
  Map<String, dynamic> toJson() => {
    'name': name,
    'value': value,
    'unit': unit,
    'type': type.name,
    'timestamp': timestamp.toIso8601String(),
    'attributes': attributes,
  };
}

class PerformanceReport {
  final List<PerformanceMetric> metrics;
  final Map<String, dynamic> deviceInfo;
  final Map<String, dynamic> appInfo;
  final DateTime timestamp;
  
  PerformanceReport({
    required this.metrics,
    required this.deviceInfo,
    required this.appInfo,
    required this.timestamp,
  });
  
  Map<String, dynamic> toJson() => {
    'metrics': metrics.map((m) => m.toJson()).toList(),
    'deviceInfo': deviceInfo,
    'appInfo': appInfo,
    'timestamp': timestamp.toIso8601String(),
  };
}
```

### 4.3 监控配置

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
  
  - job_name: 'myapp-web'
    static_configs:
      - targets: ['web:80']
    metrics_path: '/metrics'
    scrape_interval: 30s
  
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
```

```yaml
# monitoring/alert_rules.yml
groups:
- name: myapp.rules
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} errors per second"
  
  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High response time detected"
      description: "95th percentile response time is {{ $value }} seconds"
  
  - alert: LowAvailability
    expr: up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Service is down"
      description: "{{ $labels.instance }} has been down for more than 1 minute"
```

## 5. 性能优化

### 5.1 构建优化

```bash
#!/bin/bash
# scripts/optimize_build.sh

set -e

ENVIRONMENT=${1:-production}
PLATFORM=${2:-web}

echo "Optimizing build for $ENVIRONMENT on $PLATFORM"

# 清理缓存
flutter clean
flutter pub get

# 代码生成
flutter packages pub run build_runner build --delete-conflicting-outputs

# 分析包大小
if [ "$PLATFORM" = "web" ]; then
  # Web 优化
  flutter build web \
    --target=lib/main_$ENVIRONMENT.dart \
    --release \
    --web-renderer canvaskit \
    --tree-shake-icons \
    --dart-define=FLUTTER_WEB_USE_SKIA=true \
    --source-maps
  
  # 分析包大小
  echo "Analyzing web bundle size..."
  du -sh build/web/
  
  # 压缩资源
  find build/web -name "*.js" -exec gzip -k {} \;
  find build/web -name "*.css" -exec gzip -k {} \;
  find build/web -name "*.html" -exec gzip -k {} \;
  
elif [ "$PLATFORM" = "android" ]; then
  # Android 优化
  flutter build apk \
    --target=lib/main_$ENVIRONMENT.dart \
    --release \
    --tree-shake-icons \
    --shrink \
    --obfuscate \
    --split-debug-info=build/debug-info/
  
  # 构建 App Bundle
  flutter build appbundle \
    --target=lib/main_$ENVIRONMENT.dart \
    --release \
    --tree-shake-icons \
    --shrink \
    --obfuscate \
    --split-debug-info=build/debug-info/
  
  # 分析 APK 大小
  echo "Analyzing APK size..."
  flutter build apk --analyze-size
  
elif [ "$PLATFORM" = "ios" ]; then
  # iOS 优化
  flutter build ios \
    --target=lib/main_$ENVIRONMENT.dart \
    --release \
    --tree-shake-icons \
    --obfuscate \
    --split-debug-info=build/debug-info/
fi

echo "Build optimization completed!"
```

### 5.2 运行时优化

```dart
// lib/optimization/performance_optimizer.dart
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/scheduler.dart';

class PerformanceOptimizer {
  static void initialize() {
    // 优化渲染性能
    _optimizeRendering();
    
    // 优化内存使用
    _optimizeMemory();
    
    // 优化网络请求
    _optimizeNetwork();
  }
  
  static void _optimizeRendering() {
    // 启用 GPU 渲染
    if (!kIsWeb) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        // 预热着色器
        _warmupShaders();
      });
    }
    
    // 设置帧率目标
    if (kDebugMode) {
      // 开发模式下监控帧率
      SchedulerBinding.instance.addTimingsCallback((timings) {
        for (final timing in timings) {
          final frameTime = timing.totalSpan.inMilliseconds;
          if (frameTime > 16) { // 超过 60fps
            print('Slow frame detected: ${frameTime}ms');
          }
        }
      });
    }
  }
  
  static void _optimizeMemory() {
    // 定期清理图片缓存
    Timer.periodic(const Duration(minutes: 10), (_) {
      PaintingBinding.instance.imageCache.clear();
    });
    
    // 设置图片缓存大小
    PaintingBinding.instance.imageCache.maximumSize = 100;
    PaintingBinding.instance.imageCache.maximumSizeBytes = 50 * 1024 * 1024; // 50MB
  }
  
  static void _optimizeNetwork() {
    // 配置 HTTP 客户端
    HttpOverrides.global = OptimizedHttpOverrides();
  }
  
  static Future<void> _warmupShaders() async {
    // 预热常用着色器
    const shaders = [
      'flutter/runtime_effect',
      'flutter/backdrop_filter',
      'flutter/color_filter',
    ];
    
    for (final shader in shaders) {
      try {
        await rootBundle.load('shaders/$shader.frag');
      } catch (e) {
        // 忽略不存在的着色器
      }
    }
  }
}

class OptimizedHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    final client = super.createHttpClient(context);
    
    // 设置连接超时
    client.connectionTimeout = const Duration(seconds: 10);
    
    // 启用 HTTP/2
    client.autoUncompress = true;
    
    // 设置最大连接数
    client.maxConnectionsPerHost = 6;
    
    return client;
  }
}
```

## 6. 安全配置

### 6.1 应用安全

```dart
// lib/security/security_manager.dart
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';

class SecurityManager {
  static bool _initialized = false;
  
  static Future<void> initialize() async {
    if (_initialized) return;
    
    // 检查应用完整性
    await _checkAppIntegrity();
    
    // 检测调试环境
    _detectDebugging();
    
    // 检测 Root/越狱
    await _detectRootJailbreak();
    
    // 设置网络安全
    _configureNetworkSecurity();
    
    _initialized = true;
  }
  
  static Future<void> _checkAppIntegrity() async {
    if (kDebugMode) return; // 开发模式跳过
    
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      
      // 检查包名
      const expectedPackageName = 'com.example.myapp';
      if (packageInfo.packageName != expectedPackageName) {
        throw SecurityException('Invalid package name');
      }
      
      // 检查签名（Android）
      if (Platform.isAndroid) {
        await _verifyAndroidSignature();
      }
      
      // 检查证书（iOS）
      if (Platform.isIOS) {
        await _verifyIOSCertificate();
      }
    } catch (e) {
      _handleSecurityViolation('App integrity check failed: $e');
    }
  }
  
  static void _detectDebugging() {
    if (kDebugMode) {
      print('⚠️ Running in debug mode');
      return;
    }
    
    // 检测调试器
    bool isDebugging = false;
    
    assert(() {
      isDebugging = true;
      return true;
    }());
    
    if (isDebugging) {
      _handleSecurityViolation('Debugger detected');
    }
  }
  
  static Future<void> _detectRootJailbreak() async {
    if (kDebugMode) return; // 开发模式跳过
    
    bool isCompromised = false;
    
    if (Platform.isAndroid) {
      isCompromised = await _detectAndroidRoot();
    } else if (Platform.isIOS) {
      isCompromised = await _detectIOSJailbreak();
    }
    
    if (isCompromised) {
      _handleSecurityViolation('Device is rooted/jailbroken');
    }
  }
  
  static Future<bool> _detectAndroidRoot() async {
    // 检查常见的 Root 文件
    final rootFiles = [
      '/system/app/Superuser.apk',
      '/sbin/su',
      '/system/bin/su',
      '/system/xbin/su',
      '/data/local/xbin/su',
      '/data/local/bin/su',
      '/system/sd/xbin/su',
      '/system/bin/failsafe/su',
      '/data/local/su',
    ];
    
    for (final file in rootFiles) {
      if (await File(file).exists()) {
        return true;
      }
    }
    
    // 检查 Root 应用
    try {
      final result = await Process.run('which', ['su']);
      if (result.exitCode == 0) {
        return true;
      }
    } catch (e) {
      // 忽略错误
    }
    
    return false;
  }
  
  static Future<bool> _detectIOSJailbreak() async {
    // 检查越狱文件
    final jailbreakFiles = [
      '/Applications/Cydia.app',
      '/Library/MobileSubstrate/MobileSubstrate.dylib',
      '/bin/bash',
      '/usr/sbin/sshd',
      '/etc/apt',
      '/private/var/lib/apt/',
    ];
    
    for (final file in jailbreakFiles) {
      if (await File(file).exists()) {
        return true;
      }
    }
    
    // 检查是否可以写入系统目录
    try {
      final testFile = File('/private/test_jailbreak.txt');
      await testFile.writeAsString('test');
      await testFile.delete();
      return true; // 如果能写入，说明越狱了
    } catch (e) {
      // 正常情况下应该抛出异常
    }
    
    return false;
  }
  
  static Future<void> _verifyAndroidSignature() async {
    // 这里需要使用 platform channel 调用原生代码
    // 验证 APK 签名
    try {
      const platform = MethodChannel('security/signature');
      final isValid = await platform.invokeMethod('verifySignature');
      
      if (!isValid) {
        throw SecurityException('Invalid app signature');
      }
    } catch (e) {
      throw SecurityException('Signature verification failed: $e');
    }
  }
  
  static Future<void> _verifyIOSCertificate() async {
    // iOS 证书验证
    // 这里需要使用 platform channel 调用原生代码
    try {
      const platform = MethodChannel('security/certificate');
      final isValid = await platform.invokeMethod('verifyCertificate');
      
      if (!isValid) {
        throw SecurityException('Invalid app certificate');
      }
    } catch (e) {
      throw SecurityException('Certificate verification failed: $e');
    }
  }
  
  static void _configureNetworkSecurity() {
    // 配置网络安全策略
    HttpOverrides.global = SecureHttpOverrides();
  }
  
  static void _handleSecurityViolation(String message) {
    // 记录安全违规
    AppMonitor.recordError(
      SecurityException(message),
      StackTrace.current,
      extra: {
        'securityViolation': true,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
    
    if (EnvironmentConfig.isProduction) {
      // 生产环境下退出应用
      SystemNavigator.pop();
    } else {
      // 开发环境下只记录日志
      print('🚨 Security violation: $message');
    }
  }
}

class SecurityException implements Exception {
  final String message;
  SecurityException(this.message);
  
  @override
  String toString() => 'SecurityException: $message';
}

class SecureHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    final client = super.createHttpClient(context);
    
    // 禁用不安全的协议
    client.badCertificateCallback = (cert, host, port) {
      // 在生产环境中严格验证证书
      if (EnvironmentConfig.isProduction) {
        return false;
      }
      // 开发环境可以允许自签名证书
      return !EnvironmentConfig.isProduction;
    };
    
    return client;
  }
}
```

### 6.2 数据加密

```dart
// lib/security/encryption_service.dart
import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class EncryptionService {
  static const _keyAlias = 'app_encryption_key';
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: IOSAccessibility.first_unlock_this_device,
    ),
  );
  
  static Encrypter? _encrypter;
  static IV? _iv;
  
  static Future<void> initialize() async {
    await _initializeEncryption();
  }
  
  static Future<void> _initializeEncryption() async {
    // 获取或生成加密密钥
    String? keyString = await _storage.read(key: _keyAlias);
    
    if (keyString == null) {
      // 生成新密钥
      final key = Key.fromSecureRandom(32);
      keyString = key.base64;
      await _storage.write(key: _keyAlias, value: keyString);
    }
    
    final key = Key.fromBase64(keyString);
    _encrypter = Encrypter(AES(key));
    _iv = IV.fromSecureRandom(16);
  }
  
  static String encrypt(String plainText) {
    if (_encrypter == null || _iv == null) {
      throw Exception('Encryption service not initialized');
    }
    
    final encrypted = _encrypter!.encrypt(plainText, iv: _iv!);
    return encrypted.base64;
  }
  
  static String decrypt(String encryptedText) {
    if (_encrypter == null || _iv == null) {
      throw Exception('Encryption service not initialized');
    }
    
    final encrypted = Encrypted.fromBase64(encryptedText);
    return _encrypter!.decrypt(encrypted, iv: _iv!);
  }
  
  static String hashPassword(String password, String salt) {
    final bytes = utf8.encode(password + salt);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
  
  static String generateSalt() {
    final bytes = List<int>.generate(32, (i) => 
        DateTime.now().millisecondsSinceEpoch + i);
    return base64.encode(bytes);
  }
  
  static Future<void> secureStore(String key, String value) async {
    final encryptedValue = encrypt(value);
    await _storage.write(key: key, value: encryptedValue);
  }
  
  static Future<String?> secureRead(String key) async {
    final encryptedValue = await _storage.read(key: key);
    if (encryptedValue == null) return null;
    
    try {
      return decrypt(encryptedValue);
    } catch (e) {
      // 解密失败，可能是旧数据
      await _storage.delete(key: key);
      return null;
    }
  }
  
  static Future<void> secureDelete(String key) async {
    await _storage.delete(key: key);
  }
  
  static Future<void> clearAll() async {
    await _storage.deleteAll();
  }
}
```

## 7. 日志管理

### 7.1 日志服务

```dart
// lib/logging/logger_service.dart
import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';

class LoggerService {
  static Logger? _logger;
  static File? _logFile;
  static final List<LogEntry> _logBuffer = [];
  static Timer? _flushTimer;
  
  static Logger get logger {
    if (_logger == null) {
      throw Exception('Logger not initialized. Call LoggerService.initialize() first.');
    }
    return _logger!;
  }
  
  static Future<void> initialize() async {
    await _initializeLogFile();
    
    _logger = Logger(
      printer: _CustomLogPrinter(),
      output: _CustomLogOutput(),
      level: _getLogLevel(),
    );
    
    // 定期刷新日志到文件
    _flushTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _flushLogs(),
    );
  }
  
  static void dispose() {
    _flushTimer?.cancel();
    _flushLogs();
  }
  
  static Future<void> _initializeLogFile() async {
    if (!ConfigManager.config.enableLogging) return;
    
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logDir = Directory('${directory.path}/logs');
      
      if (!await logDir.exists()) {
        await logDir.create(recursive: true);
      }
      
      final now = DateTime.now();
      final fileName = 'app_${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}.log';
      _logFile = File('${logDir.path}/$fileName');
      
      // 清理旧日志文件
      await _cleanupOldLogs(logDir);
    } catch (e) {
      print('Failed to initialize log file: $e');
    }
  }
  
  static Future<void> _cleanupOldLogs(Directory logDir) async {
    try {
      final files = await logDir.list().toList();
      final now = DateTime.now();
      
      for (final file in files) {
        if (file is File && file.path.endsWith('.log')) {
          final stat = await file.stat();
          final age = now.difference(stat.modified).inDays;
          
          if (age > 7) { // 保留 7 天的日志
            await file.delete();
          }
        }
      }
    } catch (e) {
      print('Failed to cleanup old logs: $e');
    }
  }
  
  static Level _getLogLevel() {
    if (EnvironmentConfig.isProduction) {
      return Level.warning;
    } else if (EnvironmentConfig.isStaging) {
      return Level.info;
    } else {
      return Level.debug;
    }
  }
  
  static void _flushLogs() {
    if (_logBuffer.isEmpty || _logFile == null) return;
    
    try {
      final logs = _logBuffer.map((entry) => entry.toJson()).toList();
      final logString = logs.map((log) => jsonEncode(log)).join('\n');
      
      _logFile!.writeAsStringSync(
        '$logString\n',
        mode: FileMode.append,
      );
      
      _logBuffer.clear();
    } catch (e) {
      print('Failed to flush logs: $e');
    }
  }
  
  static void debug(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger?.d(message, error, stackTrace);
  }
  
  static void info(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger?.i(message, error, stackTrace);
  }
  
  static void warning(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger?.w(message, error, stackTrace);
  }
  
  static void error(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger?.e(message, error, stackTrace);
    
    // 错误日志同时发送到监控服务
    AppMonitor.recordError(error ?? message, stackTrace);
  }
  
  static void fatal(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger?.f(message, error, stackTrace);
    
    // 致命错误立即发送到监控服务
    AppMonitor.recordError(error ?? message, stackTrace);
  }
}

class _CustomLogPrinter extends LogPrinter {
  @override
  List<String> log(LogEvent event) {
    final color = PrettyPrinter.levelColors[event.level];
    final emoji = PrettyPrinter.levelEmojis[event.level];
    final timestamp = DateTime.now().toIso8601String();
    
    return [color!('$emoji [$timestamp] ${event.level.name.toUpperCase()}: ${event.message}')];
  }
}

class _CustomLogOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    // 控制台输出
    if (kDebugMode) {
      for (final line in event.lines) {
        print(line);
      }
    }
    
    // 添加到缓冲区
    if (ConfigManager.config.enableLogging) {
      final logEntry = LogEntry(
        level: event.level.name,
        message: event.lines.join('\n'),
        timestamp: DateTime.now(),
        environment: EnvironmentConfig.environmentName,
      );
      
      LoggerService._logBuffer.add(logEntry);
      
      // 如果缓冲区太大，立即刷新
      if (LoggerService._logBuffer.length > 100) {
        LoggerService._flushLogs();
      }
    }
  }
}

class LogEntry {
  final String level;
  final String message;
  final DateTime timestamp;
  final String environment;
  
  LogEntry({
    required this.level,
    required this.message,
    required this.timestamp,
    required this.environment,
  });
  
  Map<String, dynamic> toJson() => {
    'level': level,
    'message': message,
    'timestamp': timestamp.toIso8601String(),
    'environment': environment,
  };
}
```

## 8. 备份和恢复

### 8.1 数据备份服务

```dart
// lib/backup/backup_service.dart
import 'dart:io';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:archive/archive_io.dart';

class BackupService {
  static const String _backupFileName = 'app_backup.zip';
  
  static Future<File> createBackup() async {
    try {
      final backupData = await _collectBackupData();
      final backupFile = await _createBackupFile(backupData);
      
      LoggerService.info('Backup created successfully: ${backupFile.path}');
      return backupFile;
    } catch (error, stackTrace) {
      LoggerService.error('Failed to create backup', error, stackTrace);
      rethrow;
    }
  }
  
  static Future<void> restoreBackup(File backupFile) async {
    try {
      final backupData = await _extractBackupFile(backupFile);
      await _restoreBackupData(backupData);
      
      LoggerService.info('Backup restored successfully');
    } catch (error, stackTrace) {
      LoggerService.error('Failed to restore backup', error, stackTrace);
      rethrow;
    }
  }
  
  static Future<BackupData> _collectBackupData() async {
    // 收集数据库数据
    final databaseData = await _backupDatabase();
    
    // 收集用户偏好设置
    final preferences = await _backupPreferences();
    
    // 收集用户文件
    final userFiles = await _backupUserFiles();
    
    return BackupData(
      version: '1.0',
      timestamp: DateTime.now(),
      databaseData: databaseData,
      preferences: preferences,
      userFiles: userFiles,
    );
  }
  
  static Future<Map<String, dynamic>> _backupDatabase() async {
    // 这里需要根据实际的数据库实现来备份数据
    // 示例：备份 SQLite 数据库
    try {
      final databaseService = GetIt.instance<DatabaseService>();
      final tables = ['users', 'settings', 'cache']; // 需要备份的表
      final backup = <String, List<Map<String, dynamic>>>{};
      
      for (final table in tables) {
        final data = await databaseService.query('SELECT * FROM $table');
        backup[table] = data;
      }
      
      return backup;
    } catch (e) {
      LoggerService.warning('Failed to backup database: $e');
      return {};
    }
  }
  
  static Future<Map<String, dynamic>> _backupPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      final backup = <String, dynamic>{};
      
      for (final key in keys) {
        final value = prefs.get(key);
        backup[key] = value;
      }
      
      return backup;
    } catch (e) {
      LoggerService.warning('Failed to backup preferences: $e');
      return {};
    }
  }
  
  static Future<List<FileBackup>> _backupUserFiles() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final userFilesDir = Directory('${directory.path}/user_files');
      
      if (!await userFilesDir.exists()) {
        return [];
      }
      
      final files = await userFilesDir.list(recursive: true).toList();
      final backups = <FileBackup>[];
      
      for (final file in files) {
        if (file is File) {
          final relativePath = file.path.replaceFirst(userFilesDir.path, '');
          final content = await file.readAsBytes();
          
          backups.add(FileBackup(
            path: relativePath,
            content: base64.encode(content),
            lastModified: (await file.stat()).modified,
          ));
        }
      }
      
      return backups;
    } catch (e) {
      LoggerService.warning('Failed to backup user files: $e');
      return [];
    }
  }
  
  static Future<File> _createBackupFile(BackupData backupData) async {
    final directory = await getApplicationDocumentsDirectory();
    final backupDir = Directory('${directory.path}/backups');
    
    if (!await backupDir.exists()) {
      await backupDir.create(recursive: true);
    }
    
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final backupFile = File('${backupDir.path}/backup_$timestamp.zip');
    
    // 创建 ZIP 文件
    final archive = Archive();
    
    // 添加备份数据
    final jsonData = jsonEncode(backupData.toJson());
    final jsonBytes = utf8.encode(jsonData);
    archive.addFile(ArchiveFile('backup.json', jsonBytes.length, jsonBytes));
    
    // 写入 ZIP 文件
    final zipData = ZipEncoder().encode(archive);
    await backupFile.writeAsBytes(zipData!);
    
    return backupFile;
  }
  
  static Future<BackupData> _extractBackupFile(File backupFile) async {
    final bytes = await backupFile.readAsBytes();
    final archive = ZipDecoder().decodeBytes(bytes);
    
    for (final file in archive) {
      if (file.name == 'backup.json') {
        final jsonData = utf8.decode(file.content as List<int>);
        final json = jsonDecode(jsonData) as Map<String, dynamic>;
        return BackupData.fromJson(json);
      }
    }
    
    throw Exception('Invalid backup file: backup.json not found');
  }
  
  static Future<void> _restoreBackupData(BackupData backupData) async {
    // 恢复数据库数据
    await _restoreDatabase(backupData.databaseData);
    
    // 恢复用户偏好设置
    await _restorePreferences(backupData.preferences);
    
    // 恢复用户文件
    await _restoreUserFiles(backupData.userFiles);
  }
  
  static Future<void> _restoreDatabase(Map<String, dynamic> databaseData) async {
    try {
      final databaseService = GetIt.instance<DatabaseService>();
      
      for (final entry in databaseData.entries) {
        final tableName = entry.key;
        final tableData = entry.value as List<dynamic>;
        
        // 清空现有数据
        await databaseService.execute('DELETE FROM $tableName');
        
        // 插入备份数据
        for (final row in tableData) {
          final rowData = row as Map<String, dynamic>;
          await databaseService.insert(tableName, rowData);
        }
      }
      
      LoggerService.info('Database restored successfully');
    } catch (e) {
      LoggerService.error('Failed to restore database: $e');
    }
  }
  
  static Future<void> _restorePreferences(Map<String, dynamic> preferences) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 清空现有偏好设置
      await prefs.clear();
      
      // 恢复备份的偏好设置
      for (final entry in preferences.entries) {
        final key = entry.key;
        final value = entry.value;
        
        if (value is String) {
          await prefs.setString(key, value);
        } else if (value is int) {
          await prefs.setInt(key, value);
        } else if (value is double) {
          await prefs.setDouble(key, value);
        } else if (value is bool) {
          await prefs.setBool(key, value);
        } else if (value is List<String>) {
          await prefs.setStringList(key, value);
        }
      }
      
      LoggerService.info('Preferences restored successfully');
    } catch (e) {
      LoggerService.error('Failed to restore preferences: $e');
    }
  }
  
  static Future<void> _restoreUserFiles(List<FileBackup> userFiles) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final userFilesDir = Directory('${directory.path}/user_files');
      
      // 清空现有用户文件
      if (await userFilesDir.exists()) {
        await userFilesDir.delete(recursive: true);
      }
      
      await userFilesDir.create(recursive: true);
      
      // 恢复备份的文件
      for (final fileBackup in userFiles) {
        final filePath = '${userFilesDir.path}${fileBackup.path}';
        final file = File(filePath);
        
        // 确保目录存在
        await file.parent.create(recursive: true);
        
        // 写入文件内容
        final content = base64.decode(fileBackup.content);
        await file.writeAsBytes(content);
        
        // 恢复文件修改时间
        // 注意：某些平台可能不支持设置文件时间
      }
      
      LoggerService.info('User files restored successfully');
    } catch (e) {
      LoggerService.error('Failed to restore user files: $e');
    }
  }
}

class BackupData {
  final String version;
  final DateTime timestamp;
  final Map<String, dynamic> databaseData;
  final Map<String, dynamic> preferences;
  final List<FileBackup> userFiles;
  
  BackupData({
    required this.version,
    required this.timestamp,
    required this.databaseData,
    required this.preferences,
    required this.userFiles,
  });
  
  Map<String, dynamic> toJson() => {
    'version': version,
    'timestamp': timestamp.toIso8601String(),
    'databaseData': databaseData,
    'preferences': preferences,
    'userFiles': userFiles.map((f) => f.toJson()).toList(),
  };
  
  factory BackupData.fromJson(Map<String, dynamic> json) => BackupData(
    version: json['version'] as String,
    timestamp: DateTime.parse(json['timestamp'] as String),
    databaseData: json['databaseData'] as Map<String, dynamic>,
    preferences: json['preferences'] as Map<String, dynamic>,
    userFiles: (json['userFiles'] as List<dynamic>)
        .map((f) => FileBackup.fromJson(f as Map<String, dynamic>))
        .toList(),
  );
}

class FileBackup {
  final String path;
  final String content; // Base64 encoded
  final DateTime lastModified;
  
  FileBackup({
    required this.path,
    required this.content,
    required this.lastModified,
  });
  
  Map<String, dynamic> toJson() => {
    'path': path,
    'content': content,
    'lastModified': lastModified.toIso8601String(),
  };
  
  factory FileBackup.fromJson(Map<String, dynamic> json) => FileBackup(
     path: json['path'] as String,
     content: json['content'] as String,
     lastModified: DateTime.parse(json['lastModified'] as String),
   );
 }
 ```

## 9. 运维脚本和工具

### 9.1 部署脚本

```bash
#!/bin/bash
# scripts/deploy.sh

set -e

ENVIRONMENT=${1:-staging}
PLATFORM=${2:-web}
VERSION=${3:-latest}

echo "🚀 Deploying $PLATFORM to $ENVIRONMENT environment (version: $VERSION)"

# 验证参数
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
  echo "❌ Invalid environment: $ENVIRONMENT"
  echo "Valid environments: development, staging, production"
  exit 1
fi

if [[ ! "$PLATFORM" =~ ^(web|android|ios)$ ]]; then
  echo "❌ Invalid platform: $PLATFORM"
  echo "Valid platforms: web, android, ios"
  exit 1
fi

# 设置环境变量
export ENVIRONMENT
export PLATFORM
export VERSION

# 构建应用
echo "📦 Building application..."
./scripts/build.sh $ENVIRONMENT $PLATFORM release

# 运行测试
echo "🧪 Running tests..."
flutter test

# 部署到对应平台
case $PLATFORM in
  "web")
    echo "🌐 Deploying to web..."
    ./scripts/deploy_web.sh $ENVIRONMENT
    ;;
  "android")
    echo "🤖 Deploying to Android..."
    ./scripts/deploy_android.sh $ENVIRONMENT
    ;;
  "ios")
    echo "🍎 Deploying to iOS..."
    ./scripts/deploy_ios.sh $ENVIRONMENT
    ;;
esac

echo "✅ Deployment completed successfully!"
```

```bash
#!/bin/bash
# scripts/deploy_web.sh

set -e

ENVIRONMENT=${1:-staging}

echo "🌐 Deploying web application to $ENVIRONMENT"

# 构建 Docker 镜像
echo "🐳 Building Docker image..."
docker build -f Dockerfile.web -t myapp-web:$VERSION .

# 推送到镜像仓库
echo "📤 Pushing to registry..."
docker tag myapp-web:$VERSION registry.example.com/myapp-web:$VERSION
docker push registry.example.com/myapp-web:$VERSION

if [ "$ENVIRONMENT" = "production" ]; then
  # 生产环境部署到 Kubernetes
  echo "☸️ Deploying to Kubernetes..."
  kubectl set image deployment/myapp-web web=registry.example.com/myapp-web:$VERSION -n myapp-production
  kubectl rollout status deployment/myapp-web -n myapp-production
  
  # 部署到 Firebase Hosting
  echo "🔥 Deploying to Firebase Hosting..."
  firebase deploy --project myapp-production --only hosting
  
else
  # 测试环境部署
  echo "🧪 Deploying to test environment..."
  docker-compose -f docker-compose.$ENVIRONMENT.yml up -d
fi

echo "✅ Web deployment completed!"
```

```bash
#!/bin/bash
# scripts/deploy_android.sh

set -e

ENVIRONMENT=${1:-staging}

echo "🤖 Deploying Android application to $ENVIRONMENT"

if [ "$ENVIRONMENT" = "production" ]; then
  # 生产环境发布到 Google Play
  echo "📱 Publishing to Google Play..."
  
  # 使用 Fastlane 发布
  cd android
  fastlane deploy_production
  cd ..
  
else
  # 测试环境发布到内部测试
  echo "🧪 Publishing to internal testing..."
  
  cd android
  fastlane deploy_internal
  cd ..
fi

echo "✅ Android deployment completed!"
```

```bash
#!/bin/bash
# scripts/deploy_ios.sh

set -e

ENVIRONMENT=${1:-staging}

echo "🍎 Deploying iOS application to $ENVIRONMENT"

if [ "$ENVIRONMENT" = "production" ]; then
  # 生产环境发布到 App Store
  echo "📱 Publishing to App Store..."
  
  # 使用 Fastlane 发布
  cd ios
  fastlane deploy_appstore
  cd ..
  
else
  # 测试环境发布到 TestFlight
  echo "✈️ Publishing to TestFlight..."
  
  cd ios
  fastlane deploy_testflight
  cd ..
fi

echo "✅ iOS deployment completed!"
```

### 9.2 监控脚本

```bash
#!/bin/bash
# scripts/health_check.sh

set -e

ENVIRONMENT=${1:-production}
SERVICE_URL=${2:-https://myapp.example.com}

echo "🏥 Performing health check for $ENVIRONMENT environment"

# 检查服务可用性
echo "🔍 Checking service availability..."
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $SERVICE_URL/health)

if [ "$HTTP_STATUS" -eq 200 ]; then
  echo "✅ Service is healthy (HTTP $HTTP_STATUS)"
else
  echo "❌ Service is unhealthy (HTTP $HTTP_STATUS)"
  exit 1
fi

# 检查响应时间
echo "⏱️ Checking response time..."
RESPONSE_TIME=$(curl -s -o /dev/null -w "%{time_total}" $SERVICE_URL)
RESPONSE_TIME_MS=$(echo "$RESPONSE_TIME * 1000" | bc)

echo "📊 Response time: ${RESPONSE_TIME_MS}ms"

if (( $(echo "$RESPONSE_TIME > 2.0" | bc -l) )); then
  echo "⚠️ Warning: Response time is high (>${RESPONSE_TIME_MS}ms)"
fi

# 检查 SSL 证书
echo "🔒 Checking SSL certificate..."
SSL_EXPIRY=$(echo | openssl s_client -servername $(echo $SERVICE_URL | sed 's|https://||') -connect $(echo $SERVICE_URL | sed 's|https://||'):443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
SSL_EXPIRY_EPOCH=$(date -d "$SSL_EXPIRY" +%s)
CURRENT_EPOCH=$(date +%s)
DAYS_UNTIL_EXPIRY=$(( (SSL_EXPIRY_EPOCH - CURRENT_EPOCH) / 86400 ))

echo "📅 SSL certificate expires in $DAYS_UNTIL_EXPIRY days"

if [ "$DAYS_UNTIL_EXPIRY" -lt 30 ]; then
  echo "⚠️ Warning: SSL certificate expires soon!"
fi

# 检查数据库连接
echo "🗄️ Checking database connectivity..."
DB_CHECK=$(curl -s $SERVICE_URL/api/health/database | jq -r '.status')

if [ "$DB_CHECK" = "healthy" ]; then
  echo "✅ Database is healthy"
else
  echo "❌ Database is unhealthy"
  exit 1
fi

echo "✅ All health checks passed!"
```

```bash
#!/bin/bash
# scripts/performance_monitor.sh

set -e

ENVIRONMENT=${1:-production}
SERVICE_URL=${2:-https://myapp.example.com}
DURATION=${3:-300} # 5 minutes

echo "📊 Starting performance monitoring for $DURATION seconds"

# 创建结果目录
RESULT_DIR="performance_results/$(date +%Y%m%d_%H%M%S)"
mkdir -p $RESULT_DIR

# 监控 CPU 和内存使用
echo "💻 Monitoring system resources..."
top -b -n $(($DURATION / 5)) -d 5 > $RESULT_DIR/system_resources.log &

# 监控网络延迟
echo "🌐 Monitoring network latency..."
for i in $(seq 1 $(($DURATION / 10))); do
  ping -c 1 $(echo $SERVICE_URL | sed 's|https://||' | sed 's|http://||') >> $RESULT_DIR/ping.log
  sleep 10
done &

# 监控 HTTP 响应时间
echo "⏱️ Monitoring HTTP response times..."
for i in $(seq 1 $(($DURATION / 5))); do
  RESPONSE_TIME=$(curl -s -o /dev/null -w "%{time_total}" $SERVICE_URL)
  echo "$(date): ${RESPONSE_TIME}s" >> $RESULT_DIR/response_times.log
  sleep 5
done &

# 等待所有监控任务完成
wait

# 生成报告
echo "📋 Generating performance report..."
python3 scripts/generate_performance_report.py $RESULT_DIR

echo "✅ Performance monitoring completed. Results saved to $RESULT_DIR"
```

### 9.3 维护脚本

```bash
#!/bin/bash
# scripts/maintenance.sh

set -e

ACTION=${1:-status}
ENVIRONMENT=${2:-production}

echo "🔧 Performing maintenance action: $ACTION for $ENVIRONMENT"

case $ACTION in
  "enable")
    echo "🚧 Enabling maintenance mode..."
    kubectl patch ingress myapp-web-ingress -n myapp-$ENVIRONMENT -p '{"metadata":{"annotations":{"nginx.ingress.kubernetes.io/default-backend":"maintenance-service"}}}'
    echo "✅ Maintenance mode enabled"
    ;;
    
  "disable")
    echo "🟢 Disabling maintenance mode..."
    kubectl patch ingress myapp-web-ingress -n myapp-$ENVIRONMENT -p '{"metadata":{"annotations":{"nginx.ingress.kubernetes.io/default-backend":null}}}'
    echo "✅ Maintenance mode disabled"
    ;;
    
  "status")
    echo "📊 Checking maintenance status..."
    MAINTENANCE_STATUS=$(kubectl get ingress myapp-web-ingress -n myapp-$ENVIRONMENT -o jsonpath='{.metadata.annotations.nginx\.ingress\.kubernetes\.io/default-backend}')
    
    if [ "$MAINTENANCE_STATUS" = "maintenance-service" ]; then
      echo "🚧 Maintenance mode is ENABLED"
    else
      echo "🟢 Maintenance mode is DISABLED"
    fi
    ;;
    
  "backup")
    echo "💾 Creating system backup..."
    ./scripts/backup_system.sh $ENVIRONMENT
    ;;
    
  "cleanup")
    echo "🧹 Performing system cleanup..."
    ./scripts/cleanup_system.sh $ENVIRONMENT
    ;;
    
  *)
    echo "❌ Unknown action: $ACTION"
    echo "Available actions: enable, disable, status, backup, cleanup"
    exit 1
    ;;
esac
```

```bash
#!/bin/bash
# scripts/backup_system.sh

set -e

ENVIRONMENT=${1:-production}
BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"

echo "💾 Creating system backup for $ENVIRONMENT environment"

mkdir -p $BACKUP_DIR

# 备份数据库
echo "🗄️ Backing up database..."
kubectl exec -n myapp-$ENVIRONMENT deployment/postgres -- pg_dump -U postgres myapp > $BACKUP_DIR/database.sql

# 备份配置文件
echo "⚙️ Backing up configurations..."
kubectl get configmaps -n myapp-$ENVIRONMENT -o yaml > $BACKUP_DIR/configmaps.yaml
kubectl get secrets -n myapp-$ENVIRONMENT -o yaml > $BACKUP_DIR/secrets.yaml

# 备份持久化存储
echo "💿 Backing up persistent volumes..."
kubectl get pv -o yaml > $BACKUP_DIR/persistent_volumes.yaml
kubectl get pvc -n myapp-$ENVIRONMENT -o yaml > $BACKUP_DIR/persistent_volume_claims.yaml

# 压缩备份
echo "🗜️ Compressing backup..."
tar -czf $BACKUP_DIR.tar.gz -C backups $(basename $BACKUP_DIR)
rm -rf $BACKUP_DIR

# 上传到云存储
echo "☁️ Uploading to cloud storage..."
aws s3 cp $BACKUP_DIR.tar.gz s3://myapp-backups/$ENVIRONMENT/

echo "✅ System backup completed: $BACKUP_DIR.tar.gz"
```

## 10. 最佳实践和建议

### 10.1 部署最佳实践

1. **环境隔离**
   - 使用不同的环境配置
   - 独立的数据库和服务
   - 分离的监控和日志

2. **版本管理**
   - 语义化版本控制
   - Git 标签管理
   - 回滚策略

3. **自动化部署**
   - CI/CD 流水线
   - 自动化测试
   - 蓝绿部署

4. **安全考虑**
   - 密钥管理
   - 网络安全
   - 访问控制

### 10.2 监控最佳实践

1. **全面监控**
   - 应用性能监控
   - 基础设施监控
   - 业务指标监控

2. **告警策略**
   - 分级告警
   - 告警聚合
   - 静默策略

3. **日志管理**
   - 结构化日志
   - 日志聚合
   - 日志分析

### 10.3 性能优化建议

1. **构建优化**
   - 代码分割
   - 资源压缩
   - 缓存策略

2. **运行时优化**
   - 内存管理
   - 网络优化
   - 渲染优化

3. **基础设施优化**
   - CDN 使用
   - 负载均衡
   - 数据库优化

### 10.4 安全最佳实践

1. **应用安全**
   - 代码混淆
   - 证书绑定
   - 运行时保护

2. **数据安全**
   - 数据加密
   - 安全存储
   - 传输加密

3. **网络安全**
   - HTTPS 强制
   - 证书管理
   - 防火墙配置

## 总结

本文档提供了 Flutter 企业级应用部署和运维的完整实现方案，包括：

1. **多环境配置管理** - 支持开发、测试、生产环境的配置隔离
2. **CI/CD 流水线** - 自动化构建、测试和部署流程
3. **容器化部署** - Docker 和 Kubernetes 部署方案
4. **监控告警系统** - 应用性能监控和错误追踪
5. **安全防护机制** - 应用安全检查和数据加密
6. **日志管理系统** - 结构化日志记录和分析
7. **备份恢复方案** - 数据备份和灾难恢复
8. **运维工具脚本** - 自动化运维和维护工具

这套方案遵循 DevOps 最佳实践，提供了完整的部署和运维解决方案，确保应用的稳定性、安全性和可维护性。通过这些实现，可以构建一个健壮的企业级 Flutter 应用运维体系。
```