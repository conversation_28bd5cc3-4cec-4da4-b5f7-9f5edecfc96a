# 项目结构示例

## 完整项目目录结构

```
flutter_enterprise_app/
├── melos.yaml                          # Melos配置文件
├── pubspec.yaml                        # 根pubspec.yaml
├── README.md                           # 项目说明文档
├── .gitignore                          # Git忽略文件
├── analysis_options.yaml               # 代码分析配置
├── .github/                            # GitHub Actions配置
│   └── workflows/
│       ├── ci.yml                      # 持续集成
│       └── release.yml                 # 发布流程
│
├── apps/                               # 应用目录
│   └── mobile_app/                     # 主移动应用
│       ├── pubspec.yaml
│       ├── lib/
│       │   ├── main.dart               # 应用入口
│       │   ├── main_china.dart         # 中国版入口
│       │   ├── main_global.dart        # 国际版入口
│       │   ├── app/
│       │   │   ├── app.dart            # 应用配置
│       │   │   ├── router/             # 路由配置
│       │   │   └── theme/              # 主题配置
│       │   ├── di/                     # 依赖注入配置
│       │   │   ├── injection.dart      # 主入口
│       │   │   ├── injection.config.dart # 生成的配置
│       │   │   └── modules/            # DI模块
│       │   └── features/               # 功能页面
│       │       ├── splash/
│       │       ├── auth/
│       │       ├── home/
│       │       └── profile/
│       ├── android/                    # Android配置
│       │   ├── app/
│       │   │   ├── build.gradle        # 构建配置
│       │   │   └── src/
│       │   │       ├── china/          # 中国版配置
│       │   │       ├── global/         # 国际版配置
│       │   │       └── main/           # 主配置
│       │   └── gradle.properties
│       ├── ios/                        # iOS配置
│       │   ├── Runner.xcworkspace
│       │   ├── Runner/
│       │   │   ├── Info.plist
│       │   │   └── Configurations/     # 配置文件
│       │   │       ├── China-Debug.xcconfig
│       │   │       ├── China-Release.xcconfig
│       │   │       ├── Global-Debug.xcconfig
│       │   │       └── Global-Release.xcconfig
│       │   └── Schemes/                # Xcode Schemes
│       │       ├── China.xcscheme
│       │       └── Global.xcscheme
│       ├── test/                       # 单元测试
│       ├── integration_test/           # 集成测试
│       └── assets/                     # 资源文件
│           ├── images/
│           ├── fonts/
│           └── translations/
│
├── packages/                           # 包目录
│   ├── features/                       # 功能包
│   │   ├── feature_auth/               # 认证功能
│   │   │   ├── pubspec.yaml
│   │   │   ├── lib/
│   │   │   │   ├── feature_auth.dart   # 导出文件
│   │   │   │   ├── src/
│   │   │   │   │   ├── data/           # 数据层
│   │   │   │   │   │   ├── datasources/
│   │   │   │   │   │   ├── models/
│   │   │   │   │   │   └── repositories/
│   │   │   │   │   ├── domain/         # 领域层
│   │   │   │   │   │   ├── entities/
│   │   │   │   │   │   ├── repositories/
│   │   │   │   │   │   └── usecases/
│   │   │   │   │   └── presentation/   # 表现层
│   │   │   │   │       ├── bloc/
│   │   │   │   │       ├── pages/
│   │   │   │   │       └── widgets/
│   │   │   │   └── di/                 # 依赖注入
│   │   │   └── test/                   # 测试
│   │   │
│   │   ├── feature_profile/            # 用户资料功能
│   │   ├── feature_settings/           # 设置功能
│   │   └── feature_payment/            # 支付功能
│   │
│   ├── core/                           # 核心包
│   │   ├── core_network/               # 网络模块
│   │   │   ├── pubspec.yaml
│   │   │   ├── lib/
│   │   │   │   ├── core_network.dart
│   │   │   │   └── src/
│   │   │   │       ├── dio_client.dart
│   │   │   │       ├── interceptors/
│   │   │   │       ├── models/
│   │   │   │       └── exceptions/
│   │   │   └── test/
│   │   │
│   │   ├── core_database/              # 数据库模块
│   │   │   ├── pubspec.yaml
│   │   │   ├── lib/
│   │   │   │   ├── core_database.dart
│   │   │   │   └── src/
│   │   │   │       ├── app_database.dart
│   │   │   │       ├── tables/
│   │   │   │       ├── daos/
│   │   │   │       └── migrations/
│   │   │   └── test/
│   │   │
│   │   ├── core_analytics/             # 分析模块
│   │   ├── core_storage/               # 存储模块
│   │   ├── core_auth/                  # 认证核心
│   │   ├── core_error/                 # 错误处理
│   │   ├── core_utils/                 # 工具类
│   │   └── core_constants/             # 常量定义
│   │
│   └── shared/                         # 共享包
│       ├── ui_kit/                     # UI组件库
│       │   ├── pubspec.yaml
│       │   ├── lib/
│       │   │   ├── ui_kit.dart
│       │   │   └── src/
│       │   │       ├── theme/          # 主题系统
│       │   │       │   ├── app_theme.dart
│       │   │       │   ├── colors.dart
│       │   │       │   ├── typography.dart
│       │   │       │   └── spacing.dart
│       │   │       ├── components/     # 基础组件
│       │   │       │   ├── buttons/
│       │   │       │   ├── inputs/
│       │   │       │   ├── cards/
│       │   │       │   ├── dialogs/
│       │   │       │   └── navigation/
│       │   │       ├── layouts/        # 布局组件
│       │   │       │   ├── responsive_layout.dart
│       │   │       │   ├── adaptive_layout.dart
│       │   │       │   └── breakpoints.dart
│       │   │       └── extensions/     # 扩展方法
│       │   └── test/
│       │
│       ├── shared_models/              # 共享数据模型
│       │   ├── pubspec.yaml
│       │   ├── lib/
│       │   │   ├── shared_models.dart
│       │   │   └── src/
│       │   │       ├── user/
│       │   │       ├── common/
│       │   │       └── api/
│       │   └── test/
│       │
│       └── shared_utils/               # 共享工具
│           ├── pubspec.yaml
│           ├── lib/
│           │   ├── shared_utils.dart
│           │   └── src/
│           │       ├── validators/
│           │       ├── formatters/
│           │       ├── extensions/
│           │       └── helpers/
│           └── test/
│
├── tools/                              # 工具脚本
│   ├── build_runner.sh                 # 代码生成脚本
│   ├── test_runner.sh                  # 测试运行脚本
│   ├── flavor_builder.sh               # 多环境构建脚本
│   └── code_generator/                 # 代码生成工具
│       ├── templates/
│       └── generators/
│
├── docs/                               # 文档目录
│   ├── architecture.md                # 架构文档
│   ├── api/                           # API文档
│   ├── deployment/                    # 部署文档
│   └── development/                   # 开发文档
│
└── scripts/                           # 脚本目录
    ├── setup.sh                       # 环境设置
    ├── clean.sh                       # 清理脚本
    └── release.sh                     # 发布脚本
```

## 关键配置文件

### melos.yaml
```yaml
name: flutter_enterprise_app
repository: https://github.com/company/flutter_enterprise_app

packages:
  - apps/**
  - packages/**

command:
  version:
    # 生成CHANGELOG.md
    updateGitTagRefs: true
    releaseUrl: true
  
  bootstrap:
    # 运行pub get for all packages
    runPubGetInParallel: false
    
scripts:
  # 代码生成
  generate:
    run: |
      melos exec -c 1 -- \
        dart run build_runner build --delete-conflicting-outputs
    description: 运行代码生成
    
  # 运行测试
  test:
    run: melos exec -- flutter test
    description: 运行所有包的测试
    
  # 代码分析
  analyze:
    run: melos exec -- flutter analyze
    description: 分析所有包的代码
    
  # 格式化代码
  format:
    run: melos exec -- dart format .
    description: 格式化所有包的代码
    
  # 清理
  clean:
    run: melos exec -- flutter clean
    description: 清理所有包
```

### 根目录 pubspec.yaml
```yaml
name: flutter_enterprise_app
description: 企业级Flutter应用
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: '>=3.10.0'

dev_dependencies:
  melos: ^3.1.0
  very_good_analysis: ^5.0.0
```

### analysis_options.yaml
```yaml
include: package:very_good_analysis/analysis_options.yaml

linter:
  rules:
    # 自定义规则
    prefer_single_quotes: true
    require_trailing_commas: true
    sort_constructors_first: true
    sort_unnamed_constructors_first: true
    
analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.config.dart"
    - "**/generated/**"
  
  errors:
    invalid_annotation_target: ignore
    missing_required_param: error
    missing_return: error
```

## 目录说明

### apps/
包含所有应用程序，通常只有一个主应用，但可以包含多个应用（如管理后台、演示应用等）。

### packages/features/
功能包，每个包代表一个独立的业务功能模块，遵循Clean Architecture。

### packages/core/
核心基础设施包，提供应用的基础能力，如网络、数据库、认证等。

### packages/shared/
共享包，包含UI组件库、共享模型、工具类等可复用组件。

### tools/
开发工具和脚本，用于自动化开发流程。

### docs/
项目文档，包括架构设计、API文档、部署指南等。

## 命名约定

- **包名**: `snake_case`，如 `feature_auth`、`core_network`
- **文件名**: `snake_case.dart`
- **目录名**: `snake_case`
- **类名**: `PascalCase`
- **变量/方法**: `camelCase`
- **常量**: `UPPER_SNAKE_CASE`

## 依赖关系

```
apps/mobile_app
├── depends on packages/features/*
├── depends on packages/core/*
└── depends on packages/shared/*

packages/features/*
├── depends on packages/core/*
└── depends on packages/shared/*

packages/core/*
└── depends on packages/shared/* (limited)

packages/shared/*
└── no dependencies on other packages
```

## 开发流程

1. **初始化项目**: `melos bootstrap`
2. **代码生成**: `melos run generate`
3. **运行测试**: `melos run test`
4. **代码分析**: `melos run analyze`
5. **格式化代码**: `melos run format`
6. **清理项目**: `melos run clean`

这种项目结构确保了：
- 模块化和可维护性
- 清晰的依赖关系
- 代码复用
- 独立开发和测试
- 易于扩展和重构