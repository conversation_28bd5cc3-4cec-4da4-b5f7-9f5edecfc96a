# 阶段二：核心业务模块实现

## 阶段目标
基于阶段一建立的基础架构，实现核心业务模块，包括认证授权、网络通信、数据持久化、状态管理和错误处理等关键功能模块。

## 完成标准
- ✅ 认证授权模块完全实现并通过测试
- ✅ 网络通信层完整实现
- ✅ 数据持久化系统正常工作
- ✅ 状态管理机制完善
- ✅ 错误处理系统健壮
- ✅ 所有核心模块集成测试通过
- ✅ 模块化架构验证通过

---

## 任务清单

### 1. 认证授权模块实现
**参照文档**: `/doc/code_examples/04_authentication/` 和 `/doc/code_examples/05_authorization/`

#### 1.1 认证服务核心实现
**参照文档**: `authentication_service.md`

**完成内容**:
```dart
// lib/features/auth/domain/entities/user.dart
class User {
  final String id;
  final String email;
  final String name;
  final List<String> roles;
  final DateTime? lastLoginAt;
  
  // 用户实体实现
}

// lib/features/auth/domain/repositories/auth_repository.dart
abstract class IAuthRepository {
  Future<Either<Failure, User>> login(String email, String password);
  Future<Either<Failure, User>> register(UserRegistration registration);
  Future<Either<Failure, void>> logout();
  Future<Either<Failure, User>> refreshToken();
  Future<Either<Failure, User?>> getCurrentUser();
}

// lib/features/auth/domain/usecases/login_usecase.dart
class LoginUseCase implements UseCaseBase<User, LoginParams> {
  final IAuthRepository repository;
  
  @override
  Future<Either<Failure, User>> call(LoginParams params) async {
    // 登录业务逻辑实现
  }
}
```

**关键功能**:
- JWT Token 管理
- 自动刷新机制
- 生物识别认证支持
- 多因素认证（MFA）
- 社交登录集成

**验证方式**:
- 登录/注册流程完整测试
- Token 刷新机制验证
- 异常情况处理测试
- 安全性测试通过

#### 1.2 授权权限管理实现
**参照文档**: `authorization_system.md`

**完成内容**:
```dart
// lib/features/authorization/domain/entities/permission.dart
class Permission {
  final String id;
  final String name;
  final String resource;
  final String action;
  final Map<String, dynamic>? conditions;
}

// lib/features/authorization/domain/entities/role.dart
class Role {
  final String id;
  final String name;
  final List<Permission> permissions;
  final int priority;
}

// lib/features/authorization/domain/services/permission_service.dart
abstract class IPermissionService {
  Future<bool> hasPermission(String userId, String resource, String action);
  Future<List<Permission>> getUserPermissions(String userId);
  Future<bool> hasRole(String userId, String roleName);
}
```

**关键功能**:
- 基于角色的访问控制（RBAC）
- 细粒度权限控制
- 动态权限验证
- 权限缓存机制
- 权限继承支持

**模块化实现**:
```dart
// 条件注册授权服务
if (FeatureConfig.instance.isFeatureEnabled(Features.authorization)) {
  getIt.registerLazySingleton<IPermissionService>(() => PermissionService());
} else {
  getIt.registerLazySingleton<IPermissionService>(() => NoOpPermissionService());
}
```

**验证方式**:
- 权限验证逻辑正确
- 角色继承机制有效
- 权限缓存性能良好
- 安全边界测试通过

#### 1.3 认证状态管理
**参照文档**: `auth_state_management.md`

**完成内容**:
```dart
// lib/features/auth/presentation/bloc/auth_bloc.dart
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final LoginUseCase loginUseCase;
  final LogoutUseCase logoutUseCase;
  final GetCurrentUserUseCase getCurrentUserUseCase;
  
  AuthBloc({
    required this.loginUseCase,
    required this.logoutUseCase,
    required this.getCurrentUserUseCase,
  }) : super(AuthInitial()) {
    on<AuthLoginRequested>(_onLoginRequested);
    on<AuthLogoutRequested>(_onLogoutRequested);
    on<AuthUserChanged>(_onUserChanged);
  }
}

// lib/features/auth/presentation/bloc/auth_state.dart
abstract class AuthState extends Equatable {
  const AuthState();
}

class AuthInitial extends AuthState {
  @override
  List<Object> get props => [];
}

class AuthLoading extends AuthState {
  @override
  List<Object> get props => [];
}

class AuthAuthenticated extends AuthState {
  final User user;
  
  const AuthAuthenticated(this.user);
  
  @override
  List<Object> get props => [user];
}
```

**验证方式**:
- 状态转换逻辑正确
- UI 响应状态变化及时
- 状态持久化正常
- 并发状态处理安全

---

### 2. 网络通信层实现
**参照文档**: `/doc/code_examples/06_networking/network_layer.md`

#### 2.1 HTTP 客户端配置
**完成内容**:
```dart
// lib/core/network/dio_client.dart
class DioClient {
  late final Dio _dio;
  
  DioClient() {
    _dio = Dio();
    _setupInterceptors();
    _setupTimeout();
  }
  
  void _setupInterceptors() {
    _dio.interceptors.addAll([
      AuthInterceptor(),
      LoggingInterceptor(),
      ErrorInterceptor(),
      RetryInterceptor(),
    ]);
  }
}

// lib/core/network/interceptors/auth_interceptor.dart
class AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // 自动添加认证头
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // 处理认证错误，自动刷新 Token
  }
}
```

**关键功能**:
- 自动认证头注入
- Token 自动刷新
- 请求重试机制
- 网络状态监控
- 请求/响应日志
- 缓存策略支持

**验证方式**:
- 网络请求正常发送
- 认证头自动添加
- 错误重试机制有效
- 日志记录完整

#### 2.2 API 服务抽象层
**完成内容**:
```dart
// lib/core/network/api_service.dart
abstract class ApiService {
  Future<ApiResponse<T>> get<T>(String path, {Map<String, dynamic>? queryParameters});
  Future<ApiResponse<T>> post<T>(String path, {dynamic data});
  Future<ApiResponse<T>> put<T>(String path, {dynamic data});
  Future<ApiResponse<T>> delete<T>(String path);
}

// lib/core/network/api_response.dart
class ApiResponse<T> {
  final T? data;
  final String? message;
  final int statusCode;
  final bool success;
  final Map<String, dynamic>? meta;
  
  // API 响应封装
}

// lib/core/network/network_exceptions.dart
class NetworkException implements Exception {
  final String message;
  final int? statusCode;
  final String? errorCode;
  
  // 网络异常定义
}
```

**验证方式**:
- API 调用接口统一
- 响应数据正确解析
- 异常处理机制完善
- 类型安全保证

#### 2.3 网络状态管理
**完成内容**:
```dart
// lib/core/network/connectivity_service.dart
class ConnectivityService {
  final Connectivity _connectivity = Connectivity();
  final StreamController<ConnectivityResult> _connectivityController = StreamController.broadcast();
  
  Stream<ConnectivityResult> get connectivityStream => _connectivityController.stream;
  
  Future<bool> get isConnected async {
    final result = await _connectivity.checkConnectivity();
    return result != ConnectivityResult.none;
  }
}

// lib/core/network/network_info.dart
abstract class INetworkInfo {
  Future<bool> get isConnected;
  Stream<bool> get connectivityStream;
}
```

**验证方式**:
- 网络状态检测准确
- 状态变化通知及时
- 离线模式处理正确
- 网络恢复自动重试

---

### 3. 数据持久化系统实现
**参照文档**: `/doc/code_examples/07_data_persistence/`

#### 3.1 本地数据库配置
**参照文档**: `local_database.md`

**完成内容**:
```dart
// lib/core/database/database_service.dart
abstract class IDatabaseService {
  Future<void> initialize();
  Future<void> close();
  Future<T?> get<T>(String key);
  Future<void> set<T>(String key, T value);
  Future<void> delete(String key);
  Future<void> clear();
}

// lib/core/database/hive_database_service.dart
class HiveDatabaseService implements IDatabaseService {
  late Box _box;
  
  @override
  Future<void> initialize() async {
    await Hive.initFlutter();
    _box = await Hive.openBox('app_data');
  }
}

// lib/core/database/secure_storage_service.dart
class SecureStorageService {
  final FlutterSecureStorage _storage = const FlutterSecureStorage();
  
  Future<void> storeSecurely(String key, String value) async {
    await _storage.write(key: key, value: value);
  }
}
```

**关键功能**:
- Hive 本地数据库
- 安全存储（敏感数据）
- 数据加密支持
- 数据迁移机制
- 缓存策略实现

**验证方式**:
- 数据存储读取正常
- 敏感数据安全存储
- 数据库初始化成功
- 数据迁移机制有效

#### 3.2 缓存管理系统
**参照文档**: `caching_strategy.md`

**完成内容**:
```dart
// lib/core/cache/cache_service.dart
abstract class ICacheService {
  Future<T?> get<T>(String key);
  Future<void> set<T>(String key, T value, {Duration? ttl});
  Future<void> delete(String key);
  Future<void> clear();
  Future<bool> exists(String key);
}

// lib/core/cache/memory_cache_service.dart
class MemoryCacheService implements ICacheService {
  final Map<String, CacheItem> _cache = {};
  
  @override
  Future<T?> get<T>(String key) async {
    final item = _cache[key];
    if (item != null && !item.isExpired) {
      return item.value as T;
    }
    _cache.remove(key);
    return null;
  }
}

// lib/core/cache/cache_item.dart
class CacheItem {
  final dynamic value;
  final DateTime createdAt;
  final Duration? ttl;
  
  bool get isExpired {
    if (ttl == null) return false;
    return DateTime.now().difference(createdAt) > ttl!;
  }
}
```

**关键功能**:
- 内存缓存
- 磁盘缓存
- TTL 过期机制
- LRU 淘汰策略
- 缓存统计监控

**验证方式**:
- 缓存命中率统计
- 过期机制正确
- 内存使用合理
- 缓存一致性保证

#### 3.3 数据同步机制
**完成内容**:
```dart
// lib/core/sync/sync_service.dart
abstract class ISyncService {
  Future<void> syncToServer();
  Future<void> syncFromServer();
  Future<void> resolveConflicts();
  Stream<SyncStatus> get syncStatusStream;
}

// lib/core/sync/conflict_resolver.dart
class ConflictResolver {
  ConflictResolution resolveConflict(LocalData local, RemoteData remote) {
    // 冲突解决策略
  }
}
```

**验证方式**:
- 数据同步正确
- 冲突解决有效
- 同步状态准确
- 网络异常处理

---

### 4. 状态管理系统完善
**参照文档**: `/doc/code_examples/08_state_management/bloc_pattern.md`

#### 4.1 全局状态管理
**完成内容**:
```dart
// lib/core/state/app_bloc.dart
class AppBloc extends Bloc<AppEvent, AppState> {
  final IAuthService authService;
  final IConfigService configService;
  
  AppBloc({
    required this.authService,
    required this.configService,
  }) : super(AppInitial()) {
    on<AppStarted>(_onAppStarted);
    on<AppConfigChanged>(_onConfigChanged);
  }
}

// lib/core/state/app_state.dart
abstract class AppState extends Equatable {
  const AppState();
}

class AppInitial extends AppState {
  @override
  List<Object> get props => [];
}

class AppLoaded extends AppState {
  final User? user;
  final AppConfig config;
  
  const AppLoaded({this.user, required this.config});
  
  @override
  List<Object?> get props => [user, config];
}
```

**关键功能**:
- 应用级状态管理
- 状态持久化
- 状态恢复机制
- 状态变化监听
- 跨模块状态共享

**验证方式**:
- 状态变化正确传播
- 状态持久化有效
- 内存泄漏检查通过
- 状态一致性保证

#### 4.2 模块状态隔离
**完成内容**:
```dart
// lib/core/state/module_state_manager.dart
class ModuleStateManager {
  final Map<String, BlocBase> _moduleBlocs = {};
  
  T getModuleBloc<T extends BlocBase>(String moduleId) {
    return _moduleBlocs[moduleId] as T;
  }
  
  void registerModuleBloc(String moduleId, BlocBase bloc) {
    _moduleBlocs[moduleId] = bloc;
  }
}

// lib/shared/widgets/feature_bloc_provider.dart
class FeatureBlocProvider<T extends BlocBase> extends StatelessWidget {
  final String featureName;
  final T Function() create;
  final Widget child;
  
  @override
  Widget build(BuildContext context) {
    if (!FeatureConfig.instance.isFeatureEnabled(featureName)) {
      return FeatureDisabledPage(featureName: featureName);
    }
    
    return BlocProvider<T>(
      create: (_) => create(),
      child: child,
    );
  }
}
```

**验证方式**:
- 模块状态独立
- 状态隔离有效
- 模块卸载清理
- 状态通信安全

---

### 5. 错误处理系统实现
**参照文档**: `/doc/code_examples/10_error_handling/error_handling_system.md`

#### 5.1 统一错误处理
**完成内容**:
```dart
// lib/core/errors/app_error.dart
abstract class AppError {
  final String message;
  final String? code;
  final dynamic originalError;
  
  const AppError(this.message, {this.code, this.originalError});
}

class NetworkError extends AppError {
  final int? statusCode;
  
  const NetworkError(String message, {this.statusCode, String? code}) 
      : super(message, code: code);
}

class ValidationError extends AppError {
  final Map<String, List<String>> fieldErrors;
  
  const ValidationError(String message, this.fieldErrors) : super(message);
}

// lib/core/errors/error_handler.dart
class GlobalErrorHandler {
  static void handleError(Object error, StackTrace stackTrace) {
    // 全局错误处理逻辑
    _logError(error, stackTrace);
    _reportError(error, stackTrace);
    _showUserFriendlyMessage(error);
  }
}
```

**关键功能**:
- 统一错误定义
- 全局错误捕获
- 错误日志记录
- 用户友好提示
- 错误上报机制

**验证方式**:
- 错误正确分类
- 错误信息准确
- 用户体验良好
- 错误上报成功

#### 5.2 业务异常处理
**完成内容**:
```dart
// lib/core/errors/business_exceptions.dart
class BusinessException implements Exception {
  final String message;
  final String code;
  final Map<String, dynamic>? data;
  
  const BusinessException(this.message, this.code, {this.data});
}

// lib/core/errors/error_recovery.dart
class ErrorRecoveryService {
  Future<bool> canRecover(AppError error) async {
    // 判断错误是否可恢复
  }
  
  Future<void> recover(AppError error) async {
    // 执行错误恢复逻辑
  }
}
```

**验证方式**:
- 业务异常正确处理
- 错误恢复机制有效
- 异常边界保护
- 系统稳定性保证

---

### 6. 模块集成和验证

#### 6.1 模块化架构验证
**完成内容**:
- 验证所有模块可以独立启用/禁用
- 确认 NoOp 服务正常工作
- 测试模块依赖关系
- 验证条件依赖注入

**验证脚本**:
```dart
// test/integration/modular_architecture_test.dart
void main() {
  group('Modular Architecture Tests', () {
    testWidgets('should work with all features enabled', (tester) async {
      // 测试所有功能启用的情况
    });
    
    testWidgets('should work with minimal features', (tester) async {
      // 测试最小功能配置
    });
    
    testWidgets('should handle feature dependencies', (tester) async {
      // 测试功能依赖关系
    });
  });
}
```

**验证方式**:
- 所有配置组合测试通过
- 模块切换无缝进行
- 依赖关系验证正确
- 性能影响可接受

#### 6.2 端到端集成测试
**完成内容**:
```dart
// test/integration/e2e_test.dart
void main() {
  group('End-to-End Integration Tests', () {
    testWidgets('complete user journey', (tester) async {
      // 完整用户流程测试
      await tester.pumpWidget(MyApp());
      
      // 测试登录流程
      await _testLoginFlow(tester);
      
      // 测试核心功能
      await _testCoreFeatures(tester);
      
      // 测试数据同步
      await _testDataSync(tester);
    });
  });
}
```

**验证方式**:
- 完整用户流程正常
- 模块间协作正确
- 数据流转正确
- 性能指标达标

---

## 阶段验收标准

### 功能验收
1. **认证授权模块**
   - [ ] 用户登录/注册功能正常
   - [ ] JWT Token 管理正确
   - [ ] 权限验证机制有效
   - [ ] 安全性测试通过

2. **网络通信模块**
   - [ ] HTTP 请求正常发送
   - [ ] 认证头自动注入
   - [ ] 错误重试机制有效
   - [ ] 网络状态监控准确

3. **数据持久化模块**
   - [ ] 本地数据库正常工作
   - [ ] 缓存机制有效
   - [ ] 数据同步正确
   - [ ] 安全存储功能正常

4. **状态管理模块**
   - [ ] 全局状态管理正确
   - [ ] 模块状态隔离有效
   - [ ] 状态持久化正常
   - [ ] 状态变化响应及时

5. **错误处理模块**
   - [ ] 统一错误处理有效
   - [ ] 用户友好提示正确
   - [ ] 错误恢复机制正常
   - [ ] 错误上报功能正常

### 质量验收
1. **代码质量**
   - [ ] 单元测试覆盖率 > 85%
   - [ ] 集成测试覆盖核心流程
   - [ ] 代码审查通过
   - [ ] 静态分析无严重问题

2. **性能验收**
   - [ ] 应用启动时间 < 3秒
   - [ ] 内存使用 < 150MB
   - [ ] 网络请求响应时间 < 2秒
   - [ ] 数据库操作性能良好

3. **安全验收**
   - [ ] 敏感数据加密存储
   - [ ] 网络通信安全
   - [ ] 认证机制安全
   - [ ] 权限控制有效

### 模块化验收
1. **配置驱动**
   - [ ] 所有模块可配置启用/禁用
   - [ ] 配置变更无需代码修改
   - [ ] 依赖关系验证正确
   - [ ] 配置热重载支持

2. **零侵入性**
   - [ ] NoOp 服务正常工作
   - [ ] 模块禁用不影响编译
   - [ ] 接口抽象层完整
   - [ ] 条件依赖注入正确

3. **性能优化**
   - [ ] 未启用模块不影响包大小
   - [ ] 启动时间不受禁用模块影响
   - [ ] 内存使用优化
   - [ ] 构建时间合理

---

## 风险和注意事项

### 技术风险
1. **模块耦合**
   - 风险：模块间出现隐式依赖
   - 缓解：严格接口抽象，定期依赖检查
   - 监控：自动化依赖分析工具

2. **性能影响**
   - 风险：条件注入影响运行时性能
   - 缓解：优化注入逻辑，使用编译时优化
   - 监控：性能基准测试

3. **状态管理复杂性**
   - 风险：跨模块状态同步问题
   - 缓解：明确状态边界，使用事件总线
   - 监控：状态变化日志

### 业务风险
1. **安全漏洞**
   - 风险：认证授权实现不当
   - 缓解：安全代码审查，渗透测试
   - 监控：安全扫描工具

2. **数据丢失**
   - 风险：数据同步冲突导致数据丢失
   - 缓解：完善冲突解决机制，数据备份
   - 监控：数据一致性检查

### 开发风险
1. **学习成本**
   - 风险：团队对复杂架构理解不足
   - 缓解：详细文档，代码示例，培训
   - 监控：代码质量指标

2. **开发效率**
   - 风险：过度抽象影响开发速度
   - 缓解：提供开发工具，模板代码
   - 监控：开发速度指标

---

## 下一阶段准备

### 为阶段三准备的基础
1. **UI 组件基础**：为用户界面模块提供组件库
2. **主题系统**：为多主题支持提供基础
3. **路由系统**：为复杂导航提供基础
4. **性能监控**：为性能优化提供数据支持

### 技术债务管理
1. **代码重构**：优化模块间接口设计
2. **性能优化**：持续优化关键路径性能
3. **文档更新**：保持 API 文档同步
4. **工具改进**：改进开发和调试工具

---

## 总结

阶段二的成功完成将为应用提供完整的核心业务功能支持。通过实现认证授权、网络通信、数据持久化、状态管理和错误处理等关键模块，应用将具备企业级应用的核心能力。

**关键成功因素**：
1. 严格遵循模块化架构原则
2. 确保所有模块经过充分测试
3. 保持模块间的松耦合
4. 建立完善的错误处理机制

**预期收益**：
1. 完整的业务功能支持
2. 高可用的系统架构
3. 灵活的模块配置能力
4. 强大的错误恢复能力