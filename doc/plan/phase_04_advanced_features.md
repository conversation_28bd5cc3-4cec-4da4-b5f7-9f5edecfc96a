# 阶段四：高级功能模块实现

## 阶段目标
基于前三个阶段的完整基础，实现高级功能模块，包括分析统计、推送通知、离线支持、安全增强、CI/CD 集成和企业级管理功能等。

## 完成标准
- ✅ 分析统计系统完全实现并正常工作
- ✅ 推送通知系统功能完善
- ✅ 离线支持机制健壮可靠
- ✅ 安全防护措施全面有效
- ✅ CI/CD 流水线配置完成
- ✅ 企业级管理功能正常运行
- ✅ 所有高级功能集成测试通过
- ✅ 生产环境部署验证通过

---

## 任务清单

### 1. 分析统计系统实现
**参照文档**: `/doc/code_examples/16_analytics/analytics_system.md`

#### 1.1 事件追踪系统
**参照文档**: `event_tracking.md`

**完成内容**:
```dart
// lib/features/analytics/domain/entities/analytics_event.dart
class AnalyticsEvent {
  final String name;
  final Map<String, dynamic> parameters;
  final DateTime timestamp;
  final String userId;
  final String sessionId;
  final Map<String, dynamic>? userProperties;
  
  const AnalyticsEvent({
    required this.name,
    required this.parameters,
    required this.timestamp,
    required this.userId,
    required this.sessionId,
    this.userProperties,
  });
}

// lib/features/analytics/domain/repositories/analytics_repository.dart
abstract class IAnalyticsRepository {
  Future<void> trackEvent(AnalyticsEvent event);
  Future<void> setUserProperties(Map<String, dynamic> properties);
  Future<void> setUserId(String userId);
  Future<List<AnalyticsEvent>> getEvents({
    DateTime? startDate,
    DateTime? endDate,
    String? eventName,
  });
  Future<void> flush();
}

// lib/features/analytics/data/repositories/analytics_repository_impl.dart
class AnalyticsRepositoryImpl implements IAnalyticsRepository {
  final IAnalyticsLocalDataSource localDataSource;
  final IAnalyticsRemoteDataSource remoteDataSource;
  final INetworkInfo networkInfo;
  
  @override
  Future<void> trackEvent(AnalyticsEvent event) async {
    // 本地存储事件
    await localDataSource.storeEvent(event);
    
    // 如果有网络连接，尝试立即发送
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.sendEvent(event);
        await localDataSource.markEventAsSent(event.timestamp);
      } catch (e) {
        // 发送失败，保留在本地队列中
        Logger.w('Failed to send analytics event: $e');
      }
    }
  }
  
  @override
  Future<void> flush() async {
    final pendingEvents = await localDataSource.getPendingEvents();
    
    if (pendingEvents.isEmpty) return;
    
    try {
      await remoteDataSource.sendBatchEvents(pendingEvents);
      await localDataSource.clearSentEvents();
    } catch (e) {
      Logger.e('Failed to flush analytics events: $e');
    }
  }
}

// lib/features/analytics/domain/usecases/track_event_usecase.dart
class TrackEventUseCase implements UseCaseBase<void, TrackEventParams> {
  final IAnalyticsRepository repository;
  
  @override
  Future<Either<Failure, void>> call(TrackEventParams params) async {
    try {
      final event = AnalyticsEvent(
        name: params.eventName,
        parameters: params.parameters,
        timestamp: DateTime.now(),
        userId: params.userId,
        sessionId: params.sessionId,
        userProperties: params.userProperties,
      );
      
      await repository.trackEvent(event);
      return const Right(null);
    } catch (e) {
      return Left(AnalyticsFailure(e.toString()));
    }
  }
}
```

**关键功能**:
- 自定义事件追踪
- 用户行为分析
- 页面访问统计
- 转化漏斗分析
- 实时数据同步
- 离线事件缓存

**预定义事件**:
```dart
// lib/features/analytics/domain/events/predefined_events.dart
class PredefinedEvents {
  // 应用生命周期事件
  static const String appStart = 'app_start';
  static const String appBackground = 'app_background';
  static const String appForeground = 'app_foreground';
  
  // 用户行为事件
  static const String userLogin = 'user_login';
  static const String userLogout = 'user_logout';
  static const String userRegister = 'user_register';
  
  // 页面访问事件
  static const String pageView = 'page_view';
  static const String pageExit = 'page_exit';
  
  // 功能使用事件
  static const String featureUsed = 'feature_used';
  static const String buttonClicked = 'button_clicked';
  static const String formSubmitted = 'form_submitted';
  
  // 错误事件
  static const String errorOccurred = 'error_occurred';
  static const String crashReported = 'crash_reported';
}
```

**验证方式**:
- 事件正确追踪和存储
- 离线事件缓存机制有效
- 批量上传功能正常
- 数据统计准确

#### 1.2 用户行为分析
**完成内容**:
```dart
// lib/features/analytics/domain/services/user_behavior_service.dart
abstract class IUserBehaviorService {
  Future<void> trackScreenView(String screenName, {Map<String, dynamic>? parameters});
  Future<void> trackUserAction(String action, {Map<String, dynamic>? parameters});
  Future<void> trackTiming(String category, String variable, Duration duration);
  Future<void> trackException(String description, bool fatal);
  Future<UserBehaviorReport> generateBehaviorReport(String userId, DateRange dateRange);
}

// lib/features/analytics/domain/services/user_behavior_service_impl.dart
class UserBehaviorService implements IUserBehaviorService {
  final IAnalyticsRepository analyticsRepository;
  final ISessionService sessionService;
  
  @override
  Future<void> trackScreenView(String screenName, {Map<String, dynamic>? parameters}) async {
    final event = AnalyticsEvent(
      name: PredefinedEvents.pageView,
      parameters: {
        'screen_name': screenName,
        'screen_class': screenName,
        ...?parameters,
      },
      timestamp: DateTime.now(),
      userId: await _getCurrentUserId(),
      sessionId: sessionService.currentSessionId,
    );
    
    await analyticsRepository.trackEvent(event);
  }
  
  @override
  Future<void> trackUserAction(String action, {Map<String, dynamic>? parameters}) async {
    final event = AnalyticsEvent(
      name: action,
      parameters: parameters ?? {},
      timestamp: DateTime.now(),
      userId: await _getCurrentUserId(),
      sessionId: sessionService.currentSessionId,
    );
    
    await analyticsRepository.trackEvent(event);
  }
}

// lib/features/analytics/presentation/widgets/analytics_wrapper.dart
class AnalyticsWrapper extends StatefulWidget {
  final Widget child;
  final String screenName;
  final Map<String, dynamic>? screenParameters;
  
  @override
  State<AnalyticsWrapper> createState() => _AnalyticsWrapperState();
}

class _AnalyticsWrapperState extends State<AnalyticsWrapper> {
  late final IUserBehaviorService _behaviorService;
  late final DateTime _screenEnterTime;
  
  @override
  void initState() {
    super.initState();
    _behaviorService = GetIt.instance<IUserBehaviorService>();
    _screenEnterTime = DateTime.now();
    
    // 追踪页面进入
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _behaviorService.trackScreenView(
        widget.screenName,
        parameters: widget.screenParameters,
      );
    });
  }
  
  @override
  void dispose() {
    // 追踪页面停留时间
    final duration = DateTime.now().difference(_screenEnterTime);
    _behaviorService.trackTiming(
      'screen_time',
      widget.screenName,
      duration,
    );
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
```

**模块化实现**:
```dart
// 条件注册分析服务
if (FeatureConfig.instance.isFeatureEnabled(Features.analytics)) {
  getIt.registerLazySingleton<IAnalyticsRepository>(() => AnalyticsRepositoryImpl());
  getIt.registerLazySingleton<IUserBehaviorService>(() => UserBehaviorService());
} else {
  getIt.registerLazySingleton<IAnalyticsRepository>(() => NoOpAnalyticsRepository());
  getIt.registerLazySingleton<IUserBehaviorService>(() => NoOpUserBehaviorService());
}
```

**验证方式**:
- 用户行为正确追踪
- 页面访问统计准确
- 停留时间计算正确
- 行为报告生成正常

---

### 2. 推送通知系统实现
**参照文档**: `/doc/code_examples/18_push_notifications/push_notification_system.md`

#### 2.1 推送通知基础服务
**完成内容**:
```dart
// lib/features/push_notifications/domain/entities/push_notification.dart
class PushNotification {
  final String id;
  final String title;
  final String body;
  final Map<String, dynamic>? data;
  final DateTime timestamp;
  final NotificationPriority priority;
  final String? imageUrl;
  final List<NotificationAction>? actions;
  
  const PushNotification({
    required this.id,
    required this.title,
    required this.body,
    this.data,
    required this.timestamp,
    this.priority = NotificationPriority.normal,
    this.imageUrl,
    this.actions,
  });
}

// lib/features/push_notifications/domain/repositories/push_notification_repository.dart
abstract class IPushNotificationRepository {
  Future<String?> getDeviceToken();
  Future<void> subscribeToTopic(String topic);
  Future<void> unsubscribeFromTopic(String topic);
  Future<void> updateNotificationSettings(NotificationSettings settings);
  Stream<PushNotification> get notificationStream;
  Future<void> markAsRead(String notificationId);
  Future<List<PushNotification>> getNotificationHistory();
}

// lib/features/push_notifications/data/repositories/push_notification_repository_impl.dart
class PushNotificationRepositoryImpl implements IPushNotificationRepository {
  final FirebaseMessaging _firebaseMessaging;
  final ILocalNotificationService _localNotificationService;
  final IDatabaseService _databaseService;
  final StreamController<PushNotification> _notificationController = StreamController.broadcast();
  
  @override
  Stream<PushNotification> get notificationStream => _notificationController.stream;
  
  @override
  Future<String?> getDeviceToken() async {
    try {
      final token = await _firebaseMessaging.getToken();
      Logger.i('FCM Token: $token');
      return token;
    } catch (e) {
      Logger.e('Failed to get FCM token: $e');
      return null;
    }
  }
  
  @override
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      Logger.i('Subscribed to topic: $topic');
    } catch (e) {
      Logger.e('Failed to subscribe to topic $topic: $e');
    }
  }
  
  Future<void> _setupMessageHandlers() async {
    // 处理前台消息
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      final notification = _mapRemoteMessageToNotification(message);
      _notificationController.add(notification);
      _showLocalNotification(notification);
    });
    
    // 处理后台消息点击
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      final notification = _mapRemoteMessageToNotification(message);
      _handleNotificationTap(notification);
    });
    
    // 处理应用终止状态下的消息点击
    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      final notification = _mapRemoteMessageToNotification(initialMessage);
      _handleNotificationTap(notification);
    }
  }
}

// lib/features/push_notifications/domain/services/local_notification_service.dart
abstract class ILocalNotificationService {
  Future<void> initialize();
  Future<void> showNotification(PushNotification notification);
  Future<void> scheduleNotification(PushNotification notification, DateTime scheduledTime);
  Future<void> cancelNotification(String notificationId);
  Future<void> cancelAllNotifications();
}

// lib/features/push_notifications/data/services/local_notification_service_impl.dart
class LocalNotificationService implements ILocalNotificationService {
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin;
  
  @override
  Future<void> initialize() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const initializationSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );
    
    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTap,
    );
  }
  
  @override
  Future<void> showNotification(PushNotification notification) async {
    final androidDetails = AndroidNotificationDetails(
      'default_channel',
      'Default Channel',
      channelDescription: 'Default notification channel',
      importance: _mapPriorityToImportance(notification.priority),
      priority: _mapPriorityToPriority(notification.priority),
      largeIcon: notification.imageUrl != null 
          ? NetworkImageAndroidBitmap(notification.imageUrl!) 
          : null,
    );
    
    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    
    final notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );
    
    await _flutterLocalNotificationsPlugin.show(
      notification.id.hashCode,
      notification.title,
      notification.body,
      notificationDetails,
      payload: jsonEncode(notification.data),
    );
  }
}
```

**关键功能**:
- Firebase Cloud Messaging 集成
- 本地通知支持
- 通知权限管理
- 主题订阅机制
- 通知历史记录
- 通知点击处理

**验证方式**:
- 推送通知正常接收
- 本地通知正确显示
- 通知点击跳转正确
- 权限请求正常

#### 2.2 通知管理和设置
**完成内容**:
```dart
// lib/features/push_notifications/domain/entities/notification_settings.dart
class NotificationSettings {
  final bool enabled;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final bool badgeEnabled;
  final Map<String, bool> topicSubscriptions;
  final NotificationTime quietHoursStart;
  final NotificationTime quietHoursEnd;
  
  const NotificationSettings({
    required this.enabled,
    required this.soundEnabled,
    required this.vibrationEnabled,
    required this.badgeEnabled,
    required this.topicSubscriptions,
    required this.quietHoursStart,
    required this.quietHoursEnd,
  });
}

// lib/features/push_notifications/presentation/pages/notification_settings_page.dart
class NotificationSettingsPage extends StatefulWidget {
  @override
  State<NotificationSettingsPage> createState() => _NotificationSettingsPageState();
}

class _NotificationSettingsPageState extends State<NotificationSettingsPage> {
  late final NotificationSettingsBloc _bloc;
  
  @override
  void initState() {
    super.initState();
    _bloc = GetIt.instance<NotificationSettingsBloc>();
    _bloc.add(const NotificationSettingsLoadRequested());
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context).notificationSettings),
      ),
      body: BlocBuilder<NotificationSettingsBloc, NotificationSettingsState>(
        bloc: _bloc,
        builder: (context, state) {
          if (state is NotificationSettingsLoaded) {
            return _buildSettingsForm(state.settings);
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }
  
  Widget _buildSettingsForm(NotificationSettings settings) {
    return ListView(
      padding: DesignTokens.spacingM,
      children: [
        // 总开关
        SwitchListTile(
          title: Text(AppLocalizations.of(context).enableNotifications),
          value: settings.enabled,
          onChanged: (value) {
            _bloc.add(NotificationSettingsUpdated(
              settings.copyWith(enabled: value),
            ));
          },
        ),
        
        // 声音设置
        SwitchListTile(
          title: Text(AppLocalizations.of(context).notificationSound),
          value: settings.soundEnabled,
          onChanged: settings.enabled ? (value) {
            _bloc.add(NotificationSettingsUpdated(
              settings.copyWith(soundEnabled: value),
            ));
          } : null,
        ),
        
        // 主题订阅
        ExpansionTile(
          title: Text(AppLocalizations.of(context).topicSubscriptions),
          children: settings.topicSubscriptions.entries.map((entry) {
            return SwitchListTile(
              title: Text(entry.key),
              value: entry.value,
              onChanged: (value) {
                final updatedSubscriptions = Map<String, bool>.from(settings.topicSubscriptions);
                updatedSubscriptions[entry.key] = value;
                _bloc.add(NotificationSettingsUpdated(
                  settings.copyWith(topicSubscriptions: updatedSubscriptions),
                ));
              },
            );
          }).toList(),
        ),
      ],
    );
  }
}
```

**模块化实现**:
```dart
// 条件注册推送通知服务
if (FeatureConfig.instance.isFeatureEnabled(Features.pushNotifications)) {
  getIt.registerLazySingleton<IPushNotificationRepository>(() => PushNotificationRepositoryImpl());
  getIt.registerLazySingleton<ILocalNotificationService>(() => LocalNotificationService());
} else {
  getIt.registerLazySingleton<IPushNotificationRepository>(() => NoOpPushNotificationRepository());
  getIt.registerLazySingleton<ILocalNotificationService>(() => NoOpLocalNotificationService());
}
```

**验证方式**:
- 通知设置保存正确
- 主题订阅功能正常
- 免打扰时间有效
- 权限管理正确

---

### 3. 离线支持系统实现
**参照文档**: `/doc/code_examples/19_offline_support/offline_system.md`

#### 3.1 离线数据管理
**完成内容**:
```dart
// lib/core/offline/offline_manager.dart
abstract class IOfflineManager {
  Future<void> enableOfflineMode();
  Future<void> disableOfflineMode();
  bool get isOfflineMode;
  Stream<bool> get offlineModeStream;
  Future<void> syncWhenOnline();
  Future<OfflineStatus> getOfflineStatus();
}

// lib/core/offline/offline_manager_impl.dart
class OfflineManager implements IOfflineManager {
  final INetworkInfo _networkInfo;
  final ISyncService _syncService;
  final IDatabaseService _databaseService;
  final StreamController<bool> _offlineModeController = StreamController.broadcast();
  
  bool _isOfflineMode = false;
  
  @override
  bool get isOfflineMode => _isOfflineMode;
  
  @override
  Stream<bool> get offlineModeStream => _offlineModeController.stream;
  
  @override
  Future<void> enableOfflineMode() async {
    _isOfflineMode = true;
    _offlineModeController.add(true);
    
    // 缓存关键数据
    await _cacheEssentialData();
    
    Logger.i('Offline mode enabled');
  }
  
  @override
  Future<void> syncWhenOnline() async {
    if (await _networkInfo.isConnected) {
      try {
        await _syncService.syncToServer();
        await _syncService.syncFromServer();
        
        if (_isOfflineMode) {
          await disableOfflineMode();
        }
      } catch (e) {
        Logger.e('Sync failed: $e');
      }
    }
  }
  
  Future<void> _cacheEssentialData() async {
    // 缓存用户数据
    // 缓存应用配置
    // 缓存关键业务数据
  }
}

// lib/core/offline/offline_storage.dart
class OfflineStorage {
  final IDatabaseService _databaseService;
  
  static const String _offlineDataKey = 'offline_data';
  static const String _pendingActionsKey = 'pending_actions';
  
  Future<void> storeOfflineData(String key, Map<String, dynamic> data) async {
    final offlineData = await _getOfflineData();
    offlineData[key] = data;
    await _databaseService.set(_offlineDataKey, offlineData);
  }
  
  Future<Map<String, dynamic>?> getOfflineData(String key) async {
    final offlineData = await _getOfflineData();
    return offlineData[key];
  }
  
  Future<void> storePendingAction(OfflineAction action) async {
    final pendingActions = await _getPendingActions();
    pendingActions.add(action.toJson());
    await _databaseService.set(_pendingActionsKey, pendingActions);
  }
  
  Future<List<OfflineAction>> getPendingActions() async {
    final actionsJson = await _databaseService.get<List>(_pendingActionsKey) ?? [];
    return actionsJson.map((json) => OfflineAction.fromJson(json)).toList();
  }
}

// lib/core/offline/offline_action.dart
class OfflineAction {
  final String id;
  final String type;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final int retryCount;
  
  const OfflineAction({
    required this.id,
    required this.type,
    required this.data,
    required this.timestamp,
    this.retryCount = 0,
  });
  
  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type,
    'data': data,
    'timestamp': timestamp.toIso8601String(),
    'retryCount': retryCount,
  };
  
  factory OfflineAction.fromJson(Map<String, dynamic> json) => OfflineAction(
    id: json['id'],
    type: json['type'],
    data: json['data'],
    timestamp: DateTime.parse(json['timestamp']),
    retryCount: json['retryCount'] ?? 0,
  );
}
```

**关键功能**:
- 自动离线检测
- 离线数据缓存
- 离线操作队列
- 数据同步机制
- 冲突解决策略

**验证方式**:
- 离线模式切换正常
- 离线数据访问正确
- 同步机制有效
- 冲突解决正确

#### 3.2 离线 UI 组件
**完成内容**:
```dart
// lib/shared/widgets/offline/offline_banner.dart
class OfflineBanner extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OfflineBloc, OfflineState>(
      builder: (context, state) {
        if (state is OfflineEnabled) {
          return Container(
            width: double.infinity,
            padding: DesignTokens.spacingS,
            color: Theme.of(context).colorScheme.error,
            child: Row(
              children: [
                Icon(
                  Icons.cloud_off,
                  color: Theme.of(context).colorScheme.onError,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    AppLocalizations.of(context).offlineMode,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onError,
                    ),
                  ),
                ),
                if (state.hasPendingSync)
                  Icon(
                    Icons.sync,
                    color: Theme.of(context).colorScheme.onError,
                    size: 16,
                  ),
              ],
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }
}

// lib/shared/widgets/offline/offline_aware_widget.dart
class OfflineAwareWidget extends StatelessWidget {
  final Widget onlineChild;
  final Widget offlineChild;
  final bool showOfflineBanner;
  
  const OfflineAwareWidget({
    Key? key,
    required this.onlineChild,
    required this.offlineChild,
    this.showOfflineBanner = true,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OfflineBloc, OfflineState>(
      builder: (context, state) {
        return Column(
          children: [
            if (showOfflineBanner) OfflineBanner(),
            Expanded(
              child: state is OfflineEnabled ? offlineChild : onlineChild,
            ),
          ],
        );
      },
    );
  }
}
```

**验证方式**:
- 离线状态显示正确
- UI 组件响应离线状态
- 离线提示用户友好
- 同步状态显示准确

---

### 4. 安全增强系统实现
**参照文档**: `/doc/code_examples/20_security/security_implementation.md`

#### 4.1 数据加密和安全存储
**完成内容**:
```dart
// lib/core/security/encryption_service.dart
abstract class IEncryptionService {
  Future<String> encrypt(String data);
  Future<String> decrypt(String encryptedData);
  Future<String> hash(String data);
  Future<bool> verifyHash(String data, String hash);
  Future<String> generateSecureToken();
}

// lib/core/security/encryption_service_impl.dart
class EncryptionService implements IEncryptionService {
  final encrypt.Encrypter _encrypter;
  final encrypt.IV _iv;
  
  EncryptionService() : 
    _encrypter = encrypt.Encrypter(encrypt.AES(_generateKey())),
    _iv = encrypt.IV.fromSecureRandom(16);
  
  @override
  Future<String> encrypt(String data) async {
    final encrypted = _encrypter.encrypt(data, iv: _iv);
    return encrypted.base64;
  }
  
  @override
  Future<String> decrypt(String encryptedData) async {
    final encrypted = encrypt.Encrypted.fromBase64(encryptedData);
    return _encrypter.decrypt(encrypted, iv: _iv);
  }
  
  @override
  Future<String> hash(String data) async {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
  
  @override
  Future<bool> verifyHash(String data, String hash) async {
    final computedHash = await this.hash(data);
    return computedHash == hash;
  }
  
  @override
  Future<String> generateSecureToken() async {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Encode(bytes);
  }
  
  static encrypt.Key _generateKey() {
    // 在实际应用中，密钥应该从安全的地方获取
    // 例如：环境变量、密钥管理服务等
    return encrypt.Key.fromSecureRandom(32);
  }
}

// lib/core/security/secure_storage_service.dart
class SecureStorageService {
  final FlutterSecureStorage _secureStorage;
  final IEncryptionService _encryptionService;
  
  SecureStorageService(this._encryptionService) : 
    _secureStorage = const FlutterSecureStorage(
      aOptions: AndroidOptions(
        encryptedSharedPreferences: true,
      ),
      iOptions: IOSOptions(
        accessibility: IOSAccessibility.first_unlock_this_device,
      ),
    );
  
  Future<void> storeSecurely(String key, String value) async {
    final encryptedValue = await _encryptionService.encrypt(value);
    await _secureStorage.write(key: key, value: encryptedValue);
  }
  
  Future<String?> getSecurely(String key) async {
    final encryptedValue = await _secureStorage.read(key: key);
    if (encryptedValue == null) return null;
    
    try {
      return await _encryptionService.decrypt(encryptedValue);
    } catch (e) {
      Logger.e('Failed to decrypt value for key $key: $e');
      return null;
    }
  }
  
  Future<void> deleteSecurely(String key) async {
    await _secureStorage.delete(key: key);
  }
}
```

**关键功能**:
- AES 数据加密
- SHA-256 哈希验证
- 安全令牌生成
- 密钥管理
- 安全存储服务

**验证方式**:
- 数据加密解密正确
- 哈希验证有效
- 安全存储功能正常
- 密钥管理安全

#### 4.2 应用安全防护
**完成内容**:
```dart
// lib/core/security/app_security_service.dart
abstract class IAppSecurityService {
  Future<bool> isDeviceRooted();
  Future<bool> isDebugMode();
  Future<bool> isEmulator();
  Future<void> enableAppProtection();
  Future<void> disableAppProtection();
  Future<SecurityReport> generateSecurityReport();
}

// lib/core/security/app_security_service_impl.dart
class AppSecurityService implements IAppSecurityService {
  @override
  Future<bool> isDeviceRooted() async {
    try {
      // 检查常见的 root 指示器
      final rootPaths = [
        '/system/app/Superuser.apk',
        '/sbin/su',
        '/system/bin/su',
        '/system/xbin/su',
        '/data/local/xbin/su',
        '/data/local/bin/su',
        '/system/sd/xbin/su',
        '/system/bin/failsafe/su',
        '/data/local/su',
      ];
      
      for (final path in rootPaths) {
        if (await File(path).exists()) {
          return true;
        }
      }
      
      return false;
    } catch (e) {
      Logger.e('Error checking root status: $e');
      return false;
    }
  }
  
  @override
  Future<bool> isDebugMode() async {
    return kDebugMode;
  }
  
  @override
  Future<bool> isEmulator() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return androidInfo.isPhysicalDevice == false;
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return iosInfo.isPhysicalDevice == false;
      }
      return false;
    } catch (e) {
      Logger.e('Error checking emulator status: $e');
      return false;
    }
  }
  
  @override
  Future<void> enableAppProtection() async {
    // 启用屏幕截图保护
    await _enableScreenshotProtection();
    
    // 启用应用后台保护
    await _enableBackgroundProtection();
    
    // 启用调试保护
    await _enableDebugProtection();
  }
  
  Future<void> _enableScreenshotProtection() async {
    if (Platform.isAndroid) {
      // Android 平台的屏幕截图保护
      // 需要在 MainActivity 中实现
    }
  }
  
  Future<void> _enableBackgroundProtection() async {
    // 应用进入后台时的保护措施
    // 例如：模糊屏幕内容
  }
  
  Future<void> _enableDebugProtection() async {
    // 调试保护措施
    if (await isDebugMode()) {
      Logger.w('Debug mode detected - security features may be limited');
    }
  }
}

// lib/core/security/security_interceptor.dart
class SecurityInterceptor extends Interceptor {
  final IAppSecurityService _securityService;
  
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // 添加安全头
    options.headers['X-App-Version'] = await _getAppVersion();
    options.headers['X-Device-ID'] = await _getDeviceId();
    options.headers['X-Request-ID'] = _generateRequestId();
    
    // 检查安全状态
    if (await _securityService.isDeviceRooted()) {
      Logger.w('Device is rooted - request may be blocked');
      // 可以选择阻止请求或添加额外的安全措施
    }
    
    handler.next(options);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // 记录安全相关的错误
    if (err.response?.statusCode == 401 || err.response?.statusCode == 403) {
      Logger.w('Security error: ${err.response?.statusCode}');
    }
    
    handler.next(err);
  }
}
```

**验证方式**:
- 设备安全检查正确
- 应用保护功能有效
- 安全拦截器正常工作
- 安全报告生成准确

---

### 5. CI/CD 集成配置
**参照文档**: `/doc/code_examples/21_cicd/cicd_configuration.md`

#### 5.1 GitHub Actions 配置
**完成内容**:
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
    
    - name: Get dependencies
      run: flutter pub get
    
    - name: Run code generation
      run: |
        dart run build_runner build --delete-conflicting-outputs
        dart run tool/feature_generator.dart generate
    
    - name: Analyze code
      run: flutter analyze
    
    - name: Run tests
      run: flutter test --coverage
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: coverage/lcov.info
    
    - name: Validate configuration
      run: dart run tool/config_validator.dart validate

  build-android:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
    
    - name: Setup Java
      uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: '11'
    
    - name: Get dependencies
      run: flutter pub get
    
    - name: Run code generation
      run: dart run build_runner build --delete-conflicting-outputs
    
    - name: Build APK
      run: flutter build apk --release
    
    - name: Build App Bundle
      run: flutter build appbundle --release
    
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: android-builds
        path: |
          build/app/outputs/flutter-apk/app-release.apk
          build/app/outputs/bundle/release/app-release.aab

  build-ios:
    needs: test
    runs-on: macos-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
    
    - name: Get dependencies
      run: flutter pub get
    
    - name: Run code generation
      run: dart run build_runner build --delete-conflicting-outputs
    
    - name: Build iOS
      run: |
        flutter build ios --release --no-codesign
        cd ios
        xcodebuild -workspace Runner.xcworkspace -scheme Runner -configuration Release -destination generic/platform=iOS -archivePath build/Runner.xcarchive archive
    
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: ios-builds
        path: ios/build/Runner.xcarchive

  deploy:
    needs: [build-android, build-ios]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment"
        # 部署逻辑
```

**关键功能**:
- 自动化测试执行
- 代码质量检查
- 多平台构建
- 自动化部署
- 覆盖率报告
- 配置验证

**验证方式**:
- CI/CD 流水线正常运行
- 测试自动执行
- 构建产物正确生成
- 部署流程正常

#### 5.2 质量门禁配置
**完成内容**:
```yaml
# .github/workflows/quality-gate.yml
name: Quality Gate

on:
  pull_request:
    branches: [ main, develop ]

jobs:
  quality-check:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
    
    - name: Get dependencies
      run: flutter pub get
    
    - name: Run code generation
      run: dart run build_runner build --delete-conflicting-outputs
    
    - name: Analyze code
      run: |
        flutter analyze > analysis_output.txt
        if grep -q "error" analysis_output.txt; then
          echo "Code analysis failed"
          cat analysis_output.txt
          exit 1
        fi
    
    - name: Run tests with coverage
      run: |
        flutter test --coverage
        # 检查覆盖率是否达到要求（80%）
        dart run tool/coverage_checker.dart --threshold=80
    
    - name: Check code formatting
      run: |
        dart format --set-exit-if-changed .
    
    - name: Validate modular architecture
      run: |
        dart run tool/config_validator.dart validate
        dart run tool/architecture_validator.dart
    
    - name: Security scan
      run: |
        # 运行安全扫描工具
        dart run tool/security_scanner.dart
    
    - name: Performance benchmark
      run: |
        # 运行性能基准测试
        flutter test integration_test/performance_test.dart
```

**验证方式**:
- 质量门禁正确拦截问题
- 代码质量标准执行
- 安全扫描有效
- 性能基准测试通过

---

### 6. 企业级管理功能实现

#### 6.1 系统监控和日志
**完成内容**:
```dart
// lib/core/monitoring/system_monitor.dart
abstract class ISystemMonitor {
  Future<void> startMonitoring();
  Future<void> stopMonitoring();
  Future<SystemHealth> getSystemHealth();
  Stream<SystemMetrics> get metricsStream;
  Future<void> reportIssue(SystemIssue issue);
}

// lib/core/monitoring/system_monitor_impl.dart
class SystemMonitor implements ISystemMonitor {
  final Timer? _monitoringTimer;
  final StreamController<SystemMetrics> _metricsController = StreamController.broadcast();
  
  @override
  Stream<SystemMetrics> get metricsStream => _metricsController.stream;
  
  @override
  Future<void> startMonitoring() async {
    _monitoringTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _collectMetrics(),
    );
    
    Logger.i('System monitoring started');
  }
  
  Future<void> _collectMetrics() async {
    final metrics = SystemMetrics(
      timestamp: DateTime.now(),
      memoryUsage: await _getMemoryUsage(),
      cpuUsage: await _getCpuUsage(),
      networkStatus: await _getNetworkStatus(),
      batteryLevel: await _getBatteryLevel(),
      storageUsage: await _getStorageUsage(),
    );
    
    _metricsController.add(metrics);
    
    // 检查是否有异常情况
    await _checkForAnomalies(metrics);
  }
  
  Future<void> _checkForAnomalies(SystemMetrics metrics) async {
    // 内存使用过高
    if (metrics.memoryUsage > 200 * 1024 * 1024) { // 200MB
      await reportIssue(SystemIssue(
        type: IssueType.highMemoryUsage,
        severity: IssueSeverity.warning,
        message: 'High memory usage detected: ${metrics.memoryUsage / 1024 / 1024}MB',
        timestamp: DateTime.now(),
      ));
    }
    
    // 电池电量过低
    if (metrics.batteryLevel < 0.15) { // 15%
      await reportIssue(SystemIssue(
        type: IssueType.lowBattery,
        severity: IssueSeverity.info,
        message: 'Low battery level: ${(metrics.batteryLevel * 100).toInt()}%',
        timestamp: DateTime.now(),
      ));
    }
  }
}

// lib/core/logging/advanced_logger.dart
class AdvancedLogger {
  static final Logger _logger = Logger();
  static final List<LogEntry> _logBuffer = [];
  static const int _maxBufferSize = 1000;
  
  static void logWithContext({
    required LogLevel level,
    required String message,
    String? tag,
    Map<String, dynamic>? context,
    Object? error,
    StackTrace? stackTrace,
  }) {
    final logEntry = LogEntry(
      level: level,
      message: message,
      tag: tag,
      context: context,
      error: error,
      stackTrace: stackTrace,
      timestamp: DateTime.now(),
    );
    
    _addToBuffer(logEntry);
    _writeToConsole(logEntry);
    _sendToRemoteLogging(logEntry);
  }
  
  static void _addToBuffer(LogEntry entry) {
    _logBuffer.add(entry);
    
    if (_logBuffer.length > _maxBufferSize) {
      _logBuffer.removeAt(0);
    }
  }
  
  static Future<void> _sendToRemoteLogging(LogEntry entry) async {
    // 发送到远程日志服务
    if (entry.level.index >= LogLevel.warning.index) {
      // 只发送警告级别以上的日志
      try {
        // 实现远程日志发送逻辑
      } catch (e) {
        // 远程日志发送失败，不影响应用运行
      }
    }
  }
  
  static List<LogEntry> getRecentLogs({int? limit}) {
    final logs = List<LogEntry>.from(_logBuffer);
    if (limit != null && logs.length > limit) {
      return logs.sublist(logs.length - limit);
    }
    return logs;
  }
}
```

**验证方式**:
- 系统监控数据准确
- 日志记录完整
- 异常检测有效
- 远程日志发送正常

#### 6.2 配置管理后台
**完成内容**:
```dart
// lib/features/admin/presentation/pages/admin_dashboard_page.dart
class AdminDashboardPage extends StatefulWidget {
  @override
  State<AdminDashboardPage> createState() => _AdminDashboardPageState();
}

class _AdminDashboardPageState extends State<AdminDashboardPage> {
  late final AdminDashboardBloc _bloc;
  
  @override
  void initState() {
    super.initState();
    _bloc = GetIt.instance<AdminDashboardBloc>();
    _bloc.add(const AdminDashboardLoadRequested());
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context).adminDashboard),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _bloc.add(const AdminDashboardRefreshRequested()),
          ),
        ],
      ),
      body: BlocBuilder<AdminDashboardBloc, AdminDashboardState>(
        bloc: _bloc,
        builder: (context, state) {
          if (state is AdminDashboardLoaded) {
            return _buildDashboard(state.data);
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }
  
  Widget _buildDashboard(AdminDashboardData data) {
    return ResponsiveBuilder(
      mobile: (context, constraints) => _buildMobileDashboard(data),
      tablet: (context, constraints) => _buildTabletDashboard(data),
      desktop: (context, constraints) => _buildDesktopDashboard(data),
    );
  }
  
  Widget _buildDesktopDashboard(AdminDashboardData data) {
    return Row(
      children: [
        // 侧边栏
        SizedBox(
          width: 250,
          child: _buildSidebar(),
        ),
        // 主内容区
        Expanded(
          child: Padding(
            padding: DesignTokens.spacingM,
            child: Column(
              children: [
                // 统计卡片
                _buildStatsCards(data.stats),
                const SizedBox(height: 24),
                // 图表和详细信息
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: _buildSystemMetricsChart(data.metrics),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildRecentActivity(data.recentActivity),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildStatsCards(AdminStats stats) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: 'Active Users',
            value: stats.activeUsers.toString(),
            icon: Icons.people,
            color: Colors.blue,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            title: 'System Health',
            value: '${(stats.systemHealth * 100).toInt()}%',
            icon: Icons.health_and_safety,
            color: stats.systemHealth > 0.8 ? Colors.green : Colors.orange,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            title: 'Error Rate',
            value: '${(stats.errorRate * 100).toStringAsFixed(1)}%',
            icon: Icons.error,
            color: stats.errorRate < 0.01 ? Colors.green : Colors.red,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            title: 'Features Enabled',
            value: '${stats.enabledFeatures}/${stats.totalFeatures}',
            icon: Icons.toggle_on,
            color: Colors.purple,
          ),
        ),
      ],
    );
  }
}
```

**验证方式**:
- 管理后台功能正常
- 数据展示准确
- 响应式设计正确
- 权限控制有效

---

## 阶段验收标准

### 功能验收
1. **分析统计系统**
   - [ ] 事件追踪功能正常
   - [ ] 用户行为分析准确
   - [ ] 离线事件缓存有效
   - [ ] 数据报告生成正确

2. **推送通知系统**
   - [ ] 推送通知正常接收
   - [ ] 本地通知正确显示
   - [ ] 通知设置功能完善
   - [ ] 主题订阅机制正常

3. **离线支持系统**
   - [ ] 离线检测准确
   - [ ] 离线数据缓存有效
   - [ ] 数据同步机制正常
   - [ ] 冲突解决正确

4. **安全系统**
   - [ ] 数据加密功能正常
   - [ ] 安全存储有效
   - [ ] 应用保护机制正常
   - [ ] 安全检查准确

5. **CI/CD 系统**
   - [ ] 自动化测试正常
   - [ ] 构建流程正确
   - [ ] 质量门禁有效
   - [ ] 部署流程正常

6. **企业管理功能**
   - [ ] 系统监控正常
   - [ ] 日志记录完整
   - [ ] 管理后台功能正常
   - [ ] 配置管理有效

### 质量验收
1. **性能指标**
   - [ ] 应用启动时间 < 3秒
   - [ ] 内存使用 < 200MB
   - [ ] 电池消耗合理
   - [ ] 网络使用优化

2. **安全指标**
   - [ ] 数据传输加密
   - [ ] 本地数据保护
   - [ ] 安全漏洞扫描通过
   - [ ] 权限控制有效

3. **可靠性指标**
   - [ ] 崩溃率 < 0.1%
   - [ ] 错误恢复机制有效
   - [ ] 离线功能稳定
   - [ ] 数据一致性保证

### 模块化验收
1. **高级功能模块化**
   - [ ] 所有高级功能可独立启用/禁用
   - [ ] 模块禁用不影响核心功能
   - [ ] NoOp 实现正常工作
   - [ ] 模块依赖关系正确

2. **企业级特性**
   - [ ] 多租户支持
   - [ ] 配置热更新
   - [ ] 监控告警机制
   - [ ] 审计日志完整

---

## 风险和注意事项

### 技术风险
1. **性能影响**
   - 风险：高级功能影响应用性能
   - 缓解：性能监控，优化关键路径
   - 监控：实时性能指标

2. **安全风险**
   - 风险：安全措施不当导致漏洞
   - 缓解：安全审计，渗透测试
   - 监控：安全扫描工具

3. **复杂性风险**
   - 风险：系统复杂度过高，难以维护
   - 缓解：模块化设计，文档完善
   - 监控：代码复杂度指标

4. **集成风险**
   - 风险：第三方服务集成失败
   - 缓解：降级方案，容错处理
   - 监控：服务可用性监控

### 开发注意事项
1. **模块化原则**
   - 严格遵循模块化架构
   - 确保功能可独立启用/禁用
   - 实现完整的 NoOp 替代方案

2. **性能优化**
   - 监控内存使用情况
   - 优化网络请求
   - 实现懒加载机制

3. **安全最佳实践**
   - 敏感数据加密存储
   - 网络传输加密
   - 定期安全审计

4. **用户体验**
   - 渐进式功能启用
   - 友好的错误提示
   - 离线状态处理

---

## 下一阶段准备

### 阶段五预备工作
1. **生产环境准备**
   - 生产环境配置验证
   - 部署脚本准备
   - 监控系统配置

2. **文档完善**
   - 用户使用手册
   - 运维部署指南
   - API 文档更新

3. **测试准备**
   - 端到端测试用例
   - 性能压力测试
   - 安全渗透测试

### 技术债务清理
1. **代码优化**
   - 重构复杂代码
   - 移除废弃代码
   - 优化性能瓶颈

2. **依赖管理**
   - 更新依赖版本
   - 移除未使用依赖
   - 安全漏洞修复

---

## 总结

阶段四完成后，应用将具备完整的企业级功能，包括：
- 完善的分析统计系统
- 可靠的推送通知机制
- 健壮的离线支持
- 全面的安全防护
- 自动化的 CI/CD 流程
- 专业的企业管理功能

这些高级功能为应用提供了生产环境所需的所有企业级特性，确保应用能够满足大规模商业部署的需求。