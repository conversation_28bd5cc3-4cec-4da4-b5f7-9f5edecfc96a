# 阶段三：用户界面和用户体验模块实现

## 阶段目标
基于前两个阶段的基础架构和核心业务模块，实现完整的用户界面系统，包括设计系统、主题管理、国际化、路由导航、性能监控和用户体验优化等模块。

## 完成标准
- ✅ 设计系统和组件库完全实现
- ✅ 多主题支持系统正常工作
- ✅ 国际化系统完整实现
- ✅ 路由导航系统功能完善
- ✅ 性能监控系统正常运行
- ✅ 用户体验优化措施有效
- ✅ 所有 UI/UX 模块集成测试通过
- ✅ 响应式设计适配完成

---

## 任务清单

### 1. 设计系统和组件库实现
**参照文档**: `/doc/code_examples/11_ui_components/design_system.md`

#### 1.1 设计令牌系统
**参照文档**: `design_tokens.md`

**完成内容**:
```dart
// lib/core/design/design_tokens.dart
class DesignTokens {
  // 颜色系统
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFF1976D2),
    onPrimary: Color(0xFFFFFFFF),
    secondary: Color(0xFF03DAC6),
    onSecondary: Color(0xFF000000),
    error: Color(0xFFB00020),
    onError: Color(0xFFFFFFFF),
    background: Color(0xFFFAFAFA),
    onBackground: Color(0xFF000000),
    surface: Color(0xFFFFFFFF),
    onSurface: Color(0xFF000000),
  );
  
  // 字体系统
  static const TextTheme textTheme = TextTheme(
    displayLarge: TextStyle(
      fontSize: 57,
      fontWeight: FontWeight.w400,
      letterSpacing: -0.25,
    ),
    headlineLarge: TextStyle(
      fontSize: 32,
      fontWeight: FontWeight.w400,
    ),
    // 其他文本样式...
  );
  
  // 间距系统
  static const EdgeInsets spacingXS = EdgeInsets.all(4);
  static const EdgeInsets spacingS = EdgeInsets.all(8);
  static const EdgeInsets spacingM = EdgeInsets.all(16);
  static const EdgeInsets spacingL = EdgeInsets.all(24);
  static const EdgeInsets spacingXL = EdgeInsets.all(32);
  
  // 圆角系统
  static const BorderRadius radiusS = BorderRadius.all(Radius.circular(4));
  static const BorderRadius radiusM = BorderRadius.all(Radius.circular(8));
  static const BorderRadius radiusL = BorderRadius.all(Radius.circular(16));
  
  // 阴影系统
  static const List<BoxShadow> shadowS = [
    BoxShadow(
      color: Color(0x1A000000),
      blurRadius: 2,
      offset: Offset(0, 1),
    ),
  ];
}

// lib/core/design/design_system.dart
class DesignSystem {
  static ThemeData get lightTheme => ThemeData(
    useMaterial3: true,
    colorScheme: DesignTokens.lightColorScheme,
    textTheme: DesignTokens.textTheme,
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        shape: RoundedRectangleBorder(
          borderRadius: DesignTokens.radiusM,
        ),
      ),
    ),
  );
}
```

**关键功能**:
- 统一的颜色系统
- 标准化的字体规范
- 一致的间距系统
- 标准化的圆角和阴影
- 响应式设计支持

**验证方式**:
- 设计令牌应用正确
- 视觉一致性检查通过
- 响应式适配正常
- 设计规范文档完整

#### 1.2 基础组件库
**参照文档**: `component_library.md`

**完成内容**:
```dart
// lib/shared/widgets/buttons/app_button.dart
class AppButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final AppButtonType type;
  final AppButtonSize size;
  final bool isLoading;
  final Widget? icon;
  
  const AppButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.type = AppButtonType.primary,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.icon,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: _getButtonStyle(context),
      child: isLoading 
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (icon != null) ...[icon!, const SizedBox(width: 8)],
                Text(text),
              ],
            ),
    );
  }
}

// lib/shared/widgets/inputs/app_text_field.dart
class AppTextField extends StatelessWidget {
  final String? label;
  final String? hint;
  final String? errorText;
  final TextEditingController? controller;
  final bool obscureText;
  final TextInputType keyboardType;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null) ..[
          Text(
            label!,
            style: Theme.of(context).textTheme.labelMedium,
          ),
          const SizedBox(height: 8),
        ],
        TextFormField(
          controller: controller,
          obscureText: obscureText,
          keyboardType: keyboardType,
          validator: validator,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hint,
            errorText: errorText,
            border: OutlineInputBorder(
              borderRadius: DesignTokens.radiusM,
            ),
          ),
        ),
      ],
    );
  }
}

// lib/shared/widgets/cards/app_card.dart
class AppCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final VoidCallback? onTap;
  final bool elevated;
  
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: elevated ? 2 : 0,
      shape: RoundedRectangleBorder(
        borderRadius: DesignTokens.radiusM,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: DesignTokens.radiusM,
        child: Padding(
          padding: padding ?? DesignTokens.spacingM,
          child: child,
        ),
      ),
    );
  }
}
```

**组件清单**:
- 按钮组件（主要、次要、文本、图标）
- 输入组件（文本框、下拉框、开关、滑块）
- 卡片组件（基础卡片、信息卡片、操作卡片）
- 导航组件（标签栏、抽屉、面包屑）
- 反馈组件（对话框、提示条、加载指示器）
- 数据展示组件（列表、表格、图表）

**验证方式**:
- 所有组件功能正常
- 组件样式一致
- 组件可访问性良好
- 组件文档完整

#### 1.3 响应式布局系统
**完成内容**:
```dart
// lib/core/responsive/breakpoints.dart
class Breakpoints {
  static const double mobile = 600;
  static const double tablet = 1024;
  static const double desktop = 1440;
  
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobile;
  }
  
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobile && width < desktop;
  }
  
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktop;
  }
}

// lib/shared/widgets/responsive/responsive_builder.dart
class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, BoxConstraints constraints) mobile;
  final Widget Function(BuildContext context, BoxConstraints constraints)? tablet;
  final Widget Function(BuildContext context, BoxConstraints constraints)? desktop;
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= Breakpoints.desktop && desktop != null) {
          return desktop!(context, constraints);
        } else if (constraints.maxWidth >= Breakpoints.mobile && tablet != null) {
          return tablet!(context, constraints);
        } else {
          return mobile(context, constraints);
        }
      },
    );
  }
}
```

**验证方式**:
- 不同屏幕尺寸适配正确
- 布局切换流畅
- 内容可读性良好
- 交互体验一致

---

### 2. 多主题支持系统实现
**参照文档**: `/doc/code_examples/12_theming/theme_system.md`

#### 2.1 主题管理系统
**完成内容**:
```dart
// lib/core/theme/theme_service.dart
abstract class IThemeService {
  Future<ThemeMode> getThemeMode();
  Future<void> setThemeMode(ThemeMode mode);
  Future<String> getCurrentTheme();
  Future<void> setCurrentTheme(String themeId);
  Stream<ThemeData> get themeStream;
}

// lib/core/theme/theme_service_impl.dart
class ThemeService implements IThemeService {
  final IDatabaseService _databaseService;
  final StreamController<ThemeData> _themeController = StreamController.broadcast();
  
  static const String _themeModeKey = 'theme_mode';
  static const String _currentThemeKey = 'current_theme';
  
  @override
  Stream<ThemeData> get themeStream => _themeController.stream;
  
  @override
  Future<void> setThemeMode(ThemeMode mode) async {
    await _databaseService.set(_themeModeKey, mode.index);
    _notifyThemeChange();
  }
  
  void _notifyThemeChange() {
    final currentTheme = _buildCurrentTheme();
    _themeController.add(currentTheme);
  }
}

// lib/core/theme/theme_bloc.dart
class ThemeBloc extends Bloc<ThemeEvent, ThemeState> {
  final IThemeService themeService;
  
  ThemeBloc({required this.themeService}) : super(ThemeInitial()) {
    on<ThemeLoadRequested>(_onThemeLoadRequested);
    on<ThemeModeChanged>(_onThemeModeChanged);
    on<ThemeChanged>(_onThemeChanged);
  }
}
```

**关键功能**:
- 明暗主题切换
- 自定义主题支持
- 主题持久化存储
- 系统主题跟随
- 主题预览功能

**模块化实现**:
```dart
// 条件注册主题服务
if (FeatureConfig.instance.isFeatureEnabled(Features.theming)) {
  getIt.registerLazySingleton<IThemeService>(() => ThemeService());
} else {
  getIt.registerLazySingleton<IThemeService>(() => NoOpThemeService());
}
```

**验证方式**:
- 主题切换正常
- 主题持久化有效
- 自定义主题正确应用
- 系统主题跟随正常

#### 2.2 动态主题系统
**完成内容**:
```dart
// lib/core/theme/dynamic_theme.dart
class DynamicTheme {
  static ThemeData generateTheme({
    required Color primaryColor,
    required Brightness brightness,
    String? fontFamily,
  }) {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: primaryColor,
      brightness: brightness,
    );
    
    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      fontFamily: fontFamily,
      // 其他主题配置...
    );
  }
}

// lib/core/theme/theme_customizer.dart
class ThemeCustomizer extends StatefulWidget {
  @override
  State<ThemeCustomizer> createState() => _ThemeCustomizerState();
}

class _ThemeCustomizerState extends State<ThemeCustomizer> {
  Color _primaryColor = Colors.blue;
  String _fontFamily = 'Roboto';
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 颜色选择器
        ColorPicker(
          selectedColor: _primaryColor,
          onColorChanged: (color) {
            setState(() {
              _primaryColor = color;
            });
          },
        ),
        // 字体选择器
        FontFamilySelector(
          selectedFont: _fontFamily,
          onFontChanged: (font) {
            setState(() {
              _fontFamily = font;
            });
          },
        ),
        // 预览区域
        ThemePreview(
          theme: DynamicTheme.generateTheme(
            primaryColor: _primaryColor,
            brightness: Theme.of(context).brightness,
            fontFamily: _fontFamily,
          ),
        ),
      ],
    );
  }
}
```

**验证方式**:
- 动态主题生成正确
- 主题预览实时更新
- 自定义主题保存正常
- 主题导入导出功能正常

---

### 3. 国际化系统实现
**参照文档**: `/doc/code_examples/13_internationalization/i18n_system.md`

#### 3.1 多语言支持基础
**完成内容**:
```dart
// lib/core/i18n/app_localizations.dart
abstract class AppLocalizations {
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }
  
  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();
  
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = [
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
  ];
  
  static const List<Locale> supportedLocales = [
    Locale('en', 'US'),
    Locale('zh', 'CN'),
    Locale('ja', 'JP'),
    Locale('ko', 'KR'),
  ];
  
  // 通用文本
  String get appName;
  String get ok;
  String get cancel;
  String get save;
  String get delete;
  String get edit;
  String get loading;
  String get error;
  String get success;
  
  // 认证相关
  String get login;
  String get logout;
  String get register;
  String get email;
  String get password;
  String get confirmPassword;
  
  // 错误消息
  String get networkError;
  String get serverError;
  String get validationError;
  String emailValidationError(String email);
  String passwordLengthError(int minLength);
}

// lib/core/i18n/app_localizations_en.dart
class AppLocalizationsEn extends AppLocalizations {
  @override
  String get appName => 'Flutter Template';
  
  @override
  String get ok => 'OK';
  
  @override
  String get cancel => 'Cancel';
  
  @override
  String get login => 'Login';
  
  @override
  String emailValidationError(String email) => 'Invalid email: $email';
  
  @override
  String passwordLengthError(int minLength) => 'Password must be at least $minLength characters';
}

// lib/core/i18n/app_localizations_zh.dart
class AppLocalizationsZh extends AppLocalizations {
  @override
  String get appName => 'Flutter 模板';
  
  @override
  String get ok => '确定';
  
  @override
  String get cancel => '取消';
  
  @override
  String get login => '登录';
  
  @override
  String emailValidationError(String email) => '无效的邮箱地址: $email';
  
  @override
  String passwordLengthError(int minLength) => '密码长度至少为 $minLength 位';
}
```

**关键功能**:
- 多语言文本支持
- 参数化文本处理
- 复数形式处理
- 日期时间格式化
- 数字格式化
- RTL 语言支持

**验证方式**:
- 语言切换正常
- 文本显示正确
- 参数化文本正确
- RTL 布局正确

#### 3.2 动态语言切换
**完成内容**:
```dart
// lib/core/i18n/locale_service.dart
abstract class ILocaleService {
  Future<Locale> getCurrentLocale();
  Future<void> setLocale(Locale locale);
  Stream<Locale> get localeStream;
  List<Locale> getSupportedLocales();
}

// lib/core/i18n/locale_service_impl.dart
class LocaleService implements ILocaleService {
  final IDatabaseService _databaseService;
  final StreamController<Locale> _localeController = StreamController.broadcast();
  
  static const String _localeKey = 'current_locale';
  
  @override
  Stream<Locale> get localeStream => _localeController.stream;
  
  @override
  Future<void> setLocale(Locale locale) async {
    await _databaseService.set(_localeKey, '${locale.languageCode}_${locale.countryCode}');
    _localeController.add(locale);
  }
}

// lib/core/i18n/locale_bloc.dart
class LocaleBloc extends Bloc<LocaleEvent, LocaleState> {
  final ILocaleService localeService;
  
  LocaleBloc({required this.localeService}) : super(LocaleInitial()) {
    on<LocaleLoadRequested>(_onLocaleLoadRequested);
    on<LocaleChanged>(_onLocaleChanged);
  }
}
```

**模块化实现**:
```dart
// 条件注册国际化服务
if (FeatureConfig.instance.isFeatureEnabled(Features.internationalization)) {
  getIt.registerLazySingleton<ILocaleService>(() => LocaleService());
} else {
  getIt.registerLazySingleton<ILocaleService>(() => NoOpLocaleService());
}
```

**验证方式**:
- 语言切换实时生效
- 语言设置持久化
- 系统语言跟随正常
- 不支持语言回退正确

---

### 4. 路由导航系统实现
**参照文档**: `/doc/code_examples/14_navigation/navigation_system.md`

#### 4.1 声明式路由配置
**完成内容**:
```dart
// lib/core/routing/app_router.dart
class AppRouter {
  static final GoRouter _router = GoRouter(
    initialLocation: '/',
    routes: [
      GoRoute(
        path: '/',
        name: 'home',
        builder: (context, state) => const HomePage(),
        routes: [
          GoRoute(
            path: '/profile',
            name: 'profile',
            builder: (context, state) => const ProfilePage(),
          ),
        ],
      ),
      GoRoute(
        path: '/auth',
        name: 'auth',
        builder: (context, state) => const AuthPage(),
        routes: [
          GoRoute(
            path: '/login',
            name: 'login',
            builder: (context, state) => const LoginPage(),
          ),
          GoRoute(
            path: '/register',
            name: 'register',
            builder: (context, state) => const RegisterPage(),
          ),
        ],
      ),
    ],
    redirect: (context, state) {
      return _handleRedirect(context, state);
    },
  );
  
  static GoRouter get router => _router;
  
  static String? _handleRedirect(BuildContext context, GoRouterState state) {
    final authService = GetIt.instance<IAuthService>();
    final isAuthenticated = authService.isAuthenticated;
    
    // 路由守卫逻辑
    if (!isAuthenticated && state.location.startsWith('/profile')) {
      return '/auth/login';
    }
    
    return null;
  }
}

// lib/core/routing/route_guards.dart
class RouteGuards {
  static bool requiresAuth(String route) {
    const protectedRoutes = ['/profile', '/settings', '/admin'];
    return protectedRoutes.any((protected) => route.startsWith(protected));
  }
  
  static bool requiresPermission(String route, String permission) {
    final permissionService = GetIt.instance<IPermissionService>();
    return permissionService.hasPermission(getCurrentUserId(), route, 'access');
  }
}
```

**关键功能**:
- 声明式路由配置
- 嵌套路由支持
- 路由守卫机制
- 深度链接支持
- 路由参数传递
- 路由动画配置

**验证方式**:
- 路由导航正常
- 路由守卫有效
- 深度链接正确
- 路由参数传递正确

#### 4.2 条件路由系统
**完成内容**:
```dart
// lib/core/routing/conditional_router.dart
class ConditionalRouter {
  static List<RouteBase> generateRoutes() {
    final routes = <RouteBase>[];
    
    // 基础路由（始终可用）
    routes.addAll(_getBaseRoutes());
    
    // 条件路由（基于功能配置）
    if (FeatureConfig.instance.isFeatureEnabled(Features.authentication)) {
      routes.addAll(_getAuthRoutes());
    }
    
    if (FeatureConfig.instance.isFeatureEnabled(Features.analytics)) {
      routes.addAll(_getAnalyticsRoutes());
    }
    
    if (FeatureConfig.instance.isFeatureEnabled(Features.pushNotifications)) {
      routes.addAll(_getNotificationRoutes());
    }
    
    return routes;
  }
  
  static List<RouteBase> _getAuthRoutes() {
    return [
      GoRoute(
        path: '/auth',
        builder: (context, state) => const AuthPage(),
        routes: [
          GoRoute(
            path: '/login',
            builder: (context, state) => const LoginPage(),
          ),
          GoRoute(
            path: '/register',
            builder: (context, state) => const RegisterPage(),
          ),
        ],
      ),
    ];
  }
}

// lib/shared/widgets/feature_route_wrapper.dart
class FeatureRouteWrapper extends StatelessWidget {
  final String featureName;
  final Widget child;
  final Widget? fallback;
  
  @override
  Widget build(BuildContext context) {
    if (!FeatureConfig.instance.isFeatureEnabled(featureName)) {
      return fallback ?? FeatureDisabledPage(featureName: featureName);
    }
    
    return child;
  }
}
```

**验证方式**:
- 功能禁用时路由不可访问
- 条件路由生成正确
- 路由回退机制正常
- 路由配置热重载支持

---

### 5. 性能监控系统实现
**参照文档**: `/doc/code_examples/15_performance/performance_monitoring.md`

#### 5.1 性能指标收集
**完成内容**:
```dart
// lib/core/performance/performance_service.dart
abstract class IPerformanceService {
  void recordAppStart();
  void recordPageLoad(String pageName, Duration loadTime);
  void recordNetworkRequest(String url, Duration responseTime, int statusCode);
  void recordMemoryUsage(int memoryUsage);
  void recordFrameMetrics(FrameMetrics metrics);
  Future<PerformanceReport> generateReport();
}

// lib/core/performance/performance_service_impl.dart
class PerformanceService implements IPerformanceService {
  final List<PerformanceMetric> _metrics = [];
  final Stopwatch _appStartStopwatch = Stopwatch();
  
  @override
  void recordAppStart() {
    _appStartStopwatch.start();
    _recordMetric(PerformanceMetric(
      type: MetricType.appStart,
      timestamp: DateTime.now(),
      value: 0,
    ));
  }
  
  @override
  void recordPageLoad(String pageName, Duration loadTime) {
    _recordMetric(PerformanceMetric(
      type: MetricType.pageLoad,
      timestamp: DateTime.now(),
      value: loadTime.inMilliseconds.toDouble(),
      metadata: {'pageName': pageName},
    ));
  }
  
  void _recordMetric(PerformanceMetric metric) {
    _metrics.add(metric);
    
    // 限制内存中的指标数量
    if (_metrics.length > 1000) {
      _metrics.removeRange(0, 500);
    }
  }
}

// lib/core/performance/performance_metric.dart
class PerformanceMetric {
  final MetricType type;
  final DateTime timestamp;
  final double value;
  final Map<String, dynamic>? metadata;
  
  const PerformanceMetric({
    required this.type,
    required this.timestamp,
    required this.value,
    this.metadata,
  });
}

enum MetricType {
  appStart,
  pageLoad,
  networkRequest,
  memoryUsage,
  frameMetrics,
}
```

**关键功能**:
- 应用启动时间监控
- 页面加载性能监控
- 网络请求性能监控
- 内存使用监控
- 帧率性能监控
- 自定义性能指标

**验证方式**:
- 性能指标收集正确
- 性能报告生成准确
- 性能监控开销可接受
- 性能数据可视化正常

#### 5.2 性能优化建议
**完成内容**:
```dart
// lib/core/performance/performance_analyzer.dart
class PerformanceAnalyzer {
  static List<PerformanceRecommendation> analyzeMetrics(List<PerformanceMetric> metrics) {
    final recommendations = <PerformanceRecommendation>[];
    
    // 分析应用启动时间
    final appStartMetrics = metrics.where((m) => m.type == MetricType.appStart).toList();
    if (appStartMetrics.isNotEmpty) {
      final avgStartTime = _calculateAverage(appStartMetrics.map((m) => m.value));
      if (avgStartTime > 3000) { // 3秒
        recommendations.add(PerformanceRecommendation(
          type: RecommendationType.appStart,
          severity: Severity.high,
          message: 'App start time is too slow (${avgStartTime.toInt()}ms)',
          suggestions: [
            'Reduce initialization work in main()',
            'Use lazy loading for non-critical services',
            'Optimize dependency injection setup',
          ],
        ));
      }
    }
    
    // 分析内存使用
    final memoryMetrics = metrics.where((m) => m.type == MetricType.memoryUsage).toList();
    if (memoryMetrics.isNotEmpty) {
      final maxMemory = memoryMetrics.map((m) => m.value).reduce(math.max);
      if (maxMemory > 200 * 1024 * 1024) { // 200MB
        recommendations.add(PerformanceRecommendation(
          type: RecommendationType.memory,
          severity: Severity.medium,
          message: 'High memory usage detected (${(maxMemory / 1024 / 1024).toInt()}MB)',
          suggestions: [
            'Check for memory leaks',
            'Optimize image loading and caching',
            'Use object pooling for frequently created objects',
          ],
        ));
      }
    }
    
    return recommendations;
  }
}
```

**模块化实现**:
```dart
// 条件注册性能监控服务
if (FeatureConfig.instance.isFeatureEnabled(Features.performance)) {
  getIt.registerLazySingleton<IPerformanceService>(() => PerformanceService());
} else {
  getIt.registerLazySingleton<IPerformanceService>(() => NoOpPerformanceService());
}
```

**验证方式**:
- 性能分析准确
- 优化建议有效
- 性能趋势分析正确
- 性能报告可导出

---

### 6. 用户体验优化实现

#### 6.1 加载状态管理
**完成内容**:
```dart
// lib/shared/widgets/loading/loading_overlay.dart
class LoadingOverlay extends StatelessWidget {
  final bool isLoading;
  final Widget child;
  final String? message;
  final Color? backgroundColor;
  
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: backgroundColor ?? Colors.black54,
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CircularProgressIndicator(),
                  if (message != null) ..[
                    const SizedBox(height: 16),
                    Text(
                      message!,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
      ],
    );
  }
}

// lib/shared/widgets/loading/skeleton_loader.dart
class SkeletonLoader extends StatefulWidget {
  final double width;
  final double height;
  final BorderRadius? borderRadius;
  
  @override
  State<SkeletonLoader> createState() => _SkeletonLoaderState();
}

class _SkeletonLoaderState extends State<SkeletonLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat();
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: widget.borderRadius,
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              stops: [
                math.max(0.0, _animation.value - 0.3),
                _animation.value,
                math.min(1.0, _animation.value + 0.3),
              ],
              colors: [
                Colors.grey[300]!,
                Colors.grey[100]!,
                Colors.grey[300]!,
              ],
            ),
          ),
        );
      },
    );
  }
}
```

**验证方式**:
- 加载状态显示正确
- 骨架屏动画流畅
- 加载体验良好
- 加载状态切换自然

#### 6.2 错误状态处理
**完成内容**:
```dart
// lib/shared/widgets/error/error_widget.dart
class AppErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  final Widget? icon;
  final String? retryText;
  
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: DesignTokens.spacingL,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            icon ?? Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ..[
              const SizedBox(height: 24),
              AppButton(
                text: retryText ?? AppLocalizations.of(context).retry,
                onPressed: onRetry,
                type: AppButtonType.primary,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// lib/shared/widgets/error/error_boundary.dart
class ErrorBoundary extends StatefulWidget {
  final Widget child;
  final Widget Function(Object error, StackTrace stackTrace)? errorBuilder;
  final void Function(Object error, StackTrace stackTrace)? onError;
  
  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  Object? _error;
  StackTrace? _stackTrace;
  
  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return widget.errorBuilder?.call(_error!, _stackTrace!) ??
          AppErrorWidget(
            message: 'An unexpected error occurred',
            onRetry: () {
              setState(() {
                _error = null;
                _stackTrace = null;
              });
            },
          );
    }
    
    return widget.child;
  }
}
```

**验证方式**:
- 错误状态显示正确
- 错误恢复机制有效
- 错误边界保护正常
- 用户体验友好

---

## 阶段验收标准

### 功能验收
1. **设计系统**
   - [ ] 设计令牌系统完整
   - [ ] 组件库功能完善
   - [ ] 响应式布局正确
   - [ ] 视觉一致性良好

2. **主题系统**
   - [ ] 多主题切换正常
   - [ ] 动态主题生成正确
   - [ ] 主题持久化有效
   - [ ] 自定义主题支持

3. **国际化系统**
   - [ ] 多语言支持完整
   - [ ] 语言切换实时生效
   - [ ] 参数化文本正确
   - [ ] RTL 语言支持

4. **路由系统**
   - [ ] 声明式路由配置正确
   - [ ] 路由守卫机制有效
   - [ ] 条件路由功能正常
   - [ ] 深度链接支持

5. **性能监控**
   - [ ] 性能指标收集准确
   - [ ] 性能分析有效
   - [ ] 性能报告完整
   - [ ] 优化建议有用

### 质量验收
1. **用户体验**
   - [ ] 界面响应速度 < 100ms
   - [ ] 页面加载时间 < 2秒
   - [ ] 动画流畅度 > 60fps
   - [ ] 错误处理用户友好

2. **可访问性**
   - [ ] 屏幕阅读器支持
   - [ ] 键盘导航支持
   - [ ] 颜色对比度达标
   - [ ] 字体大小可调节

3. **兼容性**
   - [ ] 不同屏幕尺寸适配
   - [ ] 不同平台兼容
   - [ ] 不同语言显示正确
   - [ ] 不同主题显示正确

### 模块化验收
1. **功能模块化**
   - [ ] UI 模块可独立启用/禁用
   - [ ] 模块禁用不影响编译
   - [ ] NoOp 实现正常工作
   - [ ] 模块依赖关系正确

2. **性能影响**
   - [ ] 禁用模块不影响包大小
   - [ ] 启动时间不受影响
   - [ ] 运行时性能良好
   - [ ] 内存使用合理

---

## 风险和注意事项

### 技术风险
1. **性能影响**
   - 风险：复杂 UI 影响性能
   - 缓解：性能监控，优化关键路径
   - 监控：实时性能指标

2. **兼容性问题**
   - 风险：不同平台表现不一致
   - 缓解：充分测试，平台特定优化
   - 监控：多平台测试覆盖

3. **国际化复杂性**
   - 风险：文本翻译质量问题
   - 缓解：专业翻译，本地化测试
   - 监控：用户反馈收集

### 用户体验风险
1. **加载体验**
   - 风险：加载时间过长影响体验
   - 缓解：骨架屏，渐进式加载
   - 监控：加载时间统计

2. **错误处理**
   - 风险：错误信息不友好
   - 缓解：用户友好的错误提示
   - 监控：错误发生率统计

---

## 下一阶段准备

### 为阶段四准备的基础
1. **分析统计基础**：为数据分析提供 UI 支持
2. **推送通知 UI**：为消息推送提供界面
3. **高级功能 UI**：为企业级功能提供界面
4. **管理后台 UI**：为系统管理提供界面

### 技术债务管理
1. **组件优化**：持续优化组件性能
2. **主题完善**：完善主题系统功能
3. **国际化扩展**：支持更多语言
4. **性能调优**：持续优化 UI 性能

---

## 总结

阶段三的成功完成将为应用提供完整的用户界面和用户体验支持。通过实现设计系统、主题管理、国际化、路由导航和性能监控等模块，应用将具备现代化的用户界面和优秀的用户体验。

**关键成功因素**：
1. 严格遵循设计系统规范
2. 确保用户体验一致性
3. 保持高性能和可访问性
4. 建立完善的监控机制

**预期收益**：
1. 现代化的用户界面
2. 优秀的用户体验
3. 完善的国际化支持
4. 强大的性能监控能力