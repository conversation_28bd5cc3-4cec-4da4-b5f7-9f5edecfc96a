好的。遵照您的要求，我将生成一份完整的、可作为团队开发蓝图的技术架构文档。

这份文档将详细阐述架构的每一层、每一个核心模块的功能与使用方式，并提供关键代码示例来阐明核心概念。

-----

## **企业级Flutter应用技术架构说明文档 (TAD)**

  * **文档版本:** 3.0
  * **更新日期:** 2025年7月10日
  * **目标:** 为开发团队提供一个统一的、可扩展、可维护的Flutter应用架构指南，确保代码质量、开发效率和未来的业务适应性。

### 1\. 简介

#### 1.1. 文档目的

本技术架构文档（TAD）旨在定义项目的标准开发框架、分层原则、模块职责和技术选型。所有团队成员都应遵循本文档的规范进行开发，以保证项目架构的统一性和一致性。

#### 1.2. 架构核心目标

  * **关注点分离 (SoC):** UI、业务逻辑和数据源三者严格分离，互不影响。
  * **可测试性:** 所有业务逻辑都应易于进行单元测试。
  * **可扩展性:** 轻松添加新功能，而不影响现有代码。
  * **可维护性:** 清晰的结构和代码规范，降低长期维护成本。
  * **环境适应性:** 一份代码库，通过编译时配置，无缝支持**国内**和**国际**两套服务生态。

### 2\. 高层架构设计

#### 2.1. 分层模型：整洁架构 (Clean Architecture)

我们采用整洁架构作为应用分层的核心指导思想。

```
+-------------------------------------------------------------+
|                      表现层 (Presentation)                    |
|  +-----------------+      +------------------------------+  |
|  |     Widgets     |----->|   State Management (Bloc)    |  |
|  +-----------------+      +------------------------------+  |
+-----------+-----------------------------------------------+
            | (调用/依赖)
+-----------v-----------------------------------------------+
|                        领域层 (Domain)                      |
|  +-----------------+      +------------------------------+  |
|  |     Entities    |      |    Use Cases (Interactors)   |  |
|  +-----------------+      +-----------+------------------+  |
|                                       ^ (接口)              |
|  +------------------------------------+-----------------+  |
|  |               Repository (接口)                      |  |
|  +------------------------------------------------------+  |
+-------------------+------------------+--------------------+
                    ^ (实现)             ^ (实现)
+-------------------+------------------+--------------------+
|                        数据层 (Data)                        |
|  +------------------------------------------------------+  |
|  |              Repository (实现)                       |  |
|  +-----------------------+------------------------------+  |
|                          |                              |
|  +-----------------------v------------------------------+  |
|  |         Remote Data Source (API, Network)           |  |
|  +------------------------------------------------------+  |
|  |         Local Data Source (DB, Cache, Prefs)        |  |
|  +------------------------------------------------------+  |
+-------------------------------------------------------------+
```

  * **领域层 (Domain):** 应用的核心，包含纯粹的业务逻辑（Use Cases）和业务对象（Entities）。它不依赖任何其他层，是应用的“心脏”。
  * **数据层 (Data):** 负责所有数据的来源和管理。它实现了领域层定义的Repository接口，并处理API请求、数据库操作、缓存等。
  * **表现层 (Presentation):** 负责UI的显示和用户交互。它通过状态管理（Bloc）调用领域层的Use Cases，并根据返回的数据或状态更新UI。

#### 2.2. 项目结构：Monorepo

采用Monorepo进行代码管理，由`melos`工具进行协调。

```bash
/project_root
├── melos.yaml
├── apps
│   └── main_app            # 主应用工程
└── packages
    ├── features            # 功能模块
    │   └── feature_login/  # 登录模块
    │       ├── lib
    │       │   ├── data/
    │       │   ├── domain/
    │       │   └── presentation/
    │       └── pubspec.yaml
    ├── core                # 核心能力模块
    │   ├── core_network/
    │   └── core_analytics/
    └── shared              # 共享代码模块
        ├── ui_kit/
        └── shared_utils/
```

### 3\. 核心机制：环境与服务切换

这是本架构的关键特性，用于适配国内外不同的服务生态。

  * **策略:** **编译时Flavor** + **运行时依赖注入**。

  * **功能:** 在编译应用时指定一个`Flavor`（如`china`或`global`），应用启动时，依赖注入容器会根据这个`Flavor`注册对应服务的具体实现。

  * **使用方式:**

    1.  **定义Flavor:** 在Android的`build.gradle`和iOS的`Xcode Schemes`中配置`china`和`global`两种Flavors。
    2.  **编译:** `flutter run --flavor china`。
    3.  **在`main.dart`中配置DI:**

    <!-- end list -->

    ```dart
    // file: apps/main_app/lib/main.dart
    import 'package:get_it/get_it.dart';
    import 'package:core_analytics/analytics.dart'; // 导入抽象服务和实现

    final getIt = GetIt.instance;

    // 定义一个环境变量来获取Flavor
    const String F_FLAVOR = String.fromEnvironment('F_FLAVOR');

    void configureDependencies() {
      // 根据Flavor注册不同的服务实现
      if (F_FLAVOR == 'china') {
        // 注册国内服务
        getIt.registerLazySingleton<AnalyticsService>(() => UmengAnalyticsService());
        getIt.registerLazySingleton<PushService>(() => JPushService());
        // ... 其他国内服务
      } else { // 'global' 或默认
        // 注册国际服务
        getIt.registerLazySingleton<AnalyticsService>(() => FirebaseAnalyticsService());
        getIt.registerLazySingleton<PushService>(() => FCMPushService());
        // ... 其他国际服务
      }

      // 注册通用服务
      getIt.registerLazySingleton<NetworkClient>(() => DioClient());
    }

    void main() {
      // 在App运行前配置依赖
      configureDependencies();
      runApp(const MyApp());
    }
    ```

### 4\. 表现层 (Presentation Layer) 详解

#### 4.1. 状态管理: `bloc`

  * **功能:** 管理UI状态，分离业务逻辑与UI渲染。

  * **使用方式:**

      * **Event:** 用户操作或页面生命周期事件，是输入。
      * **Bloc:** 接收Event，调用Domain层的Use Case，处理业务逻辑，并产生新的State。
      * **State:** UI的当前状态，是输出。UI层通过`BlocBuilder`监听State变化并重绘。

    <!-- end list -->

    ```dart
    // 1. Event
    abstract class AuthEvent {}
    class LoginButtonPressed extends AuthEvent {
      final String username;
      LoginButtonPressed(this.username);
    }

    // 2. State
    abstract class AuthState {}
    class AuthInitial extends AuthState {}
    class AuthLoading extends AuthState {}
    class AuthSuccess extends AuthState {}
    class AuthFailure extends AuthState {
      final String error;
      AuthFailure(this.error);
    }

    // 3. Bloc
    class AuthBloc extends Bloc<AuthEvent, AuthState> {
      final LoginUseCase loginUseCase;

      AuthBloc(this.loginUseCase) : super(AuthInitial()) {
        on<LoginButtonPressed>((event, emit) async {
          emit(AuthLoading());
          final result = await loginUseCase.execute(event.username);
          result.fold(
            (failure) => emit(AuthFailure(failure.message)),
            (success) => emit(AuthSuccess()),
          );
        });
      }
    }
    ```

#### 4.2. UI设计系统: `ui_kit`

  * **功能:** 提供一套统一、可复用、与业务逻辑无关的UI组件和规范。是实现UI快速迭代和风格切换的关键。
  * **内容:**
      * `AppColors`: 统一的颜色定义。
      * `AppTextStyles`: 统一的文本样式。
      * `AppSpacing`: 统一的间距、边距定义。
      * `Widgets/`: 包含如`PrimaryButton`, `CustomAppBar`, `LoadingIndicator`等无状态或自定义状态的通用组件。
      * `Theme/`: 使用`ThemeExtension`定义完整的、可扩展的主题。

### 5\. 领域层 (Domain Layer) 详解

#### 5.1. Use Cases (用例)

  * **功能:** 封装单一、具体的业务操作。

  * **使用方式:** 每个Use Case类通常只有一个公共方法`execute()`。

    ```dart
    // file: packages/features/feature_login/lib/src/domain/usecases/login_use_case.dart
    class LoginUseCase {
      final AuthRepository repository;

      LoginUseCase(this.repository);

      // Either<Failure, Success> 用于处理成功或失败的结果
      Future<Either<Failure, bool>> execute(String username) {
        return repository.login(username);
      }
    }
    ```

#### 5.2. Repository (接口)

  * **功能:** 定义数据操作的契约（合同），领域层只依赖这个接口，而不关心其具体实现。

  * **使用方式:**

    ```dart
    // file: packages/features/feature_login/lib/src/domain/repositories/auth_repository.dart
    abstract class AuthRepository {
      Future<Either<Failure, bool>> login(String username);
      Future<void> logout();
    }
    ```

### 6\. 数据层 (Data Layer) 详解

#### 6.1. Repository (实现)

  * **功能:** 实现Domain层定义的Repository接口，负责协调不同的数据源。

  * **使用方式:**

    ```dart
    // file: packages/features/feature_login/lib/src/data/repositories/auth_repository_impl.dart
    class AuthRepositoryImpl implements AuthRepository {
      final AuthRemoteDataSource remoteDataSource;
      final AuthLocalDataSource localDataSource;

      AuthRepositoryImpl({required this.remoteDataSource, required this.localDataSource});

      @override
      Future<Either<Failure, bool>> login(String username) async {
        try {
          final token = await remoteDataSource.login(username);
          await localDataSource.saveToken(token);
          return Right(true);
        } on ServerException catch (e) {
          return Left(ServerFailure(e.message));
        }
      }
      // ...
    }
    ```

### 7\. 详细模块规格 (Module Specifications)

#### 7.1. 网络 (Networking)

  * **库:** `dio`
  * **功能:** 提供强大的HTTP客户端功能，支持拦截器、表单数据、文件上传下载、取消请求等。
  * **集成:** 在`core_network`模块中创建`DioClient`，封装全局配置，如`baseUrl`、`timeout`和拦截器。
  * **使用方式:** 通过拦截器统一处理请求头（如`Authorization` Token）、日志打印和错误处理。其他Data Source通过注入`DioClient`来发起网络请求。

#### 7.2. 路由 (Routing)

  * **库:** `go_router`
  * **功能:** 管理应用内所有页面的导航，支持深链接（Deep Linking）、命名路由、参数传递和路由守卫（Redirect）。
  * **集成:** 在主应用`main_app`中配置全局路由表。路由守卫可用于实现登录拦截和权限验证。

#### 7.3. 持久化 (Persistence)

  * **库:** `drift` (用于复杂关系型数据), `shared_preferences` (用于简单键值对)
  * **功能:** `drift`（原Moor）是一个基于SQLite的响应式持久化库，提供类型安全的SQL查询。`shared_preferences`用于存储少量简单数据，如用户设置。
  * **集成:** 在`core_database`模块中定义数据库结构和DAOs（数据访问对象），并作为`LocalDataSource`供Repository使用。

#### 7.4. 原生通信 (Native Interoperability)

  * **首选方案: `pigeon`**
      * **功能:** 类型安全的代码生成器，用于Flutter与原生平台(iOS/Android)之间频繁、结构化的方法调用。
      * **用途:** 登录、支付、广告、地图等需要调用原生SDK的场景。
      * **使用方式:** 定义一个`.dart`文件作为接口描述，`pigeon`会为Dart、Java/Kotlin、Objective-C/Swift生成所有通信样板代码。
  * **高性能方案: `dart:ffi`**
      * **功能:** 允许Dart直接调用C/C++动态库。
      * **用途:** 集成高性能的C/C++算法库（如加解密、音视频处理）。

#### 7.5. WebView

  * **库:** `flutter_inappwebview`
  * **功能:** 提供功能强大且高度可定制的WebView组件。
  * **集成:** 封装成一个独立的Widget，通过`addJavaScriptChannel`与WebView内的JS进行安全通信，可用于加载复杂的H5活动页。

#### 7.6. 认证、支付、广告模块

  * **通用架构:** 这三个模块都采用 **Pigeon + 原生SDK** 的方式实现。
  * **功能:**
      * **认证:** 在原生端集成微信、QQ、Apple登录SDK，通过Pigeon将登录结果（Token/用户信息）返回给Flutter。
      * **支付:** 在原生端集成支付宝、微信支付SDK，Flutter端通过Pigeon发起支付请求，并在原生端处理支付回调。
      * **广告:** 在原生端集成穿山甲(Pangle)、优量汇SDK，通过Pigeon在Flutter中控制广告的加载和展示。
  * **服务切换:** 这些模块的Repository实现可以为空（如果某个Flavor不支持），或者根据`Flavor`注入不同的实现（例如，`ApplePayPaymentServiceImpl` vs `AliPayPaymentServiceImpl`）。

#### 7.7. 推送、硬件、相机等

  * **通用架构:** 采用**封装插件**的模式。将社区的成熟插件封装在Data层的Repository实现中。
  * **库选型:**
      * **推送:** `jpush_flutter` (国内), `firebase_messaging` (国际)。国内版必须集成厂商通道。
      * **相机:** `camera` (通用), `mobile_scanner` (扫码优化)。
      * **传感器:** `sensors_plus`。
      * **蓝牙:** `flutter_blue_plus`。
  * **使用方式:** 表现层通过领域层的Use Case调用，完全与具体插件解耦。

### 8\. 测试策略

  * **单元测试 (Unit Tests):**
      * **目标:** 测试Domain层的所有Use Cases和Data层的所有Repositories。
      * **工具:** `test`, `mocktail` (或 `mockito`)。
  * **组件测试 (Widget Tests):**
      * **目标:** 测试表现层的Blos和独立的Widgets。
      * **工具:** `flutter_test`, `bloc_test`, `mocktail`。
  * **集成测试 (Integration Tests):**
      * **目标:** 测试完整的用户流程（如登录流程、支付流程）。
      * **工具:** `integration_test`。

### 9\. 附录

#### 9.1. 核心库清单

| 功能 | 推荐库 | 备注 |
| :--- | :--- | :--- |
| 状态管理 | `bloc`, `flutter_bloc` | 严格、可测试 |
| 依赖注入 | `get_it`, `injectable` | 配合代码生成 |
| HTTP请求 | `dio` | 功能强大的网络库 |
| 路由 | `go_router` | 官方推荐，功能全面 |
| 数据库 | `drift` | 类型安全的响应式SQLite库 |
| JSON序列化| `freezed`, `json_serializable` | 不可变模型与序列化 |
| 原生通信 | `pigeon` | 类型安全的方法调用 |
| 值比较 | `equatable` | 简化Equatable实现 |

#### 9.2. `melos.yaml` 示例

```yaml
name: my_project_monorepo

packages:
  - apps/**
  - packages/**

scripts:
  # 获取所有依赖
  bootstrap: melos bootstrap

  # 运行所有测试
  test:all: melos exec -- "flutter test --coverage"

  # 运行所有代码生成器
  build:all: melos exec -- "flutter pub run build_runner build --delete-conflicting-outputs"

  # 运行所有代码分析
  analyze:all: melos exec -- "flutter analyze"

  # 清理所有工程
  clean:all: melos exec -- "flutter clean"

```