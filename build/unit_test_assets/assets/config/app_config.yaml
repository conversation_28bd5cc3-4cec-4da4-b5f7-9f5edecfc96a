# 企业级Flutter应用配置文件
# 基础配置 - 所有环境通用

app:
  name: "Flutter Enterprise App"
  version: "1.0.0"
  build_number: 1
  
# 功能模块配置
features:
  # 核心功能模块
  authentication: true
  authorization: true
  
  # UI/UX 模块
  internationalization: true
  theming: true
  
  # 高级功能模块
  analytics: false
  performance_monitoring: false
  push_notifications: false
  
  # 开发工具
  dev_tools: false
  debug_features: false

# 功能依赖关系定义
dependencies:
  authorization:
    requires: [authentication]
  analytics:
    requires: [authentication]
  push_notifications:
    requires: [authentication]

# 默认配置
defaults:
  theme: "system"
  language: "zh_CN"
  log_level: "info"
