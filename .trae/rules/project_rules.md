# Flutter 企业级应用项目开发规则

## 1. 总体原则

### 1.1 开发目标
- **高效开发**：遵循统一规范，减少决策时间，提高开发效率
- **代码质量**：严格按照架构设计和最佳实践编写代码
- **可维护性**：确保代码结构清晰，易于理解和维护
- **一致性**：所有模块遵循相同的开发标准和规范

### 1.2 强制性要求
- 所有开发必须严格参照 `/doc/code_examples/` 下的代码示例
- 所有架构设计必须遵循 `/doc/` 下的技术文档
- 禁止偏离既定架构模式和设计原则
- 代码提交前必须通过质量检查

---

## 2. 架构规范

### 2.1 Clean Architecture 三层架构
**强制参照**：`/doc/code_examples/02_clean_architecture/`

- **数据层 (Data Layer)**
  - 参照：`data_layer.md`
  - Repository 实现必须遵循接口隔离原则
  - 数据源抽象必须支持本地和远程切换
  - 模型转换必须使用 Mapper 模式

- **领域层 (Domain Layer)**
  - 参照：`domain_layer.md`
  - 业务实体必须纯净，不依赖外部框架
  - UseCase 必须单一职责，可测试
  - Repository 接口定义必须清晰明确

- **表现层 (Presentation Layer)**
  - 参照：`presentation_layer.md`
  - 必须使用 BLoC 状态管理
  - UI 组件必须无状态或最小状态
  - 页面组件必须遵循组合模式

### 2.2 项目结构
**强制参照**：`/doc/code_examples/01_project_structure/project_structure.md`

```
lib/
├── core/           # 核心基础设施
│   ├── config/     # 配置管理（含功能配置）
│   ├── di/         # 依赖注入（含条件注入）
│   ├── modules/    # 模块注册
│   └── routing/    # 路由管理（含功能守卫）
├── features/       # 功能模块（可选）
│   ├── auth/       # 认证模块
│   ├── authorization/ # 权限管理模块
│   ├── internationalization/ # 国际化模块
│   ├── analytics/  # 分析统计模块
│   ├── performance/ # 性能监控模块
│   └── push_notifications/ # 推送通知模块
├── shared/         # 共享组件
│   └── widgets/    # 通用组件（含功能包装器）
└── main.dart       # 应用入口
```

---

## 3. 模块开发规范

### 3.1 依赖注入
**强制参照**：`/doc/code_examples/03_dependency_injection/di_configuration.md`

- 必须使用 GetIt + Injectable
- 所有服务必须注册为单例或工厂
- 接口和实现必须分离
- 测试时必须支持 Mock 注入

### 3.2 状态管理
**强制参照**：`/doc/code_examples/04_state_management/bloc_implementation.md`

- 必须使用 BLoC 模式
- Event 命名：`[Feature][Action]Event`
- State 命名：`[Feature]State`
- BLoC 命名：`[Feature]Bloc`
- 必须实现状态的 `copyWith` 方法

### 3.3 网络层
**强制参照**：`/doc/code_examples/05_network_layer/network_implementation.md`

- 必须使用统一的 HTTP 客户端
- 必须实现请求/响应拦截器
- 必须实现统一错误处理
- 必须支持重试机制
- API 接口必须使用 Repository 模式封装

### 3.4 数据持久化
**强制参照**：
- 基础：`/doc/code_examples/06_data_persistence/persistence_implementation.md`
- 高级：`/doc/code_examples/13_data_persistence/data_persistence_implementation.md`

- 必须使用统一的存储抽象接口
- 支持 SQLite、Hive、SharedPreferences
- 必须实现数据同步和冲突解决
- 必须支持离线模式

### 3.5 多环境配置
**强制参照**：`/doc/code_examples/07_multi_environment/environment_configuration.md`

- 必须支持 dev、test、prod 环境
- 配置文件必须使用 Flavor 机制
- 敏感信息必须使用环境变量
- 构建脚本必须自动化

### 3.6 错误处理
**强制参照**：`/doc/code_examples/08_error_handling/error_handling_implementation.md`

- 必须实现全局异常捕获
- 错误必须分类处理（网络、业务、系统）
- 必须提供用户友好的错误提示
- 必须记录详细的错误日志

### 3.7 测试框架
**强制参照**：
- 基础：`/doc/code_examples/09_testing/testing_framework.md`
- 质量：`/doc/code_examples/14_testing_quality/testing_quality_implementation.md`

- 单元测试覆盖率必须 > 80%
- 必须编写 Widget 测试
- 关键流程必须有集成测试
- 必须使用 Mock 进行隔离测试

### 3.8 性能优化
**强制参照**：`/doc/code_examples/10_performance/performance_optimization.md`

- 必须监控关键性能指标
- 必须优化内存使用
- 必须优化渲染性能
- 必须实现懒加载和缓存

### 3.9 安全实现
**强制参照**：`/doc/code_examples/11_security/security_implementation.md`

- 必须实现身份认证和授权
- 敏感数据必须加密存储
- 网络传输必须使用 HTTPS
- 必须实现安全的本地存储

### 3.10 国际化
**强制参照**：
- 基础：`/doc/code_examples/12_internationalization/internationalization_implementation.md`
- 完整：`/doc/code_examples/16_internationalization_localization/internationalization_localization_implementation.md`

- 必须支持多语言切换
- 必须支持 RTL 布局
- 必须实现本地化资源管理
- 必须支持动态语言加载

### 3.11 模块化架构
**强制参照**：`/doc/code_examples/17_modular_architecture/modular_architecture_implementation.md`

- 必须支持功能模块的启用/禁用配置
- 必须实现条件依赖注入
- 必须提供NoOp空实现服务
- 必须实现功能路由守卫
- 必须支持模块代码生成
- 必须实现零侵入性设计

### 3.12 部署运维
**强制参照**：`/doc/code_examples/15_deployment_devops/deployment_devops_implementation.md`

- 必须实现 CI/CD 流水线
- 必须支持容器化部署
- 必须实现监控告警
- 必须有备份恢复方案
- 必须支持功能配置的构建时优化

---

## 4. 代码规范

### 4.1 命名规范
- **文件名**：`snake_case.dart`
- **类名**：`PascalCase`
- **变量/方法**：`camelCase`
- **常量**：`UPPER_SNAKE_CASE`
- **私有成员**：以 `_` 开头
- **NoOp实现**：以 `NoOp` 开头，如 `NoOpAuthService`
- **功能配置**：以 `Feature` 开头，如 `FeatureConfig`
- **功能包装器**：以 `Feature` 结尾，如 `AuthFeature`

### 4.2 目录规范
- `lib/` - 主要源代码
- `test/` - 单元测试
- `integration_test/` - 集成测试
- `assets/` - 资源文件
- `tool/` - 开发工具（含功能生成器）
- `scripts/` - 构建脚本（含功能构建）
- `docs/` - 项目文档

### 4.3 代码风格
- 遵循 Dart 官方代码风格
- 使用有意义的变量和方法名
- 添加必要的注释和文档
- 保持方法简洁，单一职责
- 使用 `dart format` 格式化代码
- NoOp服务必须提供完整的接口实现
- 功能配置必须有明确的默认值
- 条件依赖注入必须有清晰的注释说明

### 4.4 注释规范
- 公共 API 必须有文档注释
- 复杂逻辑必须有行内注释
- 使用中文注释说明业务逻辑
- 使用英文注释说明技术实现
- 可选模块必须标注功能依赖关系
- NoOp实现必须说明空实现的原因和行为
- 功能配置必须说明配置项的影响范围

**模块化注释示例**：
```dart
/// 认证服务接口
/// 
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: FEATURE_AUTHENTICATION
abstract class IAuthService {
  /// 用户登录
  Future<AuthResult> login(String username, String password);
}

/// NoOp认证服务实现
/// 
/// 当authentication模块禁用时使用的空实现
/// 所有认证操作返回失败状态，不执行实际认证逻辑
class NoOpAuthService implements IAuthService {
  @override
  Future<AuthResult> login(String username, String password) async {
    // 返回认证失败，功能未启用
    return AuthResult.failure('Authentication feature is disabled');
  }
}
```

---

## 5. 文档参照

### 5.1 架构设计文档
**必读文档**：`/doc/`

- `企业级Flutter应用统一架构方案 (v4.0).md` - 最新架构设计
- `flutter-enterprise-architecture-guide.md` - 架构指导手册
- `flutter-performance-optimization-guide.md` - 性能优化指南

### 5.2 代码示例文档
**严格参照**：`/doc/code_examples/README.md`

所有开发必须严格按照代码示例进行：
- 架构基础模块
- 状态和数据管理模块
- 环境和配置模块
- 质量保证模块
- 安全和国际化模块
- 部署和运维模块

---

## 6. 开发流程

### 6.1 功能开发流程
1. **需求分析**：明确功能需求和技术要求
2. **模块规划**：确定功能模块的可选性和依赖关系
3. **架构设计**：参照架构文档设计模块结构
4. **配置设计**：定义功能配置项和默认值
5. **代码实现**：严格按照代码示例实现
6. **NoOp实现**：为可选模块创建空实现服务
7. **单元测试**：编写完整的测试用例（包含功能组合测试）
8. **集成测试**：验证模块间协作
9. **配置验证**：验证功能配置的正确性
10. **代码审查**：确保符合规范
11. **性能测试**：验证性能指标
12. **文档更新**：更新相关文档

### 6.2 模块化开发流程

**新增可选模块流程**：
1. 在 `app_config.yaml` 中添加功能配置
2. 创建模块接口和实现类
3. 创建对应的NoOp空实现
4. 在 `FeatureConfig` 中注册功能常量
5. 配置条件依赖注入
6. 添加路由守卫（如需要）
7. 创建功能包装器组件（如需要）
8. 编写功能组合测试
9. 更新文档和示例

**模块依赖管理**：
- 明确定义模块间的依赖关系
- 避免循环依赖
- 使用接口隔离原则
- 提供依赖关系图文档

### 6.3 质量检查清单
- [ ] 架构设计符合 Clean Architecture
- [ ] 代码实现参照标准示例
- [ ] 命名规范符合要求
- [ ] 单元测试覆盖率 > 80%
- [ ] 性能指标达标
- [ ] 安全检查通过
- [ ] 功能配置正确设置
- [ ] 模块依赖关系验证
- [ ] NoOp服务实现完整
- [ ] 条件依赖注入配置正确
- [ ] 路由守卫实现正确
- [ ] 功能包装器使用恰当
- [ ] 模块组合测试通过
- [ ] 文档更新完整

### 6.4 代码提交规范
- 提交信息格式：`[type]: description`
- type 类型：`feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`, `config`, `module`
- 每次提交必须通过所有测试
- 重大变更必须更新文档
- 模块化相关变更必须包含配置说明

**模块化相关提交示例**：
```
feat(auth): add optional authentication module

- Add IAuthService interface and implementation
- Add NoOpAuthService for disabled state
- Add authentication feature configuration
- Add conditional dependency injection
- Add route guards for protected pages

Closes #123
```

---

## 7. 工具和环境

### 7.1 必需工具
- **IDE**：VS Code 或 Android Studio
- **版本控制**：Git
- **包管理**：pub
- **代码格式化**：dart format
- **静态分析**：dart analyze
- **测试工具**：flutter test

### 7.2 推荐插件
- Flutter
- Dart
- Bloc
- GitLens
- Error Lens

### 7.3 CI/CD 工具
- GitHub Actions / GitLab CI
- Docker
- Kubernetes
- Firebase

---

## 8. 性能和质量标准

### 8.1 性能指标
- 应用启动时间 < 3秒
- 页面切换时间 < 500ms
- 内存使用 < 200MB
- CPU 使用率 < 50%
- 网络请求超时 < 10秒
- 功能模块加载时间 < 1秒
- 不同配置下性能差异 < 20%

### 8.2 质量指标
- 代码覆盖率 > 80%
- 静态分析无错误
- 安全扫描通过
- 性能测试通过
- 用户体验评分 > 4.0
- 功能配置测试覆盖率 > 90%
- 模块依赖关系验证通过

### 8.3 模块化质量标准

**配置管理质量**：
- 所有功能配置必须有明确的默认值
- 配置变更必须向后兼容
- 配置文档必须实时更新
- 配置验证必须在构建时执行

**NoOp实现质量**：
- NoOp服务必须实现完整的接口
- NoOp行为必须可预测和一致
- NoOp实现必须有详细的文档说明
- NoOp服务不能影响应用稳定性

**依赖注入质量**：
- 条件注入逻辑必须清晰明确
- 依赖关系必须在编译时验证
- 注入配置必须支持测试环境
- 依赖图必须避免循环依赖

---

## 9. 异常处理

### 9.1 开发异常
- 遇到架构问题：参考架构文档或咨询架构师
- 遇到技术难题：查阅代码示例或技术文档
- 遇到性能问题：参考性能优化指南

### 9.2 紧急情况
- 生产环境问题：立即回滚，分析原因
- 安全漏洞：立即修复，更新安全策略
- 性能严重下降：启动性能优化流程

---

## 10. 持续改进

### 10.1 定期评估
- 每月评估开发效率
- 每季度评估架构适应性
- 每半年评估技术栈更新

### 10.2 知识更新
- 关注 Flutter 官方更新
- 学习最新最佳实践
- 参与技术社区交流

### 10.3 文档维护
- 及时更新代码示例
- 完善架构文档
- 记录最佳实践

---

## 11. 总结

本规则文档旨在确保 Flutter 企业级应用开发的：
- **高效性**：通过统一规范减少决策时间
- **准确性**：通过严格参照避免架构偏离
- **质量性**：通过完整流程保证代码质量
- **可维护性**：通过清晰结构便于后续维护

**核心原则**：严格参照 `/doc/code_examples/` 代码示例，遵循 `/doc/` 架构文档，确保开发效率最大化和准确性最高化。